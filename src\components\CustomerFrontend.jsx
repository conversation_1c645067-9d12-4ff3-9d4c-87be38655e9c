import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Button, Modal } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import Login from './Login';
import Register from './Register';
import '../styles/CustomerFrontend.css';

const CustomerFrontend = () => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState('login'); // 'login' or 'register'
  const { currentUser, logout } = useAuth();

  const apps = [
    { name: 'Mychat', icon: '💬', color: '#4CAF50', description: 'Connect with friends' },
    { name: 'Mygo', icon: '🚗', color: '#2196F3', description: 'Transportation services' },
    { name: 'Myneedy', icon: '🛒', color: '#FF9800', description: 'Shopping & delivery' },
    { name: 'My<PERSON>ry', icon: '📔', color: '#9C27B0', description: 'Personal journal' },
    { name: 'Myjoy', icon: '🎮', color: '#F44336', description: 'Entertainment & games' },
    { name: 'Mymedia', icon: '📺', color: '#E91E63', description: 'Media & streaming' },
    { name: 'Myunions', icon: '👥', color: '#607D8B', description: 'Community groups' },
    { name: 'Myshop', icon: '🛍️', color: '#795548', description: 'Online marketplace' }
  ];

  const handleAppClick = (appName) => {
    if (!currentUser) {
      // Show login modal if not authenticated
      setAuthMode('login');
      setShowAuthModal(true);
    } else {
      // Handle app navigation for authenticated users
      console.log(`Opening ${appName} for user:`, currentUser.username);
      // In a real app, this would navigate to the specific app
    }
  };

  const handleLoginClick = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const handleRegisterClick = () => {
    setAuthMode('register');
    setShowAuthModal(true);
  };

  const handleAuthSuccess = () => {
    setShowAuthModal(false);
  };

  const switchAuthMode = () => {
    setAuthMode(authMode === 'login' ? 'register' : 'login');
  };

  return (
    <div className="customer-frontend">
      {/* Top Navigation Bar */}
      <div className="top-nav">
        <Container fluid>
          <Row className="align-items-center py-3">
            <Col xs={3}>
              <div className="logo-section">
                <span className="logo-icon">🏢</span>
                <span className="logo-text">MyGroup</span>
              </div>
            </Col>
            <Col xs={6}>
              <div className="search-bar">
                <input 
                  type="text" 
                  placeholder="Search apps, services..." 
                  className="search-input"
                />
                <button className="search-btn">🔍</button>
              </div>
            </Col>
            <Col xs={3}>
              <div className="auth-section">
                {currentUser ? (
                  <div className="user-menu">
                    <span className="welcome-text">Hi, {currentUser.username}</span>
                    <Button 
                      variant="outline-secondary" 
                      size="sm" 
                      onClick={logout}
                      className="logout-btn"
                    >
                      Logout
                    </Button>
                  </div>
                ) : (
                  <div className="auth-buttons">
                    <Button 
                      variant="outline-primary" 
                      size="sm" 
                      onClick={handleLoginClick}
                      className="me-2"
                    >
                      Login
                    </Button>
                    <Button 
                      variant="primary" 
                      size="sm" 
                      onClick={handleRegisterClick}
                    >
                      Register
                    </Button>
                  </div>
                )}
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Hero Section */}
      <div className="hero-section">
        <Container>
          <Row className="text-center">
            <Col>
              <h1 className="hero-title">Welcome to MyGroup</h1>
              <p className="hero-subtitle">
                Your gateway to a world of connected services and applications
              </p>
              {!currentUser && (
                <div className="hero-cta">
                  <Button 
                    variant="primary" 
                    size="lg" 
                    onClick={handleRegisterClick}
                    className="me-3"
                  >
                    Get Started
                  </Button>
                  <Button 
                    variant="outline-primary" 
                    size="lg" 
                    onClick={handleLoginClick}
                  >
                    Sign In
                  </Button>
                </div>
              )}
            </Col>
          </Row>
        </Container>
      </div>

      {/* Apps Grid */}
      <div className="apps-section">
        <Container>
          <Row>
            <Col>
              <h2 className="section-title">Our Applications</h2>
              <p className="section-subtitle">
                Discover our suite of integrated applications designed for your daily needs
              </p>
            </Col>
          </Row>
          <Row>
            {apps.map((app, index) => (
              <Col key={index} xs={6} md={4} lg={3} className="mb-4">
                <div 
                  className="app-card"
                  onClick={() => handleAppClick(app.name)}
                  style={{ borderColor: app.color }}
                >
                  <div className="app-icon" style={{ color: app.color }}>
                    {app.icon}
                  </div>
                  <h5 className="app-name">{app.name}</h5>
                  <p className="app-description">{app.description}</p>
                  <div className="app-status">
                    {currentUser ? (
                      <span className="status-available">Available</span>
                    ) : (
                      <span className="status-login">Login Required</span>
                    )}
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </div>

      {/* Footer */}
      <div className="footer-section">
        <Container>
          <Row>
            <Col md={6}>
              <h5>MyGroup</h5>
              <p>Connecting people through innovative applications and services.</p>
            </Col>
            <Col md={3}>
              <h6>Quick Links</h6>
              <ul className="footer-links">
                <li><a href="/about">About Us</a></li>
                <li><a href="/contact">Contact</a></li>
                <li><a href="/support">Support</a></li>
              </ul>
            </Col>
            <Col md={3}>
              <h6>Business</h6>
              <ul className="footer-links">
                <li><a href="/partner">Partner Portal</a></li>
                <li><a href="/login">Backend Login</a></li>
                <li><a href="/careers">Careers</a></li>
              </ul>
            </Col>
          </Row>
          <hr />
          <Row>
            <Col className="text-center">
              <small>&copy; 2024 MyGroup. All rights reserved.</small>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Authentication Modal */}
      <Modal 
        show={showAuthModal} 
        onHide={() => setShowAuthModal(false)}
        size="lg"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            {authMode === 'login' ? 'Sign In to MyGroup' : 'Join MyGroup'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {authMode === 'login' ? (
            <Login onLoginSuccess={handleAuthSuccess} />
          ) : (
            <Register onRegistrationComplete={handleAuthSuccess} />
          )}
        </Modal.Body>
        <Modal.Footer>
          <div className="auth-switch">
            {authMode === 'login' ? (
              <span>
                Don't have an account?{' '}
                <Button variant="link" onClick={switchAuthMode} className="p-0">
                  Register here
                </Button>
              </span>
            ) : (
              <span>
                Already have an account?{' '}
                <Button variant="link" onClick={switchAuthMode} className="p-0">
                  Sign in here
                </Button>
              </span>
            )}
          </div>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default CustomerFrontend;
