import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Form, 
  Row, 
  <PERSON>, 
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'react-bootstrap';
import { FaUser, FaEdit, FaSignOutAlt, FaCog } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import './ProfileModal.css';

const ProfileModal = ({ show, onHide }) => {
  const { user, logout, getProfile, updateProfile, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('view');
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [editData, setEditData] = useState({
    first_name: '',
    last_name: '',
    middle_name: '',
    display_name: '',
    gender: '',
    martial_status: '',
    nationality: '',
    date_of_birth: '',
    photo: ''
  });

  useEffect(() => {
    if (show && isAuthenticated) {
      fetchProfile();
    }
  }, [show, isAuthenticated]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const profileData = await getProfile();
      setProfile(profileData);
      
      // Populate edit form
      setEditData({
        first_name: profileData.first_name || '',
        last_name: profileData.last_name || '',
        middle_name: profileData.middle_name || '',
        display_name: profileData.display_name || '',
        gender: profileData.gender || '',
        martial_status: profileData.martial_status || '',
        nationality: profileData.nationality || '',
        date_of_birth: profileData.date_of_birth ? profileData.date_of_birth.split('T')[0] : '',
        photo: profileData.photo || ''
      });
    } catch (error) {
      setError('Failed to load profile');
      console.error('Profile fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Auto-generate display name
    if (name === 'first_name' || name === 'last_name') {
      const firstName = name === 'first_name' ? value : editData.first_name;
      const lastName = name === 'last_name' ? value : editData.last_name;
      setEditData(prev => ({
        ...prev,
        display_name: `${firstName} ${lastName}`.trim()
      }));
    }
  };

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    
    try {
      setUpdating(true);
      setError('');
      setSuccess('');
      
      await updateProfile(editData);
      setSuccess('Profile updated successfully!');
      setActiveTab('view');
      await fetchProfile(); // Refresh profile data
    } catch (error) {
      setError(error.message || 'Failed to update profile');
    } finally {
      setUpdating(false);
    }
  };

  const handleLogout = () => {
    logout();
    onHide();
  };

  const handleClose = () => {
    setActiveTab('view');
    setError('');
    setSuccess('');
    onHide();
  };

  if (!isAuthenticated) {
    return (
      <Modal show={show} onHide={onHide} centered>
        <Modal.Body className="text-center py-4">
          <p>Please log in to view your profile.</p>
          <Button variant="primary" onClick={onHide}>
            Close
          </Button>
        </Modal.Body>
      </Modal>
    );
  }

  return (
    <Modal 
      show={show} 
      onHide={handleClose}
      centered
      size="lg"
      className="profile-modal"
    >
      <Modal.Header closeButton className="border-0">
        <Modal.Title className="w-100 text-center">
          <FaUser className="me-2" />
          My Profile
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="px-4 pb-4">
        {error && <Alert variant="danger">{error}</Alert>}
        {success && <Alert variant="success">{success}</Alert>}
        
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k)}
          className="mb-3 custom-tabs"
          justify
        >
          <Tab eventKey="view" title="View Profile">
            {loading ? (
              <div className="text-center py-4">
                <Spinner animation="border" />
                <p className="mt-2">Loading profile...</p>
              </div>
            ) : profile ? (
              <div className="profile-view">
                <Card className="border-0 shadow-sm">
                  <Card.Body>
                    <div className="profile-header text-center mb-4">
                      <div className="profile-avatar">
                        {profile.photo ? (
                          <img src={profile.photo} alt="Profile" className="avatar-img" />
                        ) : (
                          <div className="avatar-placeholder">
                            <FaUser size={40} />
                          </div>
                        )}
                      </div>
                      <h4 className="mt-3">{profile.display_name || `${profile.first_name} ${profile.last_name}`}</h4>
                      <p className="text-muted">@{profile.user_name}</p>
                    </div>
                    
                    <Row>
                      <Col md={6}>
                        <div className="profile-field">
                          <label>Email</label>
                          <p>{profile.email}</p>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="profile-field">
                          <label>Mobile</label>
                          <p>{profile.mobile_number}</p>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="profile-field">
                          <label>Gender</label>
                          <p>{profile.gender || 'Not specified'}</p>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="profile-field">
                          <label>Date of Birth</label>
                          <p>{profile.date_of_birth ? new Date(profile.date_of_birth).toLocaleDateString() : 'Not specified'}</p>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="profile-field">
                          <label>Nationality</label>
                          <p>{profile.nationality || 'Not specified'}</p>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="profile-field">
                          <label>Marital Status</label>
                          <p>{profile.martial_status || 'Not specified'}</p>
                        </div>
                      </Col>
                    </Row>
                    
                    <div className="profile-actions mt-4">
                      <Button 
                        variant="primary" 
                        onClick={() => setActiveTab('edit')}
                        className="me-2"
                      >
                        <FaEdit className="me-1" />
                        Edit Profile
                      </Button>
                      <Button 
                        variant="outline-secondary"
                        onClick={() => setActiveTab('settings')}
                      >
                        <FaCog className="me-1" />
                        Settings
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            ) : (
              <div className="text-center py-4">
                <p>No profile data available.</p>
              </div>
            )}
          </Tab>
          
          <Tab eventKey="edit" title="Edit Profile">
            <Form onSubmit={handleUpdateProfile}>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>First Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="first_name"
                      value={editData.first_name}
                      onChange={handleEditChange}
                      placeholder="Enter first name"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Last Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="last_name"
                      value={editData.last_name}
                      onChange={handleEditChange}
                      placeholder="Enter last name"
                    />
                  </Form.Group>
                </Col>
              </Row>
              
              <Form.Group className="mb-3">
                <Form.Label>Display Name</Form.Label>
                <Form.Control
                  type="text"
                  name="display_name"
                  value={editData.display_name}
                  onChange={handleEditChange}
                  placeholder="Enter display name"
                />
              </Form.Group>
              
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Gender</Form.Label>
                    <Form.Select
                      name="gender"
                      value={editData.gender}
                      onChange={handleEditChange}
                    >
                      <option value="">Select gender</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Date of Birth</Form.Label>
                    <Form.Control
                      type="date"
                      name="date_of_birth"
                      value={editData.date_of_birth}
                      onChange={handleEditChange}
                    />
                  </Form.Group>
                </Col>
              </Row>
              
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Nationality</Form.Label>
                    <Form.Control
                      type="text"
                      name="nationality"
                      value={editData.nationality}
                      onChange={handleEditChange}
                      placeholder="Enter nationality"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Marital Status</Form.Label>
                    <Form.Select
                      name="martial_status"
                      value={editData.martial_status}
                      onChange={handleEditChange}
                    >
                      <option value="">Select status</option>
                      <option value="Single">Single</option>
                      <option value="Married">Married</option>
                      <option value="Divorced">Divorced</option>
                      <option value="Widowed">Widowed</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
              
              <div className="d-flex gap-2">
                <Button 
                  type="submit" 
                  variant="primary"
                  disabled={updating}
                >
                  {updating ? (
                    <>
                      <Spinner animation="border" size="sm" className="me-2" />
                      Updating...
                    </>
                  ) : (
                    'Update Profile'
                  )}
                </Button>
                <Button 
                  type="button" 
                  variant="outline-secondary"
                  onClick={() => setActiveTab('view')}
                >
                  Cancel
                </Button>
              </div>
            </Form>
          </Tab>
          
          <Tab eventKey="settings" title="Settings">
            <div className="settings-content">
              <Card className="border-0 shadow-sm">
                <Card.Body>
                  <h5>Account Settings</h5>
                  <div className="setting-item">
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <h6>Logout</h6>
                        <p className="text-muted small">Sign out of your account</p>
                      </div>
                      <Button 
                        variant="outline-danger"
                        onClick={handleLogout}
                      >
                        <FaSignOutAlt className="me-1" />
                        Logout
                      </Button>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </div>
          </Tab>
        </Tabs>
      </Modal.Body>
    </Modal>
  );
};

export default ProfileModal;
