{"name": "my-group-backend", "version": "1.0.0", "description": "Backend server for My Group application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "postgresql", "mvc"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}