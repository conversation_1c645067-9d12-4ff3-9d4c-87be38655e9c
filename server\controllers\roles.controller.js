import RolesModel from '../models/roles.model.js';
import { generateToken } from '../middleware/auth.middleware.js';

class RolesController {
  // Admin login
  static async adminLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }
      
      const result = await RolesModel.authenticateRole(username, password, 'admin');
      if (result.success) {
        const token = generateToken(result.user);
        res.json({
          ...result,
          token,
          redirectUrl: '/admin/dashboard'
        });
      } else {
        res.status(401).json(result);
      }
    } catch (error) {
      console.error('Admin login controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Corporate login
  static async corporateLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }
      
      const result = await RolesModel.authenticateRole(username, password, 'corporate');
      if (result.success) {
        const token = generateToken(result.user);
        res.json({
          ...result,
          token,
          redirectUrl: '/corporate/dashboard'
        });
      } else {
        res.status(401).json(result);
      }
    } catch (error) {
      console.error('Corporate login controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Head Office login
  static async headOfficeLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }
      
      const result = await RolesModel.authenticateRole(username, password, 'headoffice');
      if (result.success) {
        const token = generateToken(result.user);
        res.json({
          ...result,
          token,
          redirectUrl: '/headoffice/dashboard'
        });
      } else {
        res.status(401).json(result);
      }
    } catch (error) {
      console.error('Head Office login controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Regional login
  static async regionalLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }
      
      const result = await RolesModel.authenticateRole(username, password, 'regional');
      if (result.success) {
        const token = generateToken(result.user);
        res.json({
          ...result,
          token,
          redirectUrl: '/regional/dashboard'
        });
      } else {
        res.status(401).json(result);
      }
    } catch (error) {
      console.error('Regional login controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Branch login
  static async branchLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }
      
      const result = await RolesModel.authenticateRole(username, password, 'branch');
      if (result.success) {
        const token = generateToken(result.user);
        res.json({
          ...result,
          token,
          redirectUrl: '/branch/dashboard'
        });
      } else {
        res.status(401).json(result);
      }
    } catch (error) {
      console.error('Branch login controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Create role-based user (admin creates corporate, corporate creates headoffice, etc.)
  static async createRoleUser(req, res) {
    try {
      const userData = req.body;
      const { creatorRole, creatorId } = req.user; // Assuming middleware sets this
      
      if (!userData.username || !userData.password || !userData.role) {
        return res.status(400).json({
          success: false,
          error: 'Username, password, and role are required'
        });
      }
      
      // Validate role hierarchy
      const canCreate = RolesModel.canCreateRole(creatorRole, userData.role);
      if (!canCreate) {
        return res.status(403).json({
          success: false,
          error: `${creatorRole} cannot create ${userData.role} users`
        });
      }
      
      const result = await RolesModel.createRoleUser(userData, creatorId);
      
      if (result.success) {
        res.status(201).json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error('Create role user controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get users by role
  static async getUsersByRole(req, res) {
    try {
      const { role } = req.params;
      const { creatorId } = req.query;
      
      if (!role) {
        return res.status(400).json({
          success: false,
          error: 'Role is required'
        });
      }
      
      const result = await RolesModel.getUsersByRole(role, creatorId);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error('Get users by role controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Update role user
  static async updateRoleUser(req, res) {
    try {
      const { userId } = req.params;
      const updateData = req.body;
      const { creatorRole } = req.user; // Assuming middleware sets this
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }
      
      const result = await RolesModel.updateRoleUser(userId, updateData, creatorRole);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error('Update role user controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Delete role user
  static async deleteRoleUser(req, res) {
    try {
      const { userId } = req.params;
      const { creatorRole } = req.user; // Assuming middleware sets this
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }
      
      const result = await RolesModel.deleteRoleUser(userId, creatorRole);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error('Delete role user controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get role hierarchy
  static async getRoleHierarchy(req, res) {
    try {
      const { role } = req.params;
      
      const result = RolesModel.getRoleHierarchy(role);
      res.json(result);
    } catch (error) {
      console.error('Get role hierarchy controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get dashboard data based on role
  static async getDashboardData(req, res) {
    try {
      const { role, userId } = req.user; // Assuming middleware sets this
      
      const result = await RolesModel.getDashboardData(role, userId);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error('Get dashboard data controller error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

export default RolesController;
