const pool = require('../config/database');

class GroupData {
  // Get all group data
  static async getAll() {
    const query = `
      SELECT id, name, logo, icon, background_color, color, group_url, group_name
      FROM my_group_data
      ORDER BY id
    `;
    
    try {
      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get group data by ID
  static async findById(id) {
    const query = `
      SELECT id, name, logo, icon, background_color, color, group_url, group_name
      FROM my_group_data
      WHERE id = $1
    `;
    
    try {
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Get group categories by group ID
  static async getGroupCategories(groupId) {
    const query = `
      SELECT gc.id, gc.name, gc.icon, gc.display_name, gc.end_point,
             gd.name as group_name, gd.logo as group_logo
      FROM my_group_category gc
      LEFT JOIN my_group_data gd ON gc.my_group_data_id = gd.id
      WHERE gc.my_group_data_id = $1
      ORDER BY gc.id
    `;
    
    try {
      const result = await pool.query(query, [groupId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get all groups with their categories
  static async getAllWithCategories() {
    const query = `
      SELECT gd.id, gd.name, gd.logo, gd.icon, gd.background_color, 
             gd.color, gd.group_url, gd.group_name,
             json_agg(
               json_build_object(
                 'id', gc.id,
                 'name', gc.name,
                 'icon', gc.icon,
                 'display_name', gc.display_name,
                 'end_point', gc.end_point
               )
             ) as categories
      FROM my_group_data gd
      LEFT JOIN my_group_category gc ON gd.id = gc.my_group_data_id
      GROUP BY gd.id, gd.name, gd.logo, gd.icon, gd.background_color, 
               gd.color, gd.group_url, gd.group_name
      ORDER BY gd.id
    `;
    
    try {
      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Create new group data
  static async create(groupData) {
    const {
      name,
      logo,
      icon,
      background_color,
      color,
      group_url,
      group_name
    } = groupData;

    const query = `
      INSERT INTO my_group_data (name, logo, icon, background_color, color, group_url, group_name)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;
    
    const values = [name, logo, icon, background_color, color, group_url, group_name];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Update group data
  static async update(id, updateData) {
    const fields = [];
    const values = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = $${paramCount}`);
        values.push(updateData[key]);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    values.push(id);

    const query = `
      UPDATE my_group_data 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete group data
  static async delete(id) {
    const query = `DELETE FROM my_group_data WHERE id = $1 RETURNING *`;
    
    try {
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }
}

module.exports = GroupData;
