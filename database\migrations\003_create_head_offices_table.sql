-- Migration: Create my_head_offices table
-- Description: Stores head office information under organizations
-- Created: 2024-01-18

CREATE TABLE my_head_offices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    office_name VARCHAR(255) NOT NULL,
    office_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VA<PERSON>HAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES my_organizations(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_head_offices_code ON my_head_offices(office_code);
CREATE INDEX idx_head_offices_organization ON my_head_offices(organization_id);
CREATE INDEX idx_head_offices_active ON my_head_offices(is_active);
CREATE INDEX idx_head_offices_name ON my_head_offices(office_name);
