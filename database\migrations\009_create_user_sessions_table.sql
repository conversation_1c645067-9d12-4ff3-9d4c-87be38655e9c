-- Migration: Create my_user_sessions table
-- Description: Tracks user login sessions for security and session management
-- Created: 2024-01-18

CREATE TABLE my_user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES my_users(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_sessions_token ON my_user_sessions(session_token);
CREATE INDEX idx_sessions_user_active ON my_user_sessions(user_id, is_active);
CREATE INDEX idx_sessions_expires ON my_user_sessions(expires_at);
CREATE INDEX idx_sessions_user_id ON my_user_sessions(user_id);
