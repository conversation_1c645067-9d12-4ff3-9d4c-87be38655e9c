import React, { useState, useEffect, useRef } from 'react';
import { Container, Row, Col, Card, Button, Form, Modal, Alert, Badge, Tab, Tabs } from 'react-bootstrap';

const FranchiseTermsManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [activeTab, setActiveTab] = useState('terms');
    const editorRef = useRef(null);

    // Content data
    const [contentData, setContentData] = useState({
        terms: {
            id: 1,
            title: 'Franchise Terms and Conditions',
            content: `
                <h2>Franchise Agreement Terms</h2>
                <p>Welcome to our franchise program. Please read these terms and conditions carefully before proceeding with your franchise application.</p>
                
                <h3>1. Franchise Rights and Obligations</h3>
                <p>As a franchisee, you will receive the right to operate under our brand name and business model. This includes:</p>
                <ul>
                    <li>Use of our trademark and brand identity</li>
                    <li>Access to our proven business systems</li>
                    <li>Ongoing training and support</li>
                    <li>Marketing and advertising materials</li>
                </ul>
                
                <h3>2. Financial Requirements</h3>
                <p>The following financial commitments are required:</p>
                <ul>
                    <li>Initial franchise fee: $50,000</li>
                    <li>Ongoing royalty fee: 6% of gross revenue</li>
                    <li>Marketing fund contribution: 2% of gross revenue</li>
                    <li>Minimum liquid capital: $100,000</li>
                </ul>
                
                <h3>3. Territory and Exclusivity</h3>
                <p>Each franchise location will be granted an exclusive territory based on population density and market analysis.</p>
                
                <h3>4. Training and Support</h3>
                <p>We provide comprehensive training programs including:</p>
                <ul>
                    <li>Initial 2-week training program</li>
                    <li>On-site support during opening</li>
                    <li>Ongoing operational support</li>
                    <li>Regular business reviews</li>
                </ul>
            `,
            lastUpdated: '2024-01-20',
            status: 'Published'
        },
        conditions: {
            id: 2,
            title: 'General Terms and Conditions',
            content: `
                <h2>General Terms and Conditions</h2>
                <p>These general terms and conditions govern the relationship between our company and all stakeholders.</p>
                
                <h3>1. Acceptance of Terms</h3>
                <p>By accessing our services, you agree to be bound by these terms and conditions.</p>
                
                <h3>2. Intellectual Property</h3>
                <p>All intellectual property rights remain with our company, including but not limited to:</p>
                <ul>
                    <li>Trademarks and service marks</li>
                    <li>Copyrighted materials</li>
                    <li>Trade secrets and proprietary information</li>
                    <li>Software and technology platforms</li>
                </ul>
                
                <h3>3. Limitation of Liability</h3>
                <p>Our liability is limited to the extent permitted by applicable law.</p>
                
                <h3>4. Governing Law</h3>
                <p>These terms are governed by the laws of the jurisdiction in which our company is incorporated.</p>
            `,
            lastUpdated: '2024-01-18',
            status: 'Published'
        },
        privacy: {
            id: 3,
            title: 'Privacy Policy',
            content: `
                <h2>Privacy Policy</h2>
                <p>This privacy policy explains how we collect, use, and protect your personal information.</p>
                
                <h3>1. Information We Collect</h3>
                <p>We may collect the following types of information:</p>
                <ul>
                    <li>Personal identification information</li>
                    <li>Contact information</li>
                    <li>Financial information (for franchise applications)</li>
                    <li>Usage data and analytics</li>
                </ul>
                
                <h3>2. How We Use Your Information</h3>
                <p>Your information is used for:</p>
                <ul>
                    <li>Processing franchise applications</li>
                    <li>Providing customer support</li>
                    <li>Improving our services</li>
                    <li>Marketing communications (with consent)</li>
                </ul>
                
                <h3>3. Data Protection</h3>
                <p>We implement appropriate security measures to protect your personal information.</p>
                
                <h3>4. Your Rights</h3>
                <p>You have the right to access, update, or delete your personal information.</p>
            `,
            lastUpdated: '2024-01-15',
            status: 'Published'
        }
    });

    const [editingContent, setEditingContent] = useState('');
    const [showPreview, setShowPreview] = useState(false);

    useEffect(() => {
        // Initialize rich text editor (mock implementation)
        initializeEditor();
    }, [activeTab]);

    const initializeEditor = () => {
        // In a real implementation, you would initialize Summernote here
        // $('#summernote').summernote({
        //     height: 400,
        //     toolbar: [
        //         ['style', ['style']],
        //         ['font', ['bold', 'underline', 'clear']],
        //         ['color', ['color']],
        //         ['para', ['ul', 'ol', 'paragraph']],
        //         ['table', ['table']],
        //         ['insert', ['link', 'picture', 'video']],
        //         ['view', ['fullscreen', 'codeview', 'help']]
        //     ]
        // });
        
        // For now, we'll use a textarea with rich content
        setEditingContent(contentData[activeTab].content);
    };

    const handleSave = async () => {
        setLoading(true);
        try {
            // API call to save content
            const updatedContent = {
                ...contentData[activeTab],
                content: editingContent,
                lastUpdated: new Date().toISOString().split('T')[0]
            };

            setContentData({
                ...contentData,
                [activeTab]: updatedContent
            });

            setSuccess(`${contentData[activeTab].title} updated successfully`);
        } catch (error) {
            setError('Failed to save content');
        } finally {
            setLoading(false);
        }
    };

    const handlePublish = async () => {
        setLoading(true);
        try {
            // API call to publish content
            const updatedContent = {
                ...contentData[activeTab],
                content: editingContent,
                status: 'Published',
                lastUpdated: new Date().toISOString().split('T')[0]
            };

            setContentData({
                ...contentData,
                [activeTab]: updatedContent
            });

            setSuccess(`${contentData[activeTab].title} published successfully`);
        } catch (error) {
            setError('Failed to publish content');
        } finally {
            setLoading(false);
        }
    };

    const handlePreview = () => {
        setShowPreview(true);
    };

    const insertTemplate = (templateType) => {
        let template = '';
        
        switch (templateType) {
            case 'section':
                template = `
                    <h3>Section Title</h3>
                    <p>Section content goes here...</p>
                `;
                break;
            case 'list':
                template = `
                    <ul>
                        <li>List item 1</li>
                        <li>List item 2</li>
                        <li>List item 3</li>
                    </ul>
                `;
                break;
            case 'table':
                template = `
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Column 1</th>
                                <th>Column 2</th>
                                <th>Column 3</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Data 1</td>
                                <td>Data 2</td>
                                <td>Data 3</td>
                            </tr>
                        </tbody>
                    </table>
                `;
                break;
            case 'contact':
                template = `
                    <div class="contact-info">
                        <h4>Contact Information</h4>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Phone:</strong> +****************</p>
                        <p><strong>Address:</strong> 123 Business Street, City, State 12345</p>
                    </div>
                `;
                break;
            default:
                template = '<p>New content...</p>';
        }
        
        setEditingContent(editingContent + template);
    };

    const getStatusBadgeColor = (status) => {
        switch (status) {
            case 'Published': return 'success';
            case 'Draft': return 'warning';
            case 'Archived': return 'secondary';
            default: return 'secondary';
        }
    };

    return (
        <Container fluid className="franchise-terms-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Franchise Terms and Conditions Management</h4>
                <div>
                    <Button variant="outline-info" className="me-2" onClick={handlePreview}>
                        <i className="fas fa-eye me-1"></i>Preview
                    </Button>
                    <Button variant="success" className="me-2" onClick={handleSave} disabled={loading}>
                        <i className="fas fa-save me-1"></i>Save Draft
                    </Button>
                    <Button variant="primary" onClick={handlePublish} disabled={loading}>
                        <i className="fas fa-globe me-1"></i>Publish
                    </Button>
                </div>
            </div>

            {/* Content Statistics */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">3</h3>
                            <p className="mb-0">Total Documents</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">3</h3>
                            <p className="mb-0">Published</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {contentData[activeTab].lastUpdated}
                            </h3>
                            <p className="mb-0">Last Updated</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <Badge bg={getStatusBadgeColor(contentData[activeTab].status)} className="fs-6">
                                {contentData[activeTab].status}
                            </Badge>
                            <p className="mb-0 mt-2">Current Status</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Content Editor */}
            <Card>
                <Card.Header>
                    <Tabs activeKey={activeTab} onSelect={(k) => setActiveTab(k)}>
                        <Tab eventKey="terms" title="Franchise Terms">
                            <span className="ms-2">
                                <Badge bg={getStatusBadgeColor(contentData.terms.status)}>
                                    {contentData.terms.status}
                                </Badge>
                            </span>
                        </Tab>
                        <Tab eventKey="conditions" title="General Conditions">
                            <span className="ms-2">
                                <Badge bg={getStatusBadgeColor(contentData.conditions.status)}>
                                    {contentData.conditions.status}
                                </Badge>
                            </span>
                        </Tab>
                        <Tab eventKey="privacy" title="Privacy Policy">
                            <span className="ms-2">
                                <Badge bg={getStatusBadgeColor(contentData.privacy.status)}>
                                    {contentData.privacy.status}
                                </Badge>
                            </span>
                        </Tab>
                    </Tabs>
                </Card.Header>
                <Card.Body>
                    <Row>
                        <Col md={9}>
                            <div className="editor-container">
                                <div className="editor-toolbar mb-3">
                                    <div className="btn-group me-2">
                                        <Button 
                                            variant="outline-secondary" 
                                            size="sm"
                                            onClick={() => insertTemplate('section')}
                                        >
                                            <i className="fas fa-heading me-1"></i>Section
                                        </Button>
                                        <Button 
                                            variant="outline-secondary" 
                                            size="sm"
                                            onClick={() => insertTemplate('list')}
                                        >
                                            <i className="fas fa-list me-1"></i>List
                                        </Button>
                                        <Button 
                                            variant="outline-secondary" 
                                            size="sm"
                                            onClick={() => insertTemplate('table')}
                                        >
                                            <i className="fas fa-table me-1"></i>Table
                                        </Button>
                                        <Button 
                                            variant="outline-secondary" 
                                            size="sm"
                                            onClick={() => insertTemplate('contact')}
                                        >
                                            <i className="fas fa-address-card me-1"></i>Contact
                                        </Button>
                                    </div>
                                </div>
                                
                                <Form.Group className="mb-3">
                                    <Form.Label>Document Title</Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={contentData[activeTab].title}
                                        onChange={(e) => setContentData({
                                            ...contentData,
                                            [activeTab]: {
                                                ...contentData[activeTab],
                                                title: e.target.value
                                            }
                                        })}
                                    />
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label>Content</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={20}
                                        value={editingContent}
                                        onChange={(e) => setEditingContent(e.target.value)}
                                        className="rich-text-editor"
                                        style={{ fontFamily: 'monospace' }}
                                    />
                                    <Form.Text className="text-muted">
                                        HTML content editor. In production, this would be replaced with Summernote rich text editor.
                                    </Form.Text>
                                </Form.Group>
                            </div>
                        </Col>
                        
                        <Col md={3}>
                            <Card className="mb-3">
                                <Card.Header>
                                    <h6 className="mb-0">Document Info</h6>
                                </Card.Header>
                                <Card.Body>
                                    <div className="mb-2">
                                        <small className="text-muted">Status:</small>
                                        <br />
                                        <Badge bg={getStatusBadgeColor(contentData[activeTab].status)}>
                                            {contentData[activeTab].status}
                                        </Badge>
                                    </div>
                                    <div className="mb-2">
                                        <small className="text-muted">Last Updated:</small>
                                        <br />
                                        <small>{contentData[activeTab].lastUpdated}</small>
                                    </div>
                                    <div className="mb-2">
                                        <small className="text-muted">Word Count:</small>
                                        <br />
                                        <small>{editingContent.replace(/<[^>]*>/g, '').split(' ').length} words</small>
                                    </div>
                                </Card.Body>
                            </Card>

                            <Card className="mb-3">
                                <Card.Header>
                                    <h6 className="mb-0">Quick Templates</h6>
                                </Card.Header>
                                <Card.Body>
                                    <div className="d-grid gap-2">
                                        <Button 
                                            variant="outline-primary" 
                                            size="sm"
                                            onClick={() => insertTemplate('section')}
                                        >
                                            Add Section
                                        </Button>
                                        <Button 
                                            variant="outline-primary" 
                                            size="sm"
                                            onClick={() => insertTemplate('list')}
                                        >
                                            Add List
                                        </Button>
                                        <Button 
                                            variant="outline-primary" 
                                            size="sm"
                                            onClick={() => insertTemplate('table')}
                                        >
                                            Add Table
                                        </Button>
                                        <Button 
                                            variant="outline-primary" 
                                            size="sm"
                                            onClick={() => insertTemplate('contact')}
                                        >
                                            Add Contact Info
                                        </Button>
                                    </div>
                                </Card.Body>
                            </Card>

                            <Card>
                                <Card.Header>
                                    <h6 className="mb-0">Actions</h6>
                                </Card.Header>
                                <Card.Body>
                                    <div className="d-grid gap-2">
                                        <Button variant="outline-info" onClick={handlePreview}>
                                            <i className="fas fa-eye me-1"></i>Preview
                                        </Button>
                                        <Button variant="success" onClick={handleSave} disabled={loading}>
                                            <i className="fas fa-save me-1"></i>Save Draft
                                        </Button>
                                        <Button variant="primary" onClick={handlePublish} disabled={loading}>
                                            <i className="fas fa-globe me-1"></i>Publish
                                        </Button>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Preview Modal */}
            <Modal show={showPreview} onHide={() => setShowPreview(false)} size="xl">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-eye me-2"></i>
                        Preview: {contentData[activeTab].title}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div 
                        className="content-preview"
                        dangerouslySetInnerHTML={{ __html: editingContent }}
                        style={{ 
                            padding: '20px',
                            border: '1px solid #ddd',
                            borderRadius: '8px',
                            backgroundColor: '#fff',
                            minHeight: '400px'
                        }}
                    />
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowPreview(false)}>
                        Close Preview
                    </Button>
                    <Button variant="primary" onClick={handlePublish} disabled={loading}>
                        <i className="fas fa-globe me-1"></i>Publish Now
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default FranchiseTermsManagement;
