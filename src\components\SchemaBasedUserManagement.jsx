import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { User, ModelFactory } from '../models/DatabaseModels';
import '../styles/SchemaBasedUserManagement.css';

const SchemaBasedUserManagement = () => {
  const { currentUser, schemaBasedDatabaseService, hasPermission, hasRoleAccess } = useAuth();
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [filterRole, setFilterRole] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Form data for create/edit
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    mobile_number: '',
    password: '',
    role_id: '',
    group_id: '',
    status: 1
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [usersData, rolesData, groupsData] = await Promise.all([
        schemaBasedDatabaseService.getUsers(filterRole),
        schemaBasedDatabaseService.getRoles(),
        schemaBasedDatabaseService.getGroups()
      ]);
      
      setUsers(usersData);
      setRoles(rolesData);
      setGroups(groupsData);
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = async (roleFilter) => {
    setFilterRole(roleFilter);
    try {
      const usersData = await schemaBasedDatabaseService.getUsers(roleFilter);
      setUsers(usersData);
    } catch (error) {
      console.error('Error filtering users:', error);
      setError('Failed to filter users');
    }
  };

  const handleCreateUser = async (e) => {
    e.preventDefault();
    try {
      // Validate form data using model
      const userModel = ModelFactory.createUser(formData);
      if (!userModel.validate()) {
        const errors = userModel.getErrors();
        setError(Object.values(errors).flat().join(', '));
        return;
      }

      const newUser = await schemaBasedDatabaseService.createUser(formData);
      setUsers(prev => [...prev, newUser]);
      setShowCreateModal(false);
      resetForm();
      setError('');
    } catch (error) {
      console.error('Error creating user:', error);
      setError('Failed to create user');
    }
  };

  const handleEditUser = async (e) => {
    e.preventDefault();
    try {
      // Validate form data using model
      const userModel = ModelFactory.createUser(formData);
      if (!userModel.validate()) {
        const errors = userModel.getErrors();
        setError(Object.values(errors).flat().join(', '));
        return;
      }

      const updatedUser = await schemaBasedDatabaseService.updateUser(selectedUser.id, formData);
      setUsers(prev => prev.map(user => user.id === selectedUser.id ? updatedUser : user));
      setShowEditModal(false);
      setSelectedUser(null);
      resetForm();
      setError('');
    } catch (error) {
      console.error('Error updating user:', error);
      setError('Failed to update user');
    }
  };

  const handleDeleteUser = async (userId) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      await schemaBasedDatabaseService.deleteUser(userId);
      setUsers(prev => prev.filter(user => user.id !== userId));
      setError('');
    } catch (error) {
      console.error('Error deleting user:', error);
      setError('Failed to delete user');
    }
  };

  const openEditModal = (user) => {
    setSelectedUser(user);
    setFormData({
      username: user.user_name,
      email: user.email,
      mobile_number: user.mobile_number,
      password: '', // Don't populate password for security
      role_id: user.role_id,
      group_id: user.group_id,
      status: user.status
    });
    setShowEditModal(true);
  };

  const resetForm = () => {
    setFormData({
      username: '',
      email: '',
      mobile_number: '',
      password: '',
      role_id: '',
      group_id: '',
      status: 1
    });
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchTerm || 
      user.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role_name?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  // Check permissions
  const canManageUsers = hasPermission('user_management');
  const canCreateUsers = canManageUsers && hasRoleAccess('corporate');
  const canEditUsers = canManageUsers;
  const canDeleteUsers = canManageUsers && hasRoleAccess('admin');

  if (!canManageUsers) {
    return (
      <div className="access-denied">
        <h2>Access Denied</h2>
        <p>You don't have permission to manage users.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading users...</p>
      </div>
    );
  }

  return (
    <div className="schema-user-management">
      <div className="page-header">
        <h1>User Management</h1>
        <p>Manage users across all roles and groups</p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          {error}
          <button onClick={() => setError('')} className="close-error">×</button>
        </div>
      )}

      {/* Controls */}
      <div className="controls-section">
        <div className="search-filter-controls">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filter-controls">
            <select
              value={filterRole}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="role-filter"
            >
              <option value="">All Roles</option>
              {roles.map(role => (
                <option key={role.id} value={role.role}>
                  {role.role_description}
                </option>
              ))}
            </select>
          </div>

          {canCreateUsers && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="create-user-btn"
            >
              <span className="btn-icon">+</span>
              Create User
            </button>
          )}
        </div>

        <div className="stats-section">
          <div className="stat-card">
            <span className="stat-number">{filteredUsers.length}</span>
            <span className="stat-label">Total Users</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">{filteredUsers.filter(u => u.status === 1).length}</span>
            <span className="stat-label">Active Users</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">{roles.length}</span>
            <span className="stat-label">Roles</span>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="users-table-container">
        <table className="users-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Username</th>
              <th>Email</th>
              <th>Mobile</th>
              <th>Role</th>
              <th>Group</th>
              <th>Status</th>
              <th>Last Login</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.map(user => (
              <tr key={user.id} className={user.status === 1 ? 'active' : 'inactive'}>
                <td>{user.id}</td>
                <td>
                  <div className="user-info">
                    <span className="username">{user.user_name}</span>
                  </div>
                </td>
                <td>{user.email}</td>
                <td>{user.mobile_number}</td>
                <td>
                  <span className={`role-badge role-${user.role_name}`}>
                    {user.role_description || user.role_name}
                  </span>
                </td>
                <td>{user.group_name}</td>
                <td>
                  <span className={`status-badge ${user.status === 1 ? 'active' : 'inactive'}`}>
                    {user.status === 1 ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{new Date(user.last_logged_in).toLocaleDateString()}</td>
                <td>
                  <div className="action-buttons">
                    {canEditUsers && (
                      <button
                        onClick={() => openEditModal(user)}
                        className="edit-btn"
                        title="Edit User"
                      >
                        ✏️
                      </button>
                    )}
                    {canDeleteUsers && user.id !== currentUser?.id && (
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className="delete-btn"
                        title="Delete User"
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredUsers.length === 0 && (
          <div className="no-users">
            <p>No users found matching your criteria.</p>
          </div>
        )}
      </div>

      {/* Create User Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>Create New User</h2>
              <button onClick={() => setShowCreateModal(false)} className="close-modal">×</button>
            </div>
            <form onSubmit={handleCreateUser} className="user-form">
              <div className="form-row">
                <div className="form-group">
                  <label>Username *</label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({...prev, username: e.target.value}))}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Email *</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({...prev, email: e.target.value}))}
                    required
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Mobile Number *</label>
                  <input
                    type="tel"
                    value={formData.mobile_number}
                    onChange={(e) => setFormData(prev => ({...prev, mobile_number: e.target.value}))}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Password *</label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({...prev, password: e.target.value}))}
                    required
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Role *</label>
                  <select
                    value={formData.role_id}
                    onChange={(e) => setFormData(prev => ({...prev, role_id: parseInt(e.target.value)}))}
                    required
                  >
                    <option value="">Select Role</option>
                    {roles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.role_description}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Group *</label>
                  <select
                    value={formData.group_id}
                    onChange={(e) => setFormData(prev => ({...prev, group_id: parseInt(e.target.value)}))}
                    required
                  >
                    <option value="">Select Group</option>
                    {groups.map(group => (
                      <option key={group.id} value={group.id}>
                        {group.group_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="form-actions">
                <button type="button" onClick={() => setShowCreateModal(false)} className="cancel-btn">
                  Cancel
                </button>
                <button type="submit" className="submit-btn">
                  Create User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && selectedUser && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>Edit User: {selectedUser.user_name}</h2>
              <button onClick={() => setShowEditModal(false)} className="close-modal">×</button>
            </div>
            <form onSubmit={handleEditUser} className="user-form">
              <div className="form-row">
                <div className="form-group">
                  <label>Username *</label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({...prev, username: e.target.value}))}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Email *</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({...prev, email: e.target.value}))}
                    required
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Mobile Number *</label>
                  <input
                    type="tel"
                    value={formData.mobile_number}
                    onChange={(e) => setFormData(prev => ({...prev, mobile_number: e.target.value}))}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>New Password (leave blank to keep current)</label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({...prev, password: e.target.value}))}
                    placeholder="Enter new password or leave blank"
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Role *</label>
                  <select
                    value={formData.role_id}
                    onChange={(e) => setFormData(prev => ({...prev, role_id: parseInt(e.target.value)}))}
                    required
                  >
                    <option value="">Select Role</option>
                    {roles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.role_description}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Group *</label>
                  <select
                    value={formData.group_id}
                    onChange={(e) => setFormData(prev => ({...prev, group_id: parseInt(e.target.value)}))}
                    required
                  >
                    <option value="">Select Group</option>
                    {groups.map(group => (
                      <option key={group.id} value={group.id}>
                        {group.group_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({...prev, status: parseInt(e.target.value)}))}
                  >
                    <option value={1}>Active</option>
                    <option value={0}>Inactive</option>
                  </select>
                </div>
              </div>
              <div className="form-actions">
                <button type="button" onClick={() => setShowEditModal(false)} className="cancel-btn">
                  Cancel
                </button>
                <button type="submit" className="submit-btn">
                  Update User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SchemaBasedUserManagement;
