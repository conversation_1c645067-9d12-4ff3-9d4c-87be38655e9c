/* =====================================================
   CORPORATE DASHBOARD STYLES
   ===================================================== */

.corporate-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
}

/* =====================================================
   SIDEBAR STYLES
   ===================================================== */

.corporate-sidebar {
  width: 280px;
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.corporate-sidebar .sidebar-header {
  padding: 25px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.corporate-sidebar .sidebar-header h4 {
  margin: 0;
  font-weight: 700;
  color: #ecf0f1;
  font-size: 1.5rem;
}

.corporate-sidebar .sidebar-header small {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.corporate-sidebar .sidebar-menu {
  padding: 20px 0;
}

.corporate-sidebar .menu-item {
  padding: 15px 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.corporate-sidebar .menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #3498db;
}

.corporate-sidebar .menu-item.active {
  background: rgba(52, 152, 219, 0.2);
  border-left-color: #3498db;
  color: #3498db;
}

.corporate-sidebar .menu-item i {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
}

.corporate-sidebar .menu-label {
  flex: 1;
  margin-left: 10px;
  font-weight: 500;
}

.corporate-sidebar .submenu-arrow {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.corporate-sidebar .submenu {
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.corporate-sidebar .submenu-item {
  padding: 12px 25px 12px 60px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bdc3c7;
  font-size: 0.9rem;
}

.corporate-sidebar .submenu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.corporate-sidebar .submenu-item.active {
  background: rgba(52, 152, 219, 0.3);
  color: #3498db;
  font-weight: 600;
}

/* =====================================================
   MAIN CONTENT STYLES
   ===================================================== */

.corporate-content {
  margin-left: 280px;
  min-height: 100vh;
  background: #f8f9fa;
}

.corporate-content .content-header {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.corporate-content .content-header span {
  color: #495057;
  font-weight: 500;
}

.corporate-content .content-body {
  padding: 30px;
  animation: fadeIn 0.5s ease-out;
}

/* =====================================================
   DASHBOARD CARDS
   ===================================================== */

.dashboard-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-body {
  padding: 25px;
}

.dashboard-card i {
  opacity: 0.8;
}

/* =====================================================
   ACTIVITY ITEMS
   ===================================================== */

.activity-item {
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item small {
  display: block;
  color: #6c757d;
  font-size: 0.75rem;
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
  .corporate-sidebar {
    width: 250px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .corporate-sidebar.show {
    transform: translateX(0);
  }

  .corporate-content {
    margin-left: 0;
  }

  .corporate-content .content-body {
    padding: 20px 15px;
  }

  .dashboard-card .card-body {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .corporate-sidebar {
    width: 100%;
  }

  .corporate-content .content-header {
    padding: 15px 20px;
  }

  .corporate-content .content-body {
    padding: 15px;
  }
}

/* =====================================================
   ANIMATIONS
   ===================================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.corporate-content .content-body > * {
  animation: fadeIn 0.5s ease-out;
}

/* =====================================================
   UTILITY CLASSES
   ===================================================== */

.text-gradient {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-corporate {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.card-shadow {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 12px;
}

.btn-corporate {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border: none;
  color: white;
  font-weight: 600;
  padding: 10px 25px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-corporate:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
  color: white;
}

/* =====================================================
   COMPONENT SPECIFIC STYLES
   ===================================================== */

/* Office Management */
.office-management .table th {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  border: none;
}

.office-management .badge {
  font-size: 0.8rem;
  padding: 6px 10px;
}

/* Ads Management */
.ads-management .calendar-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ads-management .price-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ads-management .price-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Footer Management */
.footer-management .section-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.footer-management .section-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

/* Public Database */
.public-database .export-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.public-database .filter-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* Custom Scrollbar */
.corporate-content::-webkit-scrollbar {
  width: 6px;
}

.corporate-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.corporate-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.corporate-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
