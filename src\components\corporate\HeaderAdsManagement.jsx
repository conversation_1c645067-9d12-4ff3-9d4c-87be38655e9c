import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge, ProgressBar } from 'react-bootstrap';

const HeaderAdsManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [editingAd, setEditingAd] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);

    // Form data
    const [formData, setFormData] = useState({
        groupName: '',
        adTitle: '',
        adDescription: '',
        adFile: null,
        adType: 'image',
        startDate: '',
        endDate: '',
        status: 'Active',
        priority: 1
    });

    // Header ads data
    const [headerAds, setHeaderAds] = useState([
        {
            id: 1,
            groupName: 'My Apps',
            groupId: 1,
            adTitle: 'Mobile App Development',
            adDescription: 'Professional mobile app development services',
            adFile: '/images/ads/mobile-app-ad.jpg',
            adType: 'image',
            startDate: '2024-01-15',
            endDate: '2024-02-15',
            status: 'Active',
            priority: 1,
            views: 1250,
            clicks: 89,
            createdAt: '2024-01-15'
        },
        {
            id: 2,
            groupName: 'My Company',
            groupId: 2,
            adTitle: 'Corporate Solutions',
            adDescription: 'Enterprise business solutions for your company',
            adFile: '/images/ads/corporate-ad.jpg',
            adType: 'image',
            startDate: '2024-01-10',
            endDate: '2024-02-10',
            status: 'Active',
            priority: 2,
            views: 980,
            clicks: 67,
            createdAt: '2024-01-10'
        },
        {
            id: 3,
            groupName: 'My Online Apps',
            groupId: 3,
            adTitle: 'Web Services Promo',
            adDescription: 'Special offer on web development services',
            adFile: '/images/ads/web-services-ad.mp4',
            adType: 'video',
            startDate: '2024-01-08',
            endDate: '2024-01-25',
            status: 'Expired',
            priority: 3,
            views: 756,
            clicks: 45,
            createdAt: '2024-01-08'
        }
    ]);

    // Groups data (from Profile Management)
    const [groups, setGroups] = useState([
        { id: 1, groupName: 'My Apps', groupType: 'My Apps' },
        { id: 2, groupName: 'My Company', groupType: 'My Company' },
        { id: 3, groupName: 'My Online Apps', groupType: 'My Online Apps' },
        { id: 4, groupName: 'My Offline Apps', groupType: 'My Offline Apps' }
    ]);

    const adTypes = ['image', 'video', 'gif', 'banner'];

    useEffect(() => {
        fetchHeaderAds();
        fetchGroupsData();
        updateAdStatuses();
    }, []);

    const fetchHeaderAds = async () => {
        setLoading(true);
        try {
            // API call to fetch header ads
            // const response = await fetch('/api/header-ads');
            // const data = await response.json();
            // setHeaderAds(data);
        } catch (error) {
            console.error('Error fetching header ads:', error);
            setError('Failed to fetch header ads');
        } finally {
            setLoading(false);
        }
    };

    const fetchGroupsData = async () => {
        try {
            // API call to fetch groups
            // const response = await fetch('/api/profile-groups');
            // const data = await response.json();
            // setGroups(data);
        } catch (error) {
            console.error('Error fetching groups:', error);
        }
    };

    const updateAdStatuses = () => {
        const today = new Date().toISOString().split('T')[0];
        setHeaderAds(prevAds => 
            prevAds.map(ad => {
                if (ad.endDate < today && ad.status === 'Active') {
                    return { ...ad, status: 'Expired' };
                }
                return ad;
            })
        );
    };

    const handleCreate = () => {
        setEditingAd(null);
        setFormData({
            groupName: '',
            adTitle: '',
            adDescription: '',
            adFile: null,
            adType: 'image',
            startDate: '',
            endDate: '',
            status: 'Active',
            priority: 1
        });
        setShowModal(true);
    };

    const handleEdit = (ad) => {
        setEditingAd(ad);
        setFormData({
            groupName: ad.groupId,
            adTitle: ad.adTitle,
            adDescription: ad.adDescription,
            adFile: null, // File will be handled separately
            adType: ad.adType,
            startDate: ad.startDate,
            endDate: ad.endDate,
            status: ad.status,
            priority: ad.priority
        });
        setShowModal(true);
    };

    const handleDelete = (adId) => {
        if (window.confirm('Are you sure you want to delete this header ad?')) {
            setHeaderAds(headerAds.filter(ad => ad.id !== adId));
            setSuccess('Header ad deleted successfully');
        }
    };

    const toggleAdStatus = (adId) => {
        setHeaderAds(headerAds.map(ad => 
            ad.id === adId 
                ? { ...ad, status: ad.status === 'Active' ? 'Inactive' : 'Active' }
                : ad
        ));
        setSuccess('Ad status updated successfully');
    };

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = {
                image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                video: ['video/mp4', 'video/webm', 'video/ogg'],
                gif: ['image/gif'],
                banner: ['image/jpeg', 'image/png', 'image/webp']
            };

            const isValidType = allowedTypes[formData.adType]?.includes(file.type);
            if (!isValidType) {
                setError(`Invalid file type for ${formData.adType}. Please select a valid file.`);
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                setError('File size must be less than 10MB');
                return;
            }

            setFormData({
                ...formData,
                adFile: file
            });
        }
    };

    const simulateFileUpload = () => {
        return new Promise((resolve) => {
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                setUploadProgress(progress);
                if (progress >= 100) {
                    clearInterval(interval);
                    resolve('/uploads/ads/uploaded-file.jpg');
                }
            }, 200);
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setUploadProgress(0);

        try {
            let adFileUrl = editingAd?.adFile || '';

            // Upload file if new file is selected
            if (formData.adFile) {
                adFileUrl = await simulateFileUpload();
            }

            const selectedGroup = groups.find(g => g.id === parseInt(formData.groupName));
            
            const adData = {
                id: editingAd ? editingAd.id : Date.now(),
                groupName: selectedGroup?.groupName || '',
                groupId: parseInt(formData.groupName),
                adTitle: formData.adTitle,
                adDescription: formData.adDescription,
                adFile: adFileUrl,
                adType: formData.adType,
                startDate: formData.startDate,
                endDate: formData.endDate,
                status: formData.status,
                priority: formData.priority,
                views: editingAd ? editingAd.views : 0,
                clicks: editingAd ? editingAd.clicks : 0,
                createdAt: editingAd ? editingAd.createdAt : new Date().toISOString().split('T')[0]
            };

            if (editingAd) {
                setHeaderAds(headerAds.map(ad => 
                    ad.id === editingAd.id ? adData : ad
                ));
                setSuccess('Header ad updated successfully');
            } else {
                setHeaderAds([...headerAds, adData]);
                setSuccess('Header ad created successfully');
            }

            setShowModal(false);
            setFormData({
                groupName: '',
                adTitle: '',
                adDescription: '',
                adFile: null,
                adType: 'image',
                startDate: '',
                endDate: '',
                status: 'Active',
                priority: 1
            });
            setUploadProgress(0);
        } catch (error) {
            setError('Failed to save header ad');
        } finally {
            setLoading(false);
        }
    };

    const getStatusBadgeColor = (status) => {
        switch (status) {
            case 'Active': return 'success';
            case 'Inactive': return 'secondary';
            case 'Expired': return 'danger';
            default: return 'secondary';
        }
    };

    const getAdTypeIcon = (type) => {
        switch (type) {
            case 'image': return 'fas fa-image';
            case 'video': return 'fas fa-video';
            case 'gif': return 'fas fa-file-image';
            case 'banner': return 'fas fa-rectangle-ad';
            default: return 'fas fa-file';
        }
    };

    return (
        <Container fluid className="header-ads-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Header Ads Management</h4>
                <Button variant="primary" onClick={handleCreate}>
                    <i className="fas fa-plus me-2"></i>Create Header Ad
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{headerAds.length}</h3>
                            <p className="mb-0">Total Ads</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {headerAds.filter(ad => ad.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Ads</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {headerAds.reduce((sum, ad) => sum + ad.views, 0).toLocaleString()}
                            </h3>
                            <p className="mb-0">Total Views</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                {headerAds.reduce((sum, ad) => sum + ad.clicks, 0)}
                            </h3>
                            <p className="mb-0">Total Clicks</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-ad me-2"></i>Header Advertisements
                    </h5>
                </Card.Header>
                <Card.Body>
                    {headerAds.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-ad fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Header Ads</h5>
                            <p className="text-muted">Create your first header advertisement</p>
                            <Button variant="primary" onClick={handleCreate}>
                                Create First Ad
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Group</th>
                                    <th>Ad Title</th>
                                    <th>Type</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Performance</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {headerAds.map((ad, index) => (
                                    <tr key={ad.id}>
                                        <td>{index + 1}</td>
                                        <td>
                                            <Badge bg="info">{ad.groupName}</Badge>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{ad.adTitle}</strong>
                                                <br />
                                                <small className="text-muted">{ad.adDescription}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <i className={`${getAdTypeIcon(ad.adType)} me-1`}></i>
                                            {ad.adType}
                                        </td>
                                        <td>
                                            <small>
                                                {ad.startDate} to<br />
                                                {ad.endDate}
                                            </small>
                                        </td>
                                        <td>
                                            <Badge 
                                                bg={getStatusBadgeColor(ad.status)}
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => toggleAdStatus(ad.id)}
                                            >
                                                {ad.status}
                                            </Badge>
                                        </td>
                                        <td>
                                            <Badge bg="secondary">#{ad.priority}</Badge>
                                        </td>
                                        <td>
                                            <small>
                                                <div>Views: {ad.views.toLocaleString()}</div>
                                                <div>Clicks: {ad.clicks}</div>
                                                <div>CTR: {ad.views > 0 ? ((ad.clicks / ad.views) * 100).toFixed(2) : 0}%</div>
                                            </small>
                                        </td>
                                        <td>
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleEdit(ad)}
                                                title="Edit Ad"
                                            >
                                                <i className="fas fa-edit"></i>
                                            </Button>
                                            <Button
                                                variant="outline-danger"
                                                size="sm"
                                                onClick={() => handleDelete(ad.id)}
                                                title="Delete Ad"
                                            >
                                                <i className="fas fa-trash"></i>
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Create/Edit Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-ad me-2"></i>
                        {editingAd ? 'Edit Header Ad' : 'Create Header Ad'}
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Group Name *</Form.Label>
                                    <Form.Select
                                        value={formData.groupName}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            groupName: e.target.value
                                        })}
                                        required
                                    >
                                        <option value="">Select Group</option>
                                        {groups.map(group => (
                                            <option key={group.id} value={group.id}>
                                                {group.groupName} ({group.groupType})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Type *</Form.Label>
                                    <Form.Select
                                        value={formData.adType}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adType: e.target.value
                                        })}
                                        required
                                    >
                                        {adTypes.map(type => (
                                            <option key={type} value={type}>
                                                {type.charAt(0).toUpperCase() + type.slice(1)}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Title *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter ad title"
                                        value={formData.adTitle}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adTitle: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Description</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={3}
                                        placeholder="Enter ad description"
                                        value={formData.adDescription}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adDescription: e.target.value
                                        })}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad File *</Form.Label>
                                    <Form.Control
                                        type="file"
                                        accept={
                                            formData.adType === 'video' ? 'video/*' :
                                            formData.adType === 'gif' ? 'image/gif' : 'image/*'
                                        }
                                        onChange={handleFileChange}
                                        required={!editingAd}
                                    />
                                    <Form.Text className="text-muted">
                                        Max file size: 10MB. Supported formats: {
                                            formData.adType === 'video' ? 'MP4, WebM, OGG' :
                                            formData.adType === 'gif' ? 'GIF' : 'JPEG, PNG, WebP'
                                        }
                                    </Form.Text>
                                    {uploadProgress > 0 && uploadProgress < 100 && (
                                        <ProgressBar 
                                            now={uploadProgress} 
                                            label={`${uploadProgress}%`}
                                            className="mt-2"
                                        />
                                    )}
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Start Date *</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={formData.startDate}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            startDate: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>End Date *</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={formData.endDate}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            endDate: e.target.value
                                        })}
                                        required
                                        min={formData.startDate}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Priority</Form.Label>
                                    <Form.Control
                                        type="number"
                                        min="1"
                                        max="10"
                                        value={formData.priority}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            priority: parseInt(e.target.value)
                                        })}
                                    />
                                    <Form.Text className="text-muted">
                                        1 = Highest priority, 10 = Lowest priority
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Status</Form.Label>
                                    <Form.Select
                                        value={formData.status}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            status: e.target.value
                                        })}
                                    >
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    {uploadProgress > 0 ? `Uploading... ${uploadProgress}%` : 'Processing...'}
                                </>
                            ) : (
                                <>
                                    <i className={`fas ${editingAd ? 'fa-save' : 'fa-plus'} me-2`}></i>
                                    {editingAd ? 'Update Ad' : 'Create Ad'}
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default HeaderAdsManagement;
