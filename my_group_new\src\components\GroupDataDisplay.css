/* GroupDataDisplay.css */
.group-data-display {
  padding: 20px 15px;
  background-color: #f8f9fa;
  min-height: calc(100vh - 120px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #666;
}

.group-section {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.group-main-logo {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.group-details {
  flex: 1;
}

.group-title {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.group-subtitle {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.group-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.group-badge-icon {
  font-size: 1.5rem;
}

.categories-grid {
  margin: 0 -10px;
}

.categories-grid .col-6,
.categories-grid .col-sm-4,
.categories-grid .col-md-3 {
  padding: 0 10px;
}

.category-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.category-card .card-body {
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
}

.category-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.category-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 5px;
  text-align: center;
  color: #333;
  line-height: 1.2;
}

.category-description {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
  text-align: center;
}

.no-categories {
  text-align: center;
  padding: 30px 0;
}

.no-groups {
  background: white;
  border-radius: 15px;
  margin: 20px 0;
}

/* Dark theme styles */
[data-theme="dark"] .group-data-display {
  background-color: #1a1a1a;
}

[data-theme="dark"] .group-section {
  background: #2c2c2c;
  color: white;
}

[data-theme="dark"] .group-title {
  color: white;
}

[data-theme="dark"] .group-subtitle {
  color: #ccc;
}

[data-theme="dark"] .category-card {
  background: #3c3c3c;
  color: white;
}

[data-theme="dark"] .category-title {
  color: white;
}

[data-theme="dark"] .category-description {
  color: #ccc;
}

[data-theme="dark"] .no-groups {
  background: #2c2c2c;
  color: white;
}

[data-theme="dark"] .loading-container {
  color: #ccc;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .group-data-display {
    padding: 15px 10px;
  }
  
  .group-section {
    padding: 15px;
    border-radius: 12px;
  }
  
  .group-header {
    margin-bottom: 15px;
    padding-bottom: 12px;
  }
  
  .group-info {
    gap: 10px;
  }
  
  .group-main-logo {
    width: 40px;
    height: 40px;
  }
  
  .group-title {
    font-size: 1.1rem;
  }
  
  .group-subtitle {
    font-size: 0.8rem;
  }
  
  .group-badge {
    width: 35px;
    height: 35px;
  }
  
  .group-badge-icon {
    font-size: 1.2rem;
  }
  
  .category-card .card-body {
    padding: 15px 10px;
    min-height: 100px;
  }
  
  .category-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }
  
  .category-title {
    font-size: 0.8rem;
  }
  
  .category-description {
    font-size: 0.7rem;
  }
}
