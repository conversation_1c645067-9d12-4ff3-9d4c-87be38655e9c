/* Register.css */
.register-form {
  max-width: 100%;
}

.register-form .form-label {
  color: #333;
  margin-bottom: 5px;
}

.register-form .input-group-text {
  background: #f8f9fa;
  border-right: none;
  color: #666;
}

.register-form .form-control {
  border-left: none;
  padding-left: 0;
}

.register-form .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.register-form .input-group:focus-within .input-group-text {
  border-color: #667eea;
  background: #f0f2ff;
}

.register-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.password-toggle {
  border-left: none;
  background: transparent;
  color: #666;
}

.password-toggle:hover {
  background: #f8f9fa;
  color: #333;
}

.register-form .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.register-form .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.register-form .btn-link {
  color: #667eea;
  text-decoration: none;
}

.register-form .btn-link:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

.register-form .form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

/* Dark theme */
[data-theme="dark"] .register-form .form-label {
  color: #ccc;
}

[data-theme="dark"] .register-form .input-group-text {
  background: #3c3c3c;
  color: #ccc;
  border-color: #555;
}

[data-theme="dark"] .register-form .form-control,
[data-theme="dark"] .register-form .form-select {
  background: #3c3c3c;
  color: white;
  border-color: #555;
}

[data-theme="dark"] .register-form .form-control:focus,
[data-theme="dark"] .register-form .form-select:focus {
  background: #3c3c3c;
  color: white;
  border-color: #667eea;
}

[data-theme="dark"] .register-form .input-group:focus-within .input-group-text {
  background: #4a4a4a;
  border-color: #667eea;
}

[data-theme="dark"] .password-toggle {
  background: transparent;
  color: #ccc;
  border-color: #555;
}

[data-theme="dark"] .password-toggle:hover {
  background: #4a4a4a;
  color: white;
}

/* Responsive */
@media (max-width: 576px) {
  .register-form .form-label {
    font-size: 0.9rem;
  }
  
  .register-form .form-control,
  .register-form .form-select {
    font-size: 0.9rem;
  }
  
  .register-form .btn {
    font-size: 0.9rem;
  }
  
  .register-form .row .col-12 {
    margin-bottom: 0;
  }
}
