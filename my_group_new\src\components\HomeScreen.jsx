import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import Header from './Header';
import GroupDataDisplay from './GroupDataDisplay';
import { useAuth } from '../contexts/AuthContext';
import './HomeScreen.css';

const HomeScreen = ({ onShowLogin, onShowProfile }) => {
  const { isAuthenticated } = useAuth();
  const [groupData, setGroupData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const API_BASE_URL = 'http://localhost:5000/api';

  useEffect(() => {
    fetchGroupData();
  }, []);

  const fetchGroupData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/groups/with-categories`);
      
      if (response.ok) {
        const data = await response.json();
        setGroupData(data.groups || []);
      } else {
        throw new Error('Failed to fetch group data');
      }
    } catch (error) {
      console.error('Error fetching group data:', error);
      setError(error.message);
      // Set mock data for development
      setGroupData([
        {
          id: 1,
          name: 'My Group',
          logo: '/api/placeholder/50/50',
          icon: '🏢',
          background_color: '#007bff',
          color: '#ffffff',
          group_url: 'https://mygroup.com',
          group_name: 'My Group',
          categories: [
            { id: 1, name: 'News', icon: '📰', display_name: 'Latest News', end_point: '/news' },
            { id: 2, name: 'Events', icon: '📅', display_name: 'Upcoming Events', end_point: '/events' },
            { id: 3, name: 'Services', icon: '🛠️', display_name: 'Our Services', end_point: '/services' }
          ]
        },
        {
          id: 2,
          name: 'My Apps',
          logo: '/api/placeholder/50/50',
          icon: '📱',
          background_color: '#28a745',
          color: '#ffffff',
          group_url: 'https://myapps.com',
          group_name: 'My Apps',
          categories: [
            { id: 4, name: 'Mobile', icon: '📱', display_name: 'Mobile Apps', end_point: '/mobile' },
            { id: 5, name: 'Web', icon: '🌐', display_name: 'Web Apps', end_point: '/web' },
            { id: 6, name: 'Desktop', icon: '💻', display_name: 'Desktop Apps', end_point: '/desktop' }
          ]
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleLoginRequired = () => {
    if (!isAuthenticated) {
      onShowLogin();
      return false;
    }
    return true;
  };

  return (
    <div className="home-screen">
      <Header 
        onShowLogin={onShowLogin}
        onShowProfile={onShowProfile}
        groupData={groupData}
        onLoginRequired={handleLoginRequired}
      />
      
      <Container fluid className="main-content">
        <Row>
          <Col>
            <GroupDataDisplay 
              groupData={groupData}
              loading={loading}
              error={error}
              onLoginRequired={handleLoginRequired}
            />
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default HomeScreen;
