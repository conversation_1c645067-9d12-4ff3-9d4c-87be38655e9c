const jwt = require('jsonwebtoken');
const User = require('../models/User');
const UserDetails = require('../models/UserDetails');

// Generate JWT token
const generateToken = (userId, role) => {
  return jwt.sign(
    { userId, role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

// Register new user
const register = async (req, res) => {
  try {
    const {
      user_name,
      password,
      email,
      mobile_number,
      role_id = 6, // Default to customer role
      group_id = 1, // Default group
      // User details
      first_name,
      last_name,
      middle_name = '',
      display_name,
      gender,
      date_of_birth,
      coutry_id = 1,
      state_id = 1,
      district_id = 1,
      nationality = '',
      education = 1,
      profession = 1,
      language_id = 1
    } = req.body;

    // Get client IP address
    const ip_address = req.ip || req.connection.remoteAddress || '127.0.0.1';

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists with this email' });
    }

    // Create user
    const userData = {
      user_name,
      password,
      role_id,
      group_id,
      mobile_number,
      email,
      ip_address
    };

    const newUser = await User.create(userData);

    // Create user details if provided
    if (first_name && last_name) {
      const userDetailsData = {
        first_name,
        middle_name,
        last_name,
        user_id: newUser.id,
        display_name: display_name || `${first_name} ${last_name}`,
        gender,
        martial_status: 'Single', // Default
        coutry_id,
        state_id,
        district_id,
        nationality,
        education,
        profession,
        photo: '', // Default empty
        date_of_birth,
        language_id
      };

      await UserDetails.create(userDetailsData);
    }

    // Generate token
    const token = generateToken(newUser.id, newUser.role_id);

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: newUser.id,
        user_name: newUser.user_name,
        email: newUser.email,
        mobile_number: newUser.mobile_number,
        role_id: newUser.role_id
      },
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { email, password } = req.body;
    const ip_address = req.ip || req.connection.remoteAddress || '127.0.0.1';

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Verify password
    const isValidPassword = await User.verifyPassword(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Update last login
    await User.updateLastLogin(user.id, ip_address);

    // Generate token
    const token = generateToken(user.id, user.role_id);

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        user_name: user.user_name,
        email: user.email,
        mobile_number: user.mobile_number,
        role_id: user.role_id,
        role: user.role,
        group_id: user.group_id
      },
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get current user profile
const getProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const profile = await UserDetails.getCompleteProfile(userId);
    if (!profile) {
      return res.status(404).json({ message: 'User profile not found' });
    }

    res.json({
      message: 'Profile retrieved successfully',
      profile
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Verify token
const verifyToken = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      message: 'Token is valid',
      user: {
        id: user.id,
        user_name: user.user_name,
        email: user.email,
        role_id: user.role_id,
        role: user.role,
        group_id: user.group_id
      }
    });

  } catch (error) {
    console.error('Verify token error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  verifyToken
};
