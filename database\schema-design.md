# Multi-Role Authentication Database Schema Design

## Overview
This schema design supports a hierarchical multi-role authentication system with the following user types:
- **Admin** (Super Admin)
- **Corporate** (Corporate Manager)
- **Head Office** (Head Office Manager)
- **Regional** (Regional Manager)
- **Branch** (Branch Manager)

## Schema Design Principles
1. **Hierarchical Structure**: Clear organizational hierarchy with proper relationships
2. **Role-based Access Control**: Flexible permission system
3. **Scalability**: Support for multiple organizations, regions, and branches
4. **Security**: Proper password hashing and session management
5. **Audit Trail**: Track user activities and changes

## Core Tables

### 1. my_roles
Defines all available roles in the system.

```sql
CREATE TABLE my_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    hierarchy_level INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. my_organizations
Stores corporate/organization information.

```sql
CREATE TABLE my_organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_name VARCHAR(255) NOT NULL,
    organization_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. my_head_offices
Stores head office information under organizations.

```sql
CREATE TABLE my_head_offices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    office_name VARCHAR(255) NOT NULL,
    office_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES my_organizations(id) ON DELETE CASCADE
);
```

### 4. my_regional_offices
Stores regional office information under head offices.

```sql
CREATE TABLE my_regional_offices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    head_office_id INT NOT NULL,
    region_name VARCHAR(255) NOT NULL,
    region_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (head_office_id) REFERENCES my_head_offices(id) ON DELETE CASCADE
);
```

### 5. my_branch_offices
Stores branch office information under regional offices.

```sql
CREATE TABLE my_branch_offices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    regional_office_id INT NOT NULL,
    branch_name VARCHAR(255) NOT NULL,
    branch_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    services_offered JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (regional_office_id) REFERENCES my_regional_offices(id) ON DELETE CASCADE
);
```

### 6. my_users
Central user table for all user types.

```sql
CREATE TABLE my_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(255) GENERATED ALWAYS AS (CONCAT(first_name, ' ', last_name)) STORED,
    mobile_number VARCHAR(20),
    role_id INT NOT NULL,
    organization_id INT NULL,
    head_office_id INT NULL,
    regional_office_id INT NULL,
    branch_office_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES my_roles(id),
    FOREIGN KEY (organization_id) REFERENCES my_organizations(id) ON DELETE SET NULL,
    FOREIGN KEY (head_office_id) REFERENCES my_head_offices(id) ON DELETE SET NULL,
    FOREIGN KEY (regional_office_id) REFERENCES my_regional_offices(id) ON DELETE SET NULL,
    FOREIGN KEY (branch_office_id) REFERENCES my_branch_offices(id) ON DELETE SET NULL
);
```

### 7. my_permissions
Defines available permissions in the system.

```sql
CREATE TABLE my_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    permission_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 8. my_role_permissions
Maps roles to permissions.

```sql
CREATE TABLE my_role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES my_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES my_permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role_id, permission_id)
);
```

### 9. my_user_sessions
Tracks user login sessions.

```sql
CREATE TABLE my_user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES my_users(id) ON DELETE CASCADE
);
```

### 10. my_audit_logs
Tracks user activities and system changes.

```sql
CREATE TABLE my_audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES my_users(id) ON DELETE SET NULL
);
```

## Indexes for Performance

```sql
-- User authentication indexes
CREATE INDEX idx_users_username ON my_users(username);
CREATE INDEX idx_users_email ON my_users(email);
CREATE INDEX idx_users_role_id ON my_users(role_id);
CREATE INDEX idx_users_active ON my_users(is_active);

-- Organizational hierarchy indexes
CREATE INDEX idx_users_organization ON my_users(organization_id);
CREATE INDEX idx_users_head_office ON my_users(head_office_id);
CREATE INDEX idx_users_regional_office ON my_users(regional_office_id);
CREATE INDEX idx_users_branch_office ON my_users(branch_office_id);

-- Session management indexes
CREATE INDEX idx_sessions_token ON my_user_sessions(session_token);
CREATE INDEX idx_sessions_user_active ON my_user_sessions(user_id, is_active);
CREATE INDEX idx_sessions_expires ON my_user_sessions(expires_at);

-- Audit log indexes
CREATE INDEX idx_audit_user_id ON my_audit_logs(user_id);
CREATE INDEX idx_audit_action ON my_audit_logs(action);
CREATE INDEX idx_audit_created_at ON my_audit_logs(created_at);
```

## Data Relationships

### Hierarchical Structure:
```
Organization (Corporate)
    └── Head Office
        └── Regional Office
            └── Branch Office
```

### User Role Mapping:
- **Admin**: No organizational constraints (system-wide access)
- **Corporate**: Linked to specific organization
- **Head Office**: Linked to specific head office under organization
- **Regional**: Linked to specific regional office under head office
- **Branch**: Linked to specific branch office under regional office

## Security Considerations

1. **Password Security**: Use bcrypt or similar for password hashing
2. **Session Management**: Implement secure session tokens with expiration
3. **Account Lockout**: Prevent brute force attacks with failed login tracking
4. **Audit Trail**: Log all critical actions for security monitoring
5. **Role Validation**: Ensure users can only access resources within their hierarchy

## Sample Data Structure

### Roles:
1. ADMIN (hierarchy_level: 1)
2. CORPORATE (hierarchy_level: 2)
3. HEAD_OFFICE (hierarchy_level: 3)
4. REGIONAL (hierarchy_level: 4)
5. BRANCH (hierarchy_level: 5)

### Permissions:
- user.create, user.read, user.update, user.delete
- organization.manage, office.manage, branch.manage
- transaction.process, report.generate, etc.

This schema provides a robust foundation for the multi-role authentication system with proper hierarchical relationships and security features.
