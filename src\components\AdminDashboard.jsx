import React, { useState } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Badge, Modal, Alert } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import CorporateLoginManagement from './CorporateLoginManagement';
import '../styles/AdminDashboard.css';

const AdminDashboard = () => {
  const { currentUser, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'continent', 'country', 'state', 'district'
  const [formData, setFormData] = useState({});
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Mock data for continents, countries, states, districts
  const [continents, setContinents] = useState([
   
  ]);

  const [countries, setCountries] = useState([
   
  ]);

  const [states, setStates] = useState([
   
  ]);

  const [districts, setDistricts] = useState([
   
  ]);

  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    {
      id: 'profile',
      label: 'Profile',
      icon: 'fas fa-user-cog',
      hasSubmenu: true,
      submenu: [
        { id: 'profile-groups', label: 'Groups Management' },
        { id: 'profile-password', label: 'Change Password' }
      ]
    },
    { id: 'locations', label: 'Locations', icon: 'fas fa-map-marker-alt' },
    { id: 'languages', label: 'Languages', icon: 'fas fa-language' },
    { id: 'education', label: 'Education', icon: 'fas fa-graduation-cap' },
    { id: 'profession', label: 'Profession', icon: 'fas fa-briefcase' },
    { id: 'categories', label: 'Categories', icon: 'fas fa-layer-group' },
    { id: 'corporate-login', label: 'Corporate Login', icon: 'fas fa-users' },
    { id: 'database-test', label: 'Database Integration Test', icon: 'fas fa-database' },
    { id: 'logout', label: 'Logout', icon: 'fas fa-sign-out-alt' }
  ];
 

  const handleSidebarClick = (itemId) => {
    if (itemId === 'logout') {
      logout();
      return;
    }
    setActiveSection(itemId);

  };

  const handleAddNew = (type) => {
    setModalType(type);
    setFormData({});
    setShowModal(true);
    setError('');
    setSuccess('');
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    
    const newItem = {
      id: Date.now(),
      ...formData,
      order: 0,
      status: true
    };

    switch (modalType) {
      case 'continent':
        setContinents([...continents, newItem]);
        break;
      case 'country':
        setCountries([...countries, newItem]);
        break;
      case 'state':
        setStates([...states, newItem]);
        break;
      case 'district':
        setDistricts([...districts, newItem]);
        break;
    }

    setSuccess(`${modalType} added successfully!`);
    setShowModal(false);
    setFormData({});
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };



  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h2>Admin Dashboard</h2>
              <Badge bg="primary">Administrator</Badge>
            </div>

            <Row className="mb-4">
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-layer-group fa-2x text-primary mb-2"></i>
                    <h3 className="text-primary">4</h3>
                    <p className="mb-0">Groups</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-map-marker-alt fa-2x text-success mb-2"></i>
                    <h3 className="text-success">{countries.length}</h3>
                    <p className="mb-0">Countries</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-language fa-2x text-info mb-2"></i>
                    <h3 className="text-info">12</h3>
                    <p className="mb-0">Languages</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-users fa-2x text-warning mb-2"></i>
                    <h3 className="text-warning">8</h3>
                    <p className="mb-0">Corporate Users</p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-chart-line me-2"></i>Quick Stats
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Education Levels:</span>
                      <Badge bg="secondary">15</Badge>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Professions:</span>
                      <Badge bg="secondary">25</Badge>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Categories:</span>
                      <Badge bg="secondary">18</Badge>
                    </div>
                    <div className="d-flex justify-content-between">
                      <span>Active Users:</span>
                      <Badge bg="success">156</Badge>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-tasks me-2"></i>Recent Activities
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="activity-item mb-2">
                      <small className="text-muted">2 hours ago</small>
                      <div>New corporate user created</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">5 hours ago</small>
                      <div>Category structure updated</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">1 day ago</small>
                      <div>New language added</div>
                    </div>
                    <div className="activity-item">
                      <small className="text-muted">2 days ago</small>
                      <div>Profile group created</div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </div>
        );

      case 'corporate-login':
        return <CorporateLoginManagement />;

      default:
        return (
          <div className="text-center py-5">
            <i className="fas fa-cog fa-3x text-muted mb-3"></i>
            <h3>Welcome to Admin Dashboard</h3>
            <p className="text-muted">Select a menu item to get started</p>
          </div>
        );
    }
  };

  return (
    <div className="admin-dashboard">
      <div className="d-flex">
        {/* Sidebar */}
        <div className="admin-sidebar">
          <div className="sidebar-header">
            <h4>admin</h4>
          </div>
          <div className="sidebar-menu">
            {sidebarItems.map(item => (
              <div key={item.id}>
                <div
                  className={`menu-item ${activeSection === item.id || activeSection.startsWith(item.id) ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <i className={`${item.icon} me-2`}></i>
                  <span className="menu-label">{item.label}</span>
                  {item.hasSubmenu && <span className="submenu-arrow">▼</span>}
                </div>
                {item.hasSubmenu && item.submenu && (
                  <div className="submenu">
                    {item.submenu.map(subItem => (
                      <div
                        key={subItem.id}
                        className={`submenu-item ${activeSection === subItem.id ? 'active' : ''}`}
                        onClick={() => handleSidebarClick(subItem.id)}
                      >
                        <span className="submenu-label">{subItem.label}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="admin-content">
          <div className="content-header">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <span>Dashboard</span>
                <span className="mx-2">|</span>
                <span>Document</span>
              </div>
              <div className="d-flex align-items-center">
                <NotificationSystem
                  userId={currentUser?.id}
                  userRole={currentUser?.role}
                />
                <span className="ms-3">Welcome, {currentUser?.name || currentUser?.username}</span>
              </div>
            </div>
          </div>
          
          <div className="content-body">
            {success && <Alert variant="success" dismissible onClose={() => setSuccess('')}>{success}</Alert>}
            {error && <Alert variant="danger" dismissible onClose={() => setError('')}>{error}</Alert>}
            
            {renderContent()}
          </div>
        </div>
      </div>

      {/* Add Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add {modalType}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleFormSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>{modalType} Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={formData.name || ''}
                    onChange={handleInputChange}
                    placeholder={`Enter ${modalType} name`}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Code</Form.Label>
                  <Form.Control
                    type="text"
                    name="code"
                    value={formData.code || ''}
                    onChange={handleInputChange}
                    placeholder={`Enter ${modalType} code`}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            
            {modalType === 'country' && (
              <Form.Group className="mb-3">
                <Form.Label>Continent</Form.Label>
                <Form.Select
                  name="continentId"
                  value={formData.continentId || ''}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select Continent</option>
                  {continents.map(continent => (
                    <option key={continent.id} value={continent.id}>
                      {continent.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            )}
            
            {modalType === 'state' && (
              <Form.Group className="mb-3">
                <Form.Label>Country</Form.Label>
                <Form.Select
                  name="countryId"
                  value={formData.countryId || ''}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select Country</option>
                  {countries.map(country => (
                    <option key={country.id} value={country.id}>
                      {country.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            )}
            
            {modalType === 'district' && (
              <Form.Group className="mb-3">
                <Form.Label>State</Form.Label>
                <Form.Select
                  name="stateId"
                  value={formData.stateId || ''}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select State</option>
                  {states.map(state => (
                    <option key={state.id} value={state.id}>
                      {state.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleFormSubmit}>
            Submit
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AdminDashboard;
