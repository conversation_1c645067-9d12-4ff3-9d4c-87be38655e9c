const pool = require('../config/database');
const bcrypt = require('bcryptjs');

async function createAdminUser() {
  try {
    console.log('Creating default admin user...');
    
    // Check if admin user already exists
    const existingAdmin = await pool.query(
      'SELECT * FROM my_users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (existingAdmin.rows.length > 0) {
      console.log('Admin user already exists!');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
      return;
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Create admin user
    const adminUser = await pool.query(`
      INSERT INTO my_users (
        user_name, password, role_id, group_id, mobile_number, email, 
        ip_address, created_on, modified_on, last_logged_in, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW(), NOW(), 1)
      RETURNING id, user_name, email, role_id
    `, [
      'admin',
      hashedPassword,
      1, // Admin role
      1, // Default group
      '1234567890',
      '<EMAIL>',
      '127.0.0.1'
    ]);
    
    // Create admin user details
    await pool.query(`
      INSERT INTO my_user_details (
        first_name, middle_name, last_name, user_id, display_name, gender,
        martial_status, coutry_id, state_id, district_id, nationality,
        education, profession, photo, date_of_birth, language_id,
        created_at, update_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW())
    `, [
      'Admin',
      '',
      'User',
      adminUser.rows[0].id,
      'Admin User',
      'Other',
      'Single',
      1, // Default country
      1, // Default state
      1, // Default district
      'System',
      1, // Default education
      1, // Default profession
      '',
      '1990-01-01',
      1 // Default language
    ]);
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role: Admin (role_id: 1)');
    console.log('');
    console.log('You can now login with these credentials to access the admin dashboard.');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    process.exit(0);
  }
}

createAdminUser();
