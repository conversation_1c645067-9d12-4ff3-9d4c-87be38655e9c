/* App.css */
.App {
  min-height: 100vh;
}

.mobile-app {
  max-width: 100%;
  overflow-x: hidden;
}

.desktop-not-available {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.desktop-not-available h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.desktop-not-available p {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .mobile-app {
    padding: 0;
  }
}

/* Theme variables */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --black: #000000;
}

/* Dark theme */
[data-theme="dark"] {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  --light-color: #212529;
  --dark-color: #f8f9fa;
  --white: #000000;
  --black: #ffffff;
}

/* Authentication screens */
.auth-required-screen {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.auth-required-screen h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.auth-required-screen p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.auth-required-screen .btn {
  border-radius: 25px;
  padding: 12px 30px;
  font-weight: 500;
  font-size: 1.1rem;
  border: 2px solid white;
  background: transparent;
  color: white;
  transition: all 0.3s ease;
}

.auth-required-screen .btn:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.loading-screen {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.access-denied {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.access-denied h3 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.access-denied p {
  color: #666;
  margin-bottom: 2rem;
}

/* Global styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}
