import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge } from 'react-bootstrap';

const HeadOfficeManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState(''); // 'create', 'edit', 'resetPassword'
    const [editingUser, setEditingUser] = useState(null);

    // Form data
    const [formData, setFormData] = useState({
        country: '',
        name: '',
        mobileNumber: '',
        email: '',
        username: '',
        password: '',
        confirmPassword: '',
        status: 'Active'
    });

    // Head office users data
    const [headOfficeUsers, setHeadOfficeUsers] = useState([
        {
            id: 1,
            country: 'India',
            countryId: 1,
            name: '<PERSON><PERSON>',
            mobileNumber: '+91-9876543210',
            email: 'raj<PERSON>.<EMAIL>',
            username: 'rajeshho',
            status: 'Active',
            createdAt: '2024-01-15',
            lastLogin: '2024-01-20 09:30 AM'
        },
        {
            id: 2,
            country: 'United States',
            countryId: 2,
            name: 'John Smith',
            mobileNumber: '******-0123',
            email: '<EMAIL>',
            username: 'johnho',
            status: 'Active',
            createdAt: '2024-01-10',
            lastLogin: '2024-01-19 02:15 PM'
        },
        {
            id: 3,
            country: 'United Kingdom',
            countryId: 3,
            name: 'Emma Wilson',
            mobileNumber: '+44-20-7946-0958',
            email: '<EMAIL>',
            username: 'emmahouk',
            status: 'Inactive',
            createdAt: '2024-01-08',
            lastLogin: '2024-01-18 11:45 AM'
        }
    ]);

    // Countries data (from Location API)
    const [countries, setCountries] = useState([
        { id: 1, name: 'India', code: 'IN' },
        { id: 2, name: 'United States', code: 'US' },
        { id: 3, name: 'United Kingdom', code: 'UK' },
        { id: 4, name: 'Canada', code: 'CA' },
        { id: 5, name: 'Australia', code: 'AU' },
        { id: 6, name: 'Germany', code: 'DE' },
        { id: 7, name: 'France', code: 'FR' },
        { id: 8, name: 'Japan', code: 'JP' }
    ]);

    useEffect(() => {
        fetchHeadOfficeUsers();
        fetchCountriesData();
    }, []);

    const fetchHeadOfficeUsers = async () => {
        setLoading(true);
        try {
            // API call to fetch head office users
            // const response = await fetch('/api/head-office-users');
            // const data = await response.json();
            // setHeadOfficeUsers(data);
        } catch (error) {
            console.error('Error fetching head office users:', error);
            setError('Failed to fetch head office users');
        } finally {
            setLoading(false);
        }
    };

    const fetchCountriesData = async () => {
        try {
            // API call to fetch countries
            // const response = await fetch('/api/countries');
            // const data = await response.json();
            // setCountries(data);
        } catch (error) {
            console.error('Error fetching countries:', error);
        }
    };

    const handleCreate = () => {
        setModalType('create');
        setEditingUser(null);
        setFormData({
            country: '',
            name: '',
            mobileNumber: '',
            email: '',
            username: '',
            password: '',
            confirmPassword: '',
            status: 'Active'
        });
        setShowModal(true);
    };

    const handleEdit = (user) => {
        setModalType('edit');
        setEditingUser(user);
        setFormData({
            country: user.countryId,
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: '',
            status: user.status
        });
        setShowModal(true);
    };

    const handleResetPassword = (user) => {
        setModalType('resetPassword');
        setEditingUser(user);
        setFormData({
            country: user.countryId,
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: '',
            status: user.status
        });
        setShowModal(true);
    };

    const toggleUserStatus = (userId) => {
        setHeadOfficeUsers(headOfficeUsers.map(user => 
            user.id === userId 
                ? { ...user, status: user.status === 'Active' ? 'Inactive' : 'Active' }
                : user
        ));
        setSuccess('User status updated successfully');
    };

    const handleDelete = (userId) => {
        if (window.confirm('Are you sure you want to delete this head office user?')) {
            setHeadOfficeUsers(headOfficeUsers.filter(user => user.id !== userId));
            setSuccess('Head office user deleted successfully');
        }
    };

    const validateForm = () => {
        if (modalType === 'create' || modalType === 'resetPassword') {
            if (formData.password !== formData.confirmPassword) {
                setError('Passwords do not match');
                return false;
            }
            if (formData.password.length < 6) {
                setError('Password must be at least 6 characters long');
                return false;
            }
        }

        if (!formData.email.includes('@')) {
            setError('Please enter a valid email address');
            return false;
        }

        if (formData.mobileNumber.length < 10) {
            setError('Please enter a valid mobile number');
            return false;
        }

        return true;
    };

    const sendEmailNotification = async (user, credentials = null) => {
        try {
            // Mock email sending
            console.log('Sending email to:', user.email);
            if (credentials) {
                console.log('Username:', credentials.username);
                console.log('Password:', credentials.password);
            }
            // In real implementation, call email API here
        } catch (error) {
            console.error('Error sending email:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const selectedCountry = countries.find(c => c.id === parseInt(formData.country));
            
            if (modalType === 'create') {
                const newUser = {
                    id: Date.now(),
                    country: selectedCountry?.name || '',
                    countryId: parseInt(formData.country),
                    name: formData.name,
                    mobileNumber: formData.mobileNumber,
                    email: formData.email,
                    username: formData.username,
                    status: formData.status,
                    createdAt: new Date().toISOString().split('T')[0],
                    lastLogin: 'Never'
                };

                setHeadOfficeUsers([...headOfficeUsers, newUser]);
                setSuccess('Head office user created successfully');

                // Send email with credentials
                await sendEmailNotification(newUser, {
                    username: formData.username,
                    password: formData.password
                });

            } else if (modalType === 'edit') {
                setHeadOfficeUsers(headOfficeUsers.map(user => 
                    user.id === editingUser.id 
                        ? {
                            ...user,
                            country: selectedCountry?.name || user.country,
                            countryId: parseInt(formData.country),
                            name: formData.name,
                            mobileNumber: formData.mobileNumber,
                            email: formData.email,
                            username: formData.username,
                            status: formData.status
                        }
                        : user
                ));
                setSuccess('Head office user updated successfully');

            } else if (modalType === 'resetPassword') {
                setSuccess('Password reset successfully. New credentials sent to email.');
                
                // Send email with new credentials
                await sendEmailNotification(editingUser, {
                    username: formData.username,
                    password: formData.password
                });
            }

            setShowModal(false);
            setFormData({
                country: '',
                name: '',
                mobileNumber: '',
                email: '',
                username: '',
                password: '',
                confirmPassword: '',
                status: 'Active'
            });
        } catch (error) {
            setError('Failed to save head office user');
        } finally {
            setLoading(false);
        }
    };

    const getModalTitle = () => {
        switch (modalType) {
            case 'create': return 'Create Head Office User';
            case 'edit': return 'Edit Head Office User';
            case 'resetPassword': return 'Reset Password';
            default: return 'Head Office User';
        }
    };

    return (
        <Container fluid className="head-office-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Head Office Login Management</h4>
                <Button variant="primary" onClick={handleCreate}>
                    <i className="fas fa-plus me-2"></i>Create Head Office User
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{headOfficeUsers.length}</h3>
                            <p className="mb-0">Total Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {headOfficeUsers.filter(user => user.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                {headOfficeUsers.filter(user => user.status === 'Inactive').length}
                            </h3>
                            <p className="mb-0">Inactive Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {new Set(headOfficeUsers.map(user => user.country)).size}
                            </h3>
                            <p className="mb-0">Countries</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-building me-2"></i>Head Office Users
                    </h5>
                </Card.Header>
                <Card.Body>
                    {headOfficeUsers.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Head Office Users</h5>
                            <p className="text-muted">Create your first head office user to get started</p>
                            <Button variant="primary" onClick={handleCreate}>
                                Create First User
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Country</th>
                                    <th>Name</th>
                                    <th>Mobile Number</th>
                                    <th>Email</th>
                                    <th>Username</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {headOfficeUsers.map((user, index) => (
                                    <tr key={user.id}>
                                        <td>{index + 1}</td>
                                        <td>
                                            <Badge bg="info">{user.country}</Badge>
                                        </td>
                                        <td>
                                            <strong>{user.name}</strong>
                                        </td>
                                        <td>{user.mobileNumber}</td>
                                        <td>{user.email}</td>
                                        <td>
                                            <Badge bg="secondary">{user.username}</Badge>
                                        </td>
                                        <td>
                                            <Badge 
                                                bg={user.status === 'Active' ? 'success' : 'danger'}
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => toggleUserStatus(user.id)}
                                            >
                                                {user.status}
                                            </Badge>
                                        </td>
                                        <td>
                                            <small className="text-muted">{user.lastLogin}</small>
                                        </td>
                                        <td>
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleEdit(user)}
                                                title="Edit User"
                                            >
                                                <i className="fas fa-edit"></i>
                                            </Button>
                                            <Button
                                                variant="outline-warning"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleResetPassword(user)}
                                                title="Reset Password"
                                            >
                                                <i className="fas fa-key"></i>
                                            </Button>
                                            <Button
                                                variant="outline-danger"
                                                size="sm"
                                                onClick={() => handleDelete(user.id)}
                                                title="Delete User"
                                            >
                                                <i className="fas fa-trash"></i>
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Create/Edit/Reset Password Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-building me-2"></i>
                        {getModalTitle()}
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Country *</Form.Label>
                                    <Form.Select
                                        value={formData.country}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            country: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    >
                                        <option value="">Select Country</option>
                                        {countries.map(country => (
                                            <option key={country.id} value={country.id}>
                                                {country.name} ({country.code})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Full Name *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter full name"
                                        value={formData.name}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            name: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Mobile Number *</Form.Label>
                                    <Form.Control
                                        type="tel"
                                        placeholder="Enter mobile number"
                                        value={formData.mobileNumber}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            mobileNumber: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Email Address *</Form.Label>
                                    <Form.Control
                                        type="email"
                                        placeholder="Enter email address"
                                        value={formData.email}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            email: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Username *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter username"
                                        value={formData.username}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            username: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Status</Form.Label>
                                    <Form.Select
                                        value={formData.status}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            status: e.target.value
                                        })}
                                        disabled={modalType === 'resetPassword'}
                                    >
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        {(modalType === 'create' || modalType === 'resetPassword') && (
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>
                                            {modalType === 'resetPassword' ? 'New Password' : 'Password'} *
                                        </Form.Label>
                                        <Form.Control
                                            type="password"
                                            placeholder="Enter password"
                                            value={formData.password}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                password: e.target.value
                                            })}
                                            required
                                            minLength={6}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Confirm Password *</Form.Label>
                                        <Form.Control
                                            type="password"
                                            placeholder="Confirm password"
                                            value={formData.confirmPassword}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                confirmPassword: e.target.value
                                            })}
                                            required
                                            minLength={6}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        )}

                        <div className="bg-light p-3 rounded">
                            <small className="text-muted">
                                <i className="fas fa-info-circle me-1"></i>
                                <strong>Note:</strong> 
                                {modalType === 'create' && ' Login credentials will be sent to the provided email address.'}
                                {modalType === 'edit' && ' User information will be updated. Password remains unchanged.'}
                                {modalType === 'resetPassword' && ' New password will be sent to the user\'s email address.'}
                            </small>
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <i className={`fas ${modalType === 'create' ? 'fa-plus' : modalType === 'edit' ? 'fa-save' : 'fa-key'} me-2`}></i>
                                    {modalType === 'create' && 'Create User'}
                                    {modalType === 'edit' && 'Update User'}
                                    {modalType === 'resetPassword' && 'Reset Password'}
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default HeadOfficeManagement;
