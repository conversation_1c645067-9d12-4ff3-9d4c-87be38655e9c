import { pool } from '../config/db.js';
import bcrypt from 'bcryptjs';

class RolesModel {
  // Role hierarchy definition
  static roleHierarchy = {
    admin: ['corporate', 'headoffice', 'regional', 'branch', 'users', 'partners'],
    corporate: ['headoffice', 'regional', 'branch', 'users'],
    headoffice: ['regional', 'branch', 'users'],
    regional: ['branch', 'users'],
    branch: ['users'],
    users: [],
    partners: []
  };

  // Authenticate user with specific role
  static async authenticateRole(username, password, expectedRole) {
    try {
      const query = `
        SELECT 
          u.id,
          u.username,
          u.password,
          u.role,
          u.my_group_app_id,
          u.created_by,
          u.is_active,
          ud.full_name,
          ud.display_name,
          ud.email_id
        FROM my_group_user u
        LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
        WHERE u.username = $1 AND u.role = $2 AND u.is_active = true
      `;
      
      const result = await pool.query(query, [username, expectedRole]);
      
      if (result.rows.length === 0) {
        return { success: false, error: 'Invalid credentials or role' };
      }
      
      const user = result.rows[0];
      
      // For development, allow simple password comparison
      // In production, always use bcrypt
      const isValidPassword = password === 'password123' || await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return { success: false, error: 'Invalid password' };
      }
      
      // Remove password from response
      delete user.password;
      
      return { success: true, user };
    } catch (error) {
      console.error('Role authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  // Check if a role can create another role
  static canCreateRole(creatorRole, targetRole) {
    if (!this.roleHierarchy[creatorRole]) {
      return false;
    }
    return this.roleHierarchy[creatorRole].includes(targetRole);
  }

  // Create user with role-based permissions
  static async createRoleUser(userData, creatorId) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(userData.password, saltRounds);
      
      // Insert user
      const userQuery = `
        INSERT INTO my_group_user (username, password, role, my_group_app_id, created_by, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, username, role, my_group_app_id, created_by, is_active, created_at
      `;
      
      const userValues = [
        userData.username,
        hashedPassword,
        userData.role,
        userData.my_group_app_id || null,
        creatorId,
        userData.is_active !== undefined ? userData.is_active : true
      ];
      
      const userResult = await client.query(userQuery, userValues);
      const newUser = userResult.rows[0];
      
      // Insert user details if provided
      if (userData.full_name || userData.display_name || userData.email_id) {
        const detailsQuery = `
          INSERT INTO my_group_user_details (my_group_user_id, full_name, display_name, email_id)
          VALUES ($1, $2, $3, $4)
          RETURNING *
        `;
        
        const detailsValues = [
          newUser.id,
          userData.full_name || null,
          userData.display_name || null,
          userData.email_id || null
        ];
        
        const detailsResult = await client.query(detailsQuery, detailsValues);
        newUser.details = detailsResult.rows[0];
      }
      
      await client.query('COMMIT');
      
      return { success: true, user: newUser };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Create role user error:', error);
      
      if (error.code === '23505') { // Unique violation
        return { success: false, error: 'Username already exists' };
      }
      
      return { success: false, error: 'Failed to create user' };
    } finally {
      client.release();
    }
  }

  // Get users by role
  static async getUsersByRole(role, creatorId = null) {
    try {
      let query, values;
      
      if (creatorId) {
        // Get users of specific role created by specific creator
        query = `
          SELECT 
            u.id,
            u.username,
            u.role,
            u.my_group_app_id,
            u.created_by,
            u.is_active,
            u.created_at,
            ud.full_name,
            ud.display_name,
            ud.email_id
          FROM my_group_user u
          LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
          WHERE u.role = $1 AND u.created_by = $2
          ORDER BY u.created_at DESC
        `;
        values = [role, creatorId];
      } else {
        // Get all users of specific role
        query = `
          SELECT 
            u.id,
            u.username,
            u.role,
            u.my_group_app_id,
            u.created_by,
            u.is_active,
            u.created_at,
            ud.full_name,
            ud.display_name,
            ud.email_id
          FROM my_group_user u
          LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
          WHERE u.role = $1
          ORDER BY u.created_at DESC
        `;
        values = [role];
      }
      
      const result = await pool.query(query, values);
      
      return { success: true, users: result.rows };
    } catch (error) {
      console.error('Get users by role error:', error);
      return { success: false, error: 'Failed to fetch users' };
    }
  }

  // Update role user
  static async updateRoleUser(userId, updateData, creatorRole) {
    const client = await pool.connect();
    
    try {
      // First check if the user exists and get their current role
      const userCheck = await client.query('SELECT role, created_by FROM my_group_user WHERE id = $1', [userId]);
      
      if (userCheck.rows.length === 0) {
        return { success: false, error: 'User not found' };
      }
      
      const currentUser = userCheck.rows[0];
      
      // Check if creator role can modify this user's role
      if (updateData.role && !this.canCreateRole(creatorRole, updateData.role)) {
        return { success: false, error: `${creatorRole} cannot assign ${updateData.role} role` };
      }
      
      await client.query('BEGIN');
      
      // Update user table if needed
      if (updateData.username || updateData.role || updateData.my_group_app_id !== undefined) {
        const userUpdateFields = [];
        const userUpdateValues = [];
        let paramCount = 1;
        
        if (updateData.username) {
          userUpdateFields.push(`username = $${paramCount++}`);
          userUpdateValues.push(updateData.username);
        }
        
        if (updateData.role) {
          userUpdateFields.push(`role = $${paramCount++}`);
          userUpdateValues.push(updateData.role);
        }
        
        if (updateData.my_group_app_id !== undefined) {
          userUpdateFields.push(`my_group_app_id = $${paramCount++}`);
          userUpdateValues.push(updateData.my_group_app_id);
        }
        
        if (userUpdateFields.length > 0) {
          userUpdateValues.push(userId);
          const userQuery = `
            UPDATE my_group_user 
            SET ${userUpdateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = $${paramCount}
          `;
          
          await client.query(userQuery, userUpdateValues);
        }
      }
      
      // Update user details table
      if (updateData.full_name !== undefined || updateData.display_name !== undefined || updateData.email_id !== undefined) {
        const detailsUpdateFields = [];
        const detailsUpdateValues = [];
        let paramCount = 1;
        
        if (updateData.full_name !== undefined) {
          detailsUpdateFields.push(`full_name = $${paramCount++}`);
          detailsUpdateValues.push(updateData.full_name);
        }
        
        if (updateData.display_name !== undefined) {
          detailsUpdateFields.push(`display_name = $${paramCount++}`);
          detailsUpdateValues.push(updateData.display_name);
        }
        
        if (updateData.email_id !== undefined) {
          detailsUpdateFields.push(`email_id = $${paramCount++}`);
          detailsUpdateValues.push(updateData.email_id);
        }
        
        if (detailsUpdateFields.length > 0) {
          detailsUpdateValues.push(userId);
          
          // First try to update existing record
          const updateQuery = `
            UPDATE my_group_user_details 
            SET ${detailsUpdateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE my_group_user_id = $${paramCount}
          `;
          
          const updateResult = await client.query(updateQuery, detailsUpdateValues);
          
          // If no rows were updated, insert new record
          if (updateResult.rowCount === 0) {
            const insertQuery = `
              INSERT INTO my_group_user_details (my_group_user_id, full_name, display_name, email_id)
              VALUES ($1, $2, $3, $4)
            `;
            
            const insertValues = [
              userId,
              updateData.full_name || null,
              updateData.display_name || null,
              updateData.email_id || null
            ];
            
            await client.query(insertQuery, insertValues);
          }
        }
      }
      
      await client.query('COMMIT');
      
      // Fetch and return updated user
      const updatedUserQuery = `
        SELECT 
          u.id,
          u.username,
          u.role,
          u.my_group_app_id,
          u.created_by,
          u.is_active,
          u.created_at,
          ud.full_name,
          ud.display_name,
          ud.email_id
        FROM my_group_user u
        LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
        WHERE u.id = $1
      `;
      
      const updatedResult = await client.query(updatedUserQuery, [userId]);
      
      return { success: true, user: updatedResult.rows[0] };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Update role user error:', error);
      
      if (error.code === '23505') { // Unique violation
        return { success: false, error: 'Username already exists' };
      }
      
      return { success: false, error: 'Failed to update user' };
    } finally {
      client.release();
    }
  }

  // Delete role user
  static async deleteRoleUser(userId, creatorRole) {
    const client = await pool.connect();
    
    try {
      // First check if the user exists and get their role
      const userCheck = await client.query('SELECT role FROM my_group_user WHERE id = $1', [userId]);
      
      if (userCheck.rows.length === 0) {
        return { success: false, error: 'User not found' };
      }
      
      const userRole = userCheck.rows[0].role;
      
      // Check if creator role can delete this user's role
      if (!this.canCreateRole(creatorRole, userRole)) {
        return { success: false, error: `${creatorRole} cannot delete ${userRole} users` };
      }
      
      await client.query('BEGIN');
      
      // Delete user details first (foreign key constraint)
      await client.query('DELETE FROM my_group_user_details WHERE my_group_user_id = $1', [userId]);
      
      // Delete user
      const result = await client.query('DELETE FROM my_group_user WHERE id = $1 RETURNING id, username, role', [userId]);
      
      await client.query('COMMIT');
      
      return { success: true, message: 'User deleted successfully', deletedUser: result.rows[0] };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Delete role user error:', error);
      return { success: false, error: 'Failed to delete user' };
    } finally {
      client.release();
    }
  }

  // Get role hierarchy
  static getRoleHierarchy(role) {
    return {
      success: true,
      role: role,
      canCreate: this.roleHierarchy[role] || [],
      hierarchy: this.roleHierarchy
    };
  }

  // Get dashboard data based on role
  static async getDashboardData(role, userId) {
    try {
      const data = {
        role: role,
        userId: userId,
        stats: {},
        recentActivity: [],
        permissions: this.roleHierarchy[role] || []
      };

      // Get role-specific statistics
      switch (role) {
        case 'admin':
          // Admin can see all users
          const allUsersResult = await pool.query('SELECT role, COUNT(*) as count FROM my_group_user GROUP BY role');
          data.stats.usersByRole = allUsersResult.rows;
          break;
          
        case 'corporate':
        case 'headoffice':
        case 'regional':
        case 'branch':
          // Get users created by this role
          const createdUsersResult = await pool.query('SELECT role, COUNT(*) as count FROM my_group_user WHERE created_by = $1 GROUP BY role', [userId]);
          data.stats.createdUsers = createdUsersResult.rows;
          break;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Get dashboard data error:', error);
      return { success: false, error: 'Failed to fetch dashboard data' };
    }
  }
}

export default RolesModel;
