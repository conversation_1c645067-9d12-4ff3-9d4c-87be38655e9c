-- Migration: Create my_users table
-- Description: Central user table for all user types with hierarchical relationships
-- Created: 2024-01-18

CREATE TABLE my_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) GENERATED ALWAYS AS (CONCAT(first_name, ' ', last_name)) STORED,
    mobile_number VARCHAR(20),
    role_id INT NOT NULL,
    organization_id INT NULL,
    head_office_id INT NULL,
    regional_office_id INT NULL,
    branch_office_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMES<PERSON><PERSON> NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES my_roles(id),
    FOREIGN KEY (organization_id) REFERENCES my_organizations(id) ON DELETE SET NULL,
    FOREIGN KEY (head_office_id) REFERENCES my_head_offices(id) ON DELETE SET NULL,
    FOREIGN KEY (regional_office_id) REFERENCES my_regional_offices(id) ON DELETE SET NULL,
    FOREIGN KEY (branch_office_id) REFERENCES my_branch_offices(id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX idx_users_username ON my_users(username);
CREATE INDEX idx_users_email ON my_users(email);
CREATE INDEX idx_users_role_id ON my_users(role_id);
CREATE INDEX idx_users_active ON my_users(is_active);
CREATE INDEX idx_users_organization ON my_users(organization_id);
CREATE INDEX idx_users_head_office ON my_users(head_office_id);
CREATE INDEX idx_users_regional_office ON my_users(regional_office_id);
CREATE INDEX idx_users_branch_office ON my_users(branch_office_id);
CREATE INDEX idx_users_last_login ON my_users(last_login);
CREATE INDEX idx_users_locked_until ON my_users(locked_until);
