import express from 'express';
import { verifyToken, requireRole, dashboardRedirect } from '../middleware/auth.middleware.js';

const router = express.Router();

// Generic dashboard route that redirects based on role
router.get('/', verifyToken, dashboardRedirect, (req, res) => {
  res.json({
    success: true,
    message: 'Dashboard access granted',
    user: req.user,
    redirectUrl: req.dashboardUrl
  });
});

// Admin dashboard
router.get('/admin', verifyToken, requireRole(['admin']), (req, res) => {
  res.json({
    success: true,
    message: 'Admin Dashboard',
    user: req.user,
    dashboardType: 'admin',
    permissions: ['manage_all_users', 'view_all_data', 'system_settings']
  });
});

// Corporate dashboard
router.get('/corporate', verifyToken, requireRole(['corporate', 'admin']), (req, res) => {
  res.json({
    success: true,
    message: 'Corporate Dashboard',
    user: req.user,
    dashboardType: 'corporate',
    permissions: ['manage_headoffice', 'manage_regional', 'manage_branch', 'manage_users']
  });
});

// Head Office dashboard
router.get('/headoffice', verifyToken, requireRole(['headoffice', 'corporate', 'admin']), (req, res) => {
  res.json({
    success: true,
    message: 'Head Office Dashboard',
    user: req.user,
    dashboardType: 'headoffice',
    permissions: ['manage_regional', 'manage_branch', 'manage_users']
  });
});

// Regional dashboard
router.get('/regional', verifyToken, requireRole(['regional', 'headoffice', 'corporate', 'admin']), (req, res) => {
  res.json({
    success: true,
    message: 'Regional Dashboard',
    user: req.user,
    dashboardType: 'regional',
    permissions: ['manage_branch', 'manage_users']
  });
});

// Branch dashboard
router.get('/branch', verifyToken, requireRole(['branch', 'regional', 'headoffice', 'corporate', 'admin']), (req, res) => {
  res.json({
    success: true,
    message: 'Branch Dashboard',
    user: req.user,
    dashboardType: 'branch',
    permissions: ['manage_users']
  });
});

// User dashboard
router.get('/user', verifyToken, requireRole(['users', 'branch', 'regional', 'headoffice', 'corporate', 'admin']), (req, res) => {
  res.json({
    success: true,
    message: 'User Dashboard',
    user: req.user,
    dashboardType: 'user',
    permissions: ['view_profile', 'update_profile']
  });
});

// Partner dashboard
router.get('/partner', verifyToken, requireRole(['partners', 'admin']), (req, res) => {
  res.json({
    success: true,
    message: 'Partner Dashboard',
    user: req.user,
    dashboardType: 'partner',
    permissions: ['view_partner_data', 'manage_partner_content']
  });
});

export default router;
