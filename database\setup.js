#!/usr/bin/env node

// Database Setup Script for MyGroup
// Runs schema_new.sql and seeds initial data
// Supports both PostgreSQL and MySQL

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

class DatabaseSetup {
  constructor() {
    this.dbType = process.env.DB_TYPE || 'postgres';
    this.dbName = process.env.DB_NAME || 'my_group_db';
    this.connection = null;
    this.schemaFile = path.join(__dirname, 'schema_new.sql');
    this.pg = null;
    this.mysql = null;

    this.config = {
      host: process.env.DB_HOST || 'localhost',
      port: this.dbType === 'postgres' ? (process.env.DB_PORT || 5432) : (process.env.DB_PORT || 3306),
      user: process.env.DB_USER || (this.dbType === 'postgres' ? 'postgres' : 'root'),
      password: process.env.DB_PASSWORD || (this.dbType === 'postgres' ? 'admin' : ''),
      database: this.dbType === 'postgres' ? 'postgres' : undefined,
      multipleStatements: this.dbType === 'mysql' ? true : undefined
    };
  }

  async initializeDriver() {
    if (this.dbType === 'postgres') {
      const pg = await import('pg');
      this.pg = pg.default;
    } else if (this.dbType === 'mysql') {
      const mysql = await import('mysql2/promise');
      this.mysql = mysql.default;
    }
  }

  async connect() {
    try {
      await this.initializeDriver();

      if (this.dbType === 'postgres') {
        // Connect to PostgreSQL
        this.connection = new this.pg.Client(this.config);
        await this.connection.connect();
        console.log('✅ Connected to PostgreSQL server');

        // Check if database exists
        const checkDb = await this.connection.query(
          'SELECT 1 FROM pg_database WHERE datname = $1',
          [this.dbName]
        );

        if (checkDb.rows.length === 0) {
          console.log(`📦 Creating database: ${this.dbName}`);
          await this.connection.query(`CREATE DATABASE "${this.dbName}"`);
          console.log(`✅ Database ${this.dbName} created successfully`);
        } else {
          console.log(`📦 Database ${this.dbName} already exists`);
        }

        await this.connection.end();

        // Connect to the target database
        this.config.database = this.dbName;
        this.connection = new this.pg.Client(this.config);
        await this.connection.connect();
        console.log(`✅ Connected to database '${this.dbName}'`);

      } else if (this.dbType === 'mysql') {
        // Connect to MySQL
        this.connection = await this.mysql.createConnection(this.config);
        console.log('✅ Connected to MySQL server');

        // Create database if it doesn't exist
        await this.connection.execute(`CREATE DATABASE IF NOT EXISTS \`${this.dbName}\``);
        console.log(`✅ Database '${this.dbName}' ready`);

        // Close and reconnect with database
        await this.connection.end();
        this.config.database = this.dbName;
        this.connection = await this.mysql.createConnection(this.config);
        console.log(`✅ Connected to database '${this.dbName}'`);
      }

      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      console.error('💡 Make sure your database server is running and credentials are correct');
      return false;
    }
  }

  async runMigrations() {
    try {
      console.log('\n🔄 Running database migrations...');
      
      // Create migrations table if it doesn't exist
      await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS migrations (
          id INT PRIMARY KEY AUTO_INCREMENT,
          filename VARCHAR(255) NOT NULL,
          executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Get list of migration files
      const migrationFiles = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort();

      // Get already executed migrations
      const [executedMigrations] = await this.connection.execute(
        'SELECT filename FROM migrations'
      );
      const executedFiles = executedMigrations.map(row => row.filename);

      // Run pending migrations
      for (const file of migrationFiles) {
        if (!executedFiles.includes(file)) {
          console.log(`  📄 Running migration: ${file}`);
          
          const migrationSQL = fs.readFileSync(
            path.join(this.migrationsPath, file),
            'utf8'
          );
          
          await this.connection.execute(migrationSQL);
          
          // Record migration as executed
          await this.connection.execute(
            'INSERT INTO migrations (filename) VALUES (?)',
            [file]
          );
          
          console.log(`  ✅ Migration completed: ${file}`);
        } else {
          console.log(`  ⏭️  Migration already executed: ${file}`);
        }
      }
      
      console.log('✅ All migrations completed');
      return true;
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      return false;
    }
  }

  async runSeeds() {
    try {
      console.log('\n🌱 Running database seeds...');
      
      // Get list of seed files
      const seedFiles = fs.readdirSync(this.seedsPath)
        .filter(file => file.endsWith('.sql'))
        .sort();

      // Check if seeds have already been run
      const [existingData] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM my_users'
      );
      
      if (existingData[0].count > 0) {
        console.log('  ⏭️  Seeds already exist, skipping...');
        return true;
      }

      // Run seed files
      for (const file of seedFiles) {
        console.log(`  📄 Running seed: ${file}`);
        
        let seedSQL = fs.readFileSync(
          path.join(this.seedsPath, file),
          'utf8'
        );
        
        // Replace password placeholders with actual hashed passwords
        const hashedPassword = await bcrypt.hash('password123', 12);
        seedSQL = seedSQL.replace(/\$2b\$10\$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX/g, hashedPassword);
        
        await this.connection.execute(seedSQL);
        console.log(`  ✅ Seed completed: ${file}`);
      }
      
      console.log('✅ All seeds completed');
      return true;
    } catch (error) {
      console.error('❌ Seeding failed:', error.message);
      return false;
    }
  }

  async createAdminUser() {
    try {
      console.log('\n👤 Creating default admin user...');
      
      // Check if admin user already exists
      const [existingAdmin] = await this.connection.execute(
        'SELECT id FROM my_users WHERE username = ? OR email = ?',
        ['admin', '<EMAIL>']
      );
      
      if (existingAdmin.length > 0) {
        console.log('  ⏭️  Admin user already exists, skipping...');
        return true;
      }

      // Create admin user
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      await this.connection.execute(`
        INSERT INTO my_users (
          username, email, password_hash, first_name, last_name, 
          mobile_number, role_id, is_active, email_verified
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'admin',
        '<EMAIL>',
        hashedPassword,
        'System',
        'Administrator',
        '******-0000',
        1, // Admin role ID
        true,
        true
      ]);
      
      console.log('✅ Default admin user created');
      console.log('   Username: admin');
      console.log('   Password: admin123');
      console.log('   Email: <EMAIL>');
      
      return true;
    } catch (error) {
      console.error('❌ Admin user creation failed:', error.message);
      return false;
    }
  }

  async verifySetup() {
    try {
      console.log('\n🔍 Verifying database setup...');
      
      // Check tables exist
      const [tables] = await this.connection.execute(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = ?
      `, [this.config.database]);
      
      const expectedTables = [
        'my_roles', 'my_organizations', 'my_head_offices', 
        'my_regional_offices', 'my_branch_offices', 'my_users',
        'my_permissions', 'my_role_permissions', 'my_user_sessions', 
        'my_audit_logs'
      ];
      
      const existingTables = tables.map(row => row.TABLE_NAME);
      const missingTables = expectedTables.filter(table => !existingTables.includes(table));
      
      if (missingTables.length > 0) {
        console.error('❌ Missing tables:', missingTables.join(', '));
        return false;
      }
      
      // Check data exists
      const [userCount] = await this.connection.execute('SELECT COUNT(*) as count FROM my_users');
      const [roleCount] = await this.connection.execute('SELECT COUNT(*) as count FROM my_roles');
      
      console.log(`✅ Database verification completed`);
      console.log(`   Tables: ${existingTables.length}/${expectedTables.length}`);
      console.log(`   Users: ${userCount[0].count}`);
      console.log(`   Roles: ${roleCount[0].count}`);
      
      return true;
    } catch (error) {
      console.error('❌ Database verification failed:', error.message);
      return false;
    }
  }

  async runSchema() {
    console.log('📋 Running schema_new.sql...');

    try {
      // Read the schema file
      if (!fs.existsSync(this.schemaFile)) {
        throw new Error(`Schema file not found: ${this.schemaFile}`);
      }

      const schemaSQL = fs.readFileSync(this.schemaFile, 'utf8');

      if (this.dbType === 'postgres') {
        // For PostgreSQL, execute the entire schema at once
        await this.connection.query(schemaSQL);
        console.log('✅ PostgreSQL schema executed successfully');
      } else if (this.dbType === 'mysql') {
        // For MySQL, split statements and execute one by one
        const statements = schemaSQL
          .split(';')
          .map(stmt => stmt.trim())
          .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        console.log(`📝 Executing ${statements.length} SQL statements...`);

        for (let i = 0; i < statements.length; i++) {
          const statement = statements[i];
          if (statement.trim()) {
            try {
              await this.connection.execute(statement);
              console.log(`✅ Statement ${i + 1}/${statements.length} executed`);
            } catch (error) {
              if (error.message.includes('already exists') || error.message.includes('duplicate')) {
                console.log(`⚠️  Statement ${i + 1} skipped (already exists)`);
              } else {
                console.error(`❌ Error in statement ${i + 1}:`, error.message);
                throw error;
              }
            }
          }
        }
      }

      console.log('✅ Schema execution completed');

    } catch (error) {
      console.error('❌ Error running schema:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Database connection closed');
    }
  }

  async setup() {
    console.log('🚀 Starting MyGroup Database Setup...');
    console.log(`Database Type: ${this.dbType.toUpperCase()}`);
    console.log(`Database Name: ${this.dbName}`);
    console.log(`Host: ${this.config.host}`);
    console.log(`Port: ${this.config.port}\n`);

    try {
      // Connect to database
      const connected = await this.connect();
      if (!connected) return false;

      // Run schema_new.sql
      await this.runSchema();

      // Insert initial data
      await this.insertInitialData();

      console.log('\n🎉 Database setup completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('   1. Start your application: npm start');
      console.log('   2. Login with admin credentials');
      console.log('\n🔐 Default Admin Login:');
      console.log('   URL: http://localhost:3000/login');
      console.log('   Username: admin');
      console.log('   Password: admin123');
      console.log('\n🔗 Other Login Points:');
      console.log('   Partner Login: http://localhost:3000/partner');
      console.log('   Customer Frontend: http://localhost:3000');

      return true;
    } catch (error) {
      console.error('❌ Database setup failed:', error.message);
      return false;
    } finally {
      await this.disconnect();
    }
  }

  async insertInitialData() {
    console.log('📊 Inserting initial data...');

    try {
      // Insert roles
      const roles = [
        { id: 1, role: 'admin', role_description: 'System Administrator' },
        { id: 2, role: 'corporate', role_description: 'Corporate User' },
        { id: 3, role: 'headoffice', role_description: 'Head Office User' },
        { id: 4, role: 'regional', role_description: 'Regional Office User' },
        { id: 5, role: 'branch', role_description: 'Branch Office User' },
        { id: 6, role: 'partner', role_description: 'Partner User' },
        { id: 7, role: 'customer', role_description: 'Customer User' },
        { id: 8, role: 'app_admin', role_description: 'Application Administrator' }
      ];

      for (const role of roles) {
        try {
          if (this.dbType === 'postgres') {
            await this.connection.query(
              'INSERT INTO my_role (id, role, role_description) VALUES ($1, $2, $3) ON CONFLICT (id) DO NOTHING',
              [role.id, role.role, role.role_description]
            );
          } else if (this.dbType === 'mysql') {
            await this.connection.execute(
              'INSERT IGNORE INTO my_role (id, role, role_description) VALUES (?, ?, ?)',
              [role.id, role.role, role.role_description]
            );
          }
        } catch (error) {
          console.log(`⚠️  Role ${role.role} already exists`);
        }
      }

      // Insert group data
      const groups = [
        {
          id: 1,
          name: 'MyGroup Corporate',
          logo: '/assets/logos/corporate.png',
          icon: 'corporate-icon',
          background_color: '#1e40af',
          color: '#ffffff',
          group_url: 'corporate.mygroup.com',
          group_name: 'MyGroup Corporate'
        },
        {
          id: 2,
          name: 'MyGroup Partners',
          logo: '/assets/logos/partners.png',
          icon: 'partner-icon',
          background_color: '#059669',
          color: '#ffffff',
          group_url: 'partners.mygroup.com',
          group_name: 'MyGroup Partners'
        }
      ];

      for (const group of groups) {
        try {
          if (this.dbType === 'postgres') {
            await this.connection.query(
              'INSERT INTO my_group_data (id, name, logo, icon, background_color, color, group_url, group_name) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) ON CONFLICT (id) DO NOTHING',
              [group.id, group.name, group.logo, group.icon, group.background_color, group.color, group.group_url, group.group_name]
            );
          } else if (this.dbType === 'mysql') {
            await this.connection.execute(
              'INSERT IGNORE INTO my_group_data (id, name, logo, icon, background_color, color, group_url, group_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
              [group.id, group.name, group.logo, group.icon, group.background_color, group.color, group.group_url, group.group_name]
            );
          }
        } catch (error) {
          console.log(`⚠️  Group ${group.name} already exists`);
        }
      }

      // Insert default users
      const users = [
        {
          id: 1,
          user_name: 'admin',
          password: 'admin123',
          role_id: 1,
          group_id: 1,
          email: '<EMAIL>',
          mobile_number: '+1234567890'
        },
        {
          id: 2,
          user_name: 'corporate1',
          password: 'corporate123',
          role_id: 2,
          group_id: 1,
          email: '<EMAIL>',
          mobile_number: '+1234567891'
        },
        {
          id: 3,
          user_name: 'partner1',
          password: 'partner123',
          role_id: 6,
          group_id: 2,
          email: '<EMAIL>',
          mobile_number: '+1234567892'
        }
      ];

      for (const user of users) {
        try {
          const now = new Date().toISOString();
          if (this.dbType === 'postgres') {
            await this.connection.query(
              'INSERT INTO my_users (id, user_name, password, role_id, group_id, created_on, modified_on, status, ip_address, last_logged_in, mobile_number, email) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) ON CONFLICT (id) DO NOTHING',
              [user.id, user.user_name, user.password, user.role_id, user.group_id, now, now, 1, '127.0.0.1', now, user.mobile_number, user.email]
            );
          } else if (this.dbType === 'mysql') {
            await this.connection.execute(
              'INSERT IGNORE INTO my_users (id, user_name, password, role_id, group_id, created_on, modified_on, status, ip_address, last_logged_in, mobile_number, email) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
              [user.id, user.user_name, user.password, user.role_id, user.group_id, now, now, 1, '127.0.0.1', now, user.mobile_number, user.email]
            );
          }
          console.log(`✅ User ${user.user_name} created`);
        } catch (error) {
          console.log(`⚠️  User ${user.user_name} already exists`);
        }
      }

      console.log('✅ Initial data insertion completed');

    } catch (error) {
      console.error('❌ Error inserting initial data:', error.message);
      throw error;
    }
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new DatabaseSetup();
  setup.setup().then(success => {
    process.exit(success ? 0 : 1);
  });
}

export default DatabaseSetup;
