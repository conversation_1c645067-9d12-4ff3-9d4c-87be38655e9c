/* AdminDashboard.css */
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Mobile Header */
.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #dee2e6;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.sidebar-toggle {
  color: #333 !important;
  text-decoration: none !important;
  font-size: 1.2rem;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 999;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar-open {
  transform: translateX(0);
}

.sidebar-header {
  padding: 30px 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
  margin: 0;
  font-weight: 600;
}

.sidebar-header p {
  margin: 5px 0 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.sidebar-nav .nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 15px 20px;
  border: none;
  border-radius: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.sidebar-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
}

.sidebar-nav .nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white !important;
  border-left: 4px solid white;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.sidebar-footer .btn:hover {
  background: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  color: #dc3545;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 0;
  padding: 20px;
  transition: margin-left 0.3s ease;
}

/* Dashboard Content */
.dashboard-content h2,
.user-management h2,
.settings-content h2 {
  color: #333;
  font-weight: 600;
}

/* Stat Cards */
.stat-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin: 10px 0 5px;
  color: #333;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* Tables */
.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #333;
}

.table td {
  vertical-align: middle;
}

/* Cards */
.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0 !important;
  border: none;
}

.card-header h5 {
  font-weight: 600;
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  font-weight: 500;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-warning,
.btn-outline-success {
  border-radius: 20px;
  font-weight: 500;
}

/* Responsive Design */
@media (min-width: 768px) {
  .mobile-header {
    display: none;
  }
  
  .sidebar {
    position: relative;
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 280px;
    padding: 30px;
  }
  
  .admin-dashboard {
    padding-top: 0;
  }
}

@media (max-width: 767px) {
  .main-content {
    padding-top: 80px;
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .stat-card {
    margin-bottom: 15px;
  }
  
  .table-responsive {
    font-size: 0.9rem;
  }
  
  .btn-group .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
  }
}

/* Dark theme support */
[data-theme="dark"] .admin-dashboard {
  background-color: #1a1a1a;
}

[data-theme="dark"] .mobile-header {
  background: #2c2c2c;
  border-bottom-color: #444;
}

[data-theme="dark"] .sidebar-toggle {
  color: white !important;
}

[data-theme="dark"] .main-content {
  color: white;
}

[data-theme="dark"] .dashboard-content h2,
[data-theme="dark"] .user-management h2,
[data-theme="dark"] .settings-content h2 {
  color: white;
}

[data-theme="dark"] .stat-card {
  background: #2c2c2c;
  color: white;
}

[data-theme="dark"] .stat-number {
  color: white;
}

[data-theme="dark"] .card {
  background: #2c2c2c;
  color: white;
}

[data-theme="dark"] .table {
  color: white;
}

[data-theme="dark"] .table th {
  background-color: #3c3c3c;
  color: white;
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state h5 {
  margin-bottom: 10px;
}

.empty-state p {
  margin-bottom: 20px;
}

/* Animation */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
