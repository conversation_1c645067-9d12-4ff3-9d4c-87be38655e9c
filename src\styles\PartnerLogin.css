/* Partner Login Styles */

.partner-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  padding: 20px;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 480px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 25px;
}

.logo-section .logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;
}

.logo-icon {
  font-size: 2.5rem;
}

.logo-text {
  font-size: 2rem;
  font-weight: bold;
  color: #059669;
}

.login-header h1 {
  color: #1f2937;
  margin: 0 0 8px 0;
  font-size: 1.6rem;
  font-weight: 600;
}

.login-header p {
  color: #6b7280;
  margin: 0;
  font-size: 0.9rem;
}

/* Partner Benefits */
.partner-benefits {
  margin-bottom: 25px;
  padding: 20px;
  background: #ecfdf5;
  border-radius: 12px;
  border: 1px solid #a7f3d0;
}

.partner-benefits h3 {
  color: #065f46;
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
}

.partner-benefits ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.partner-benefits li {
  padding: 4px 0;
  font-size: 0.85rem;
  color: #047857;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 0.9rem;
}

.error-icon {
  font-size: 1.1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.form-group input:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-group input:disabled {
  background: #f9fafb;
  color: #6b7280;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #374151;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: #059669;
  border-color: #059669;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-password-link {
  background: none;
  border: none;
  color: #059669;
  font-size: 0.9rem;
  cursor: pointer;
  text-decoration: underline;
}

.forgot-password-link:hover {
  color: #047857;
}

.login-button {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Demo Credentials */
.demo-credentials {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 20px;
}

.demo-credentials h4 {
  color: #14532d;
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.credentials-info {
  margin-bottom: 8px;
}

.credentials-info p {
  margin: 4px 0;
  font-size: 0.85rem;
  color: #166534;
}

.demo-credentials small {
  color: #15803d;
  font-size: 0.8rem;
  font-style: italic;
}

/* Partnership Info */
.partnership-info {
  background: #fefce8;
  border: 1px solid #fde047;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 20px;
  text-align: center;
}

.partnership-info h4 {
  color: #713f12;
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.partnership-info p {
  color: #92400e;
  margin: 0 0 12px 0;
  font-size: 0.85rem;
}

.become-partner-btn {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.become-partner-btn:hover {
  background: #d97706;
}

/* Footer */
.login-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.navigation-links {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.navigation-links a {
  color: #059669;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.navigation-links a:hover {
  text-decoration: underline;
}

.system-info small {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 640px) {
  .partner-login-container {
    padding: 10px;
  }
  
  .login-card {
    padding: 30px 20px;
  }
  
  .navigation-links {
    flex-direction: column;
    gap: 10px;
  }
}
