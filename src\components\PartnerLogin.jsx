import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import authAPI from '../api/authAPI';
import '../styles/PartnerLogin.css';

const PartnerLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const navigate = useNavigate();
  const location = useLocation();
  const { login, currentUser } = useAuth();

  // Redirect if already logged in with partner role
  useEffect(() => {
    if (currentUser && currentUser.role === 'partner') {
      navigate('/partner/dashboard', { replace: true });
    }
  }, [currentUser, navigate]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.username.trim()) {
        throw new Error('Username is required');
      }
      if (!formData.password) {
        throw new Error('Password is required');
      }

      // Attempt login
      const result = await authAPI.login(
        formData.username.trim(),
        formData.password,
        navigator.userAgent,
        '127.0.0.1'
      );

      if (result.success) {
        // Check if user has partner role access
        if (result.user.role !== 'partner') {
          throw new Error(`Access denied. This login is for partners only. Your role: ${result.user.role}`);
        }

        // Save remember me preference
        if (formData.rememberMe) {
          localStorage.setItem('partnerRememberMe', 'true');
          localStorage.setItem('partnerSavedUsername', formData.username);
        } else {
          localStorage.removeItem('partnerRememberMe');
          localStorage.removeItem('partnerSavedUsername');
        }

        // Use auth context login
        await login(formData.username, formData.password);
        
        // Redirect to partner dashboard
        navigate('/partner/dashboard', { replace: true });
      } else {
        throw new Error(result.error || 'Login failed');
      }
    } catch (error) {
      console.error('Partner login error:', error);
      setError(error.message || 'An error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  // Load saved username if remember me was checked
  useEffect(() => {
    const rememberMe = localStorage.getItem('partnerRememberMe');
    const savedUsername = localStorage.getItem('partnerSavedUsername');
    
    if (rememberMe === 'true' && savedUsername) {
      setFormData(prev => ({
        ...prev,
        username: savedUsername,
        rememberMe: true
      }));
    }
  }, []);

  return (
    <div className="partner-login-container">
      <div className="login-background">
        <div className="background-pattern"></div>
      </div>
      
      <div className="login-content">
        <div className="login-card">
          <div className="login-header">
            <div className="logo-section">
              <div className="logo">
                <span className="logo-icon">🤝</span>
                <span className="logo-text">MyGroup</span>
              </div>
              <h1>Partner Portal</h1>
              <p>Exclusive access for MyGroup partners</p>
            </div>
          </div>

          {/* Partner Benefits */}
          <div className="partner-benefits">
            <h3>Partner Benefits:</h3>
            <ul>
              <li>📊 Access to analytics dashboard</li>
              <li>💼 Manage your business listings</li>
              <li>📈 Track performance metrics</li>
              <li>🎯 Targeted advertising tools</li>
              <li>🤝 Direct support channel</li>
            </ul>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="login-form">
            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="username">Partner Username</label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter your partner username"
                required
                disabled={loading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                required
                disabled={loading}
              />
            </div>

            <div className="form-options">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  disabled={loading}
                />
                <span className="checkbox-custom"></span>
                Remember me
              </label>
              
              <button type="button" className="forgot-password-link">
                Forgot Password?
              </button>
            </div>

            <button 
              type="submit" 
              className="login-button"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="loading-spinner"></span>
                  Signing in...
                </>
              ) : (
                'Access Partner Portal'
              )}
            </button>
          </form>

          {/* Demo Credentials */}
          <div className="demo-credentials">
            <h4>Demo Partner Credentials:</h4>
            <div className="credentials-info">
              <p><strong>Username:</strong> partner1</p>
              <p><strong>Password:</strong> partner123</p>
            </div>
            <small>Use these credentials for demo access</small>
          </div>

          {/* Partnership Info */}
          <div className="partnership-info">
            <h4>Not a partner yet?</h4>
            <p>Join our partner program and grow your business with MyGroup.</p>
            <button className="become-partner-btn">
              Become a Partner
            </button>
          </div>

          <div className="login-footer">
            <div className="navigation-links">
              <a href="/">← Back to Main Site</a>
              <a href="/login">Backend Login →</a>
            </div>
            <div className="system-info">
              <small>MyGroup Partner Portal v2.0</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartnerLogin;
