# Environment Configuration for MyGroup Multi-Role Dashboard

# Application Settings
NODE_ENV=development
PORT=3001
REACT_APP_API_URL=http://localhost:3001/api

# Database Configuration
# Use the database name from schema_new.sql
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password_here
DB_NAME=my_group_db
DB_SSL=false

# For MySQL (alternative)
# DB_TYPE=mysql
# DB_HOST=localhost
# DB_PORT=3306
# DB_USERNAME=root
# DB_PASSWORD=your_password_here
# DB_NAME=my_group_db

# For SQLite (development/testing)
# DB_TYPE=sqlite
# DB_NAME=mygroup_dev.sqlite

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-very-long-and-random
JWT_EXPIRES_IN=24h

# Security Settings
BCRYPT_SALT_ROUNDS=12
SESSION_TIMEOUT=24h
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=30m

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=MyGroup System

# File Upload Settings
MAX_FILE_SIZE=10MB
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=100

# CORS Settings
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Development Settings
DEBUG=true
MOCK_DATA=false
