CREATE TABLE "my_users"(
    "id" BIGINT NOT NULL,
    "user_name" VARCHAR(255) NOT NULL,
    "password" TEXT NOT NULL,
    "role_id" SMALLINT NOT NULL,
    "group_id" SMALLINT NOT NULL,
    "created_on" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "modified_on" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "status" SMALLINT NOT NULL DEFAULT 1,
    "ip_address" VARCHAR(255) NOT NULL,
    "last_logged_in" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "mobile_number" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) NOT NULL
);
ALTER TABLE
    "my_users" ADD PRIMARY KEY("id");
COMMENT
ON COLUMN
    "my_users"."password" IS 'hash encritypted';
COMMENT
ON COLUMN
    "my_users"."role_id" IS 'my_role id';
COMMENT
ON COLUMN
    "my_users"."group_id" IS 'store group_id for partner';
COMMENT
ON COLUMN
    "my_users"."status" IS 'active, in-active';
CREATE TABLE "my_role"(
    "id" SMALLINT NOT NULL,
    "role" VARCHAR(255) NOT NULL,
    "role_description" VARCHAR(255) NOT NULL
);
ALTER TABLE
    "my_role" ADD PRIMARY KEY("id");
COMMENT
ON COLUMN
    "my_role"."role" IS 'admin, corporate, headoffice, regional, branch, partner, customer, app_admin';
CREATE TABLE "my_group_data"(
    "id" INTEGER NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "logo" TEXT NOT NULL,
    "icon" TEXT NOT NULL,
    "background_color" CHAR(255) NOT NULL,
    "color" CHAR(255) NOT NULL,
    "group_url" CHAR(255) NOT NULL,
    "group_name" CHAR(255) NOT NULL
);
ALTER TABLE
    "my_group_data" ADD PRIMARY KEY("id");
COMMENT
ON COLUMN
    "my_group_data"."logo" IS 'logo path';
COMMENT
ON COLUMN
    "my_group_data"."group_name" IS 'My group, my apps,';
CREATE TABLE "my_user_details"(
    "id" BIGINT NOT NULL,
    "first_name" VARCHAR(255) NOT NULL,
    "middle_name" VARCHAR(255) NOT NULL,
    "last_name" VARCHAR(255) NOT NULL,
    "user_id" INTEGER NOT NULL,
    "display_name" VARCHAR(255) NOT NULL,
    "gender" CHAR(10) NOT NULL,
    "martial_status" CHAR(20) NOT NULL,
    "coutry_id" INTEGER NOT NULL,
    "state_id" INTEGER NOT NULL,
    "district_id" INTEGER NOT NULL,
    "nationality" CHAR(255) NOT NULL,
    "education" INTEGER NOT NULL,
    "profession" INTEGER NOT NULL,
    "photo" VARCHAR(255) NOT NULL,
    "date_of_birth" DATE NOT NULL,
    "created_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "update_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "language_id" INTEGER NOT NULL
);
ALTER TABLE
    "my_user_details" ADD PRIMARY KEY("id");
CREATE TABLE "my_country"(
    "id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "continent_id" INTEGER NOT NULL,
    "code" CHAR(255) NOT NULL,
    "currency" CHAR(255) NOT NULL,
    "flag" TEXT NOT NULL,
    "iso_code" CHAR(255) NOT NULL,
    "nationality" INTEGER NOT NULL,
    "display_order" INTEGER NOT NULL
);
ALTER TABLE
    "my_country" ADD PRIMARY KEY("id");
CREATE TABLE "my_continents"(
    "id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "code" CHAR(255) NOT NULL,
    "display_order" INTEGER NOT NULL
);
ALTER TABLE
    "my_continents" ADD PRIMARY KEY("id");
CREATE TABLE "my_state"(
    "id" INTEGER NOT NULL,
    "country_id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "code" CHAR(255) NOT NULL,
    "display_order" INTEGER NOT NULL,
    "status" CHAR(30) NOT NULL DEFAULT 'Active'
);
ALTER TABLE
    "my_state" ADD PRIMARY KEY("id");
COMMENT
ON COLUMN
    "my_state"."status" IS 'Active, Inactive';
CREATE TABLE "my_district"(
    "id" INTEGER NOT NULL,
    "state_id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "code" CHAR(255) NOT NULL,
    "display_order" INTEGER NOT NULL
);
ALTER TABLE
    "my_district" ADD PRIMARY KEY("id");
CREATE TABLE "my_language"(
    "id" INTEGER NOT NULL,
    "country_id" INTEGER NOT NULL,
    "primary_language" VARCHAR(255) NOT NULL,
    "secondary_language" VARCHAR(255) NOT NULL
);
ALTER TABLE
    "my_language" ADD PRIMARY KEY("id");
COMMENT
ON COLUMN
    "my_language"."secondary_language" IS 'ENCODING ''UTF8''';
CREATE TABLE "my_education"(
    "id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "description" VARCHAR(255) NOT NULL
);
ALTER TABLE
    "my_education" ADD PRIMARY KEY("id");
CREATE TABLE "my_profession"(
    "id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "description" VARCHAR(255) NOT NULL
);
ALTER TABLE
    "my_profession" ADD PRIMARY KEY("id");
CREATE TABLE "my_ads"(
    "id" INTEGER NOT NULL,
    "titile" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "my_user_id" INTEGER NOT NULL,
    "my_group_category_id" INTEGER NOT NULL,
    "my_ads_type_id" INTEGER NOT NULL,
    "country_id" INTEGER NOT NULL,
    "state_id" INTEGER NOT NULL,
    "district_id" INTEGER NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'Pending',
    "created_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "update_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL
);
ALTER TABLE
    "my_ads" ADD PRIMARY KEY("id");
CREATE TABLE "my_ads_slots"(
    "id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "location_details" TEXT NOT NULL,
    "price" DECIMAL(10, 2) NOT NULL,
    "duration_minutes" INTEGER NOT NULL,
    "is_available" BOOLEAN NOT NULL DEFAULT true,
    "available_from" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "available_to" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "created_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    "update_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL
);
ALTER TABLE
    "my_ads_slots" ADD PRIMARY KEY("id");
COMMENT
ON COLUMN
    "my_ads_slots"."location_details" IS '"Top banner on homepage", "Sidebar on sports page"';
COMMENT
ON COLUMN
    "my_ads_slots"."price" IS 'CHECK (Price >= 0),';
COMMENT
ON COLUMN
    "my_ads_slots"."available_from" IS 'WITH TIME ZONE';
COMMENT
ON COLUMN
    "my_ads_slots"."available_to" IS 'WITH TIME ZONE';
CREATE TABLE "my_group_category"(
    "id" INTEGER NOT NULL,
    "my_group_data_id" INTEGER NOT NULL,
    "name" CHAR(255) NOT NULL,
    "icon" VARCHAR(255) NOT NULL,
    "display_name" VARCHAR(255) NOT NULL,
    "end_point" VARCHAR(255) NOT NULL
);
ALTER TABLE
    "my_group_category" ADD PRIMARY KEY("id");
CREATE TABLE "my_ads_type"(
    "id" INTEGER NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL
);
ALTER TABLE
    "my_ads_type" ADD PRIMARY KEY("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_coutry_id_foreign" FOREIGN KEY("coutry_id") REFERENCES "my_country"("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_profession_foreign" FOREIGN KEY("profession") REFERENCES "my_profession"("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_education_foreign" FOREIGN KEY("education") REFERENCES "my_education"("id");
ALTER TABLE
    "my_state" ADD CONSTRAINT "my_state_country_id_foreign" FOREIGN KEY("country_id") REFERENCES "my_country"("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_state_id_foreign" FOREIGN KEY("state_id") REFERENCES "my_state"("id");
ALTER TABLE
    "my_country" ADD CONSTRAINT "my_country_continent_id_foreign" FOREIGN KEY("continent_id") REFERENCES "my_continents"("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_district_id_foreign" FOREIGN KEY("district_id") REFERENCES "my_district"("id");
ALTER TABLE
    "my_users" ADD CONSTRAINT "my_users_group_id_foreign" FOREIGN KEY("group_id") REFERENCES "my_group_data"("id");
ALTER TABLE
    "my_state" ADD CONSTRAINT "my_state_id_foreign" FOREIGN KEY("id") REFERENCES "my_district"("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_user_id_foreign" FOREIGN KEY("user_id") REFERENCES "my_users"("id");
ALTER TABLE
    "my_user_details" ADD CONSTRAINT "my_user_details_language_id_foreign" FOREIGN KEY("language_id") REFERENCES "my_language"("id");
ALTER TABLE
    "my_group_category" ADD CONSTRAINT "my_group_category_my_group_data_id_foreign" FOREIGN KEY("my_group_data_id") REFERENCES "my_group_data"("id");
ALTER TABLE
    "my_users" ADD CONSTRAINT "my_users_role_id_foreign" FOREIGN KEY("role_id") REFERENCES "my_role"("id");