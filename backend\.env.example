# Server Configuration
PORT=8000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Database Configuration (for future use)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=corporate_dashboard
DB_USER=postgres
DB_PASSWORD=your-db-password

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Frontend Configuration
FRONTEND_URL=http://localhost:3001
CORS_ORIGIN=http://localhost:3001

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Redis Configuration (for sessions/cache)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Notification Configuration
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
SMS_API_KEY=your-sms-api-key

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
