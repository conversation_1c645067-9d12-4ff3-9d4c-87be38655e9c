-- Migration: Create my_audit_logs table
-- Description: Tracks user activities and system changes for security and compliance
-- Created: 2024-01-18

CREATE TABLE my_audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES my_users(id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX idx_audit_user_id ON my_audit_logs(user_id);
CREATE INDEX idx_audit_action ON my_audit_logs(action);
CREATE INDEX idx_audit_table_name ON my_audit_logs(table_name);
CREATE INDEX idx_audit_created_at ON my_audit_logs(created_at);
CREATE INDEX idx_audit_record_id ON my_audit_logs(record_id);
