import React, { useState } from 'react';
import { Container, Row, Col, Button, Form } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import {
  FaBars,
  FaUser,
  FaSearch,
  FaSun,
  FaMoon,
  FaBell,
  FaCog,
  FaTachometerAlt
} from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import './Header.css';

const Header = ({ onShowLogin, onShowProfile, groupData, onLoginRequired }) => {
  const { isAuthenticated, user, canAccessDashboard } = useAuth();
  const navigate = useNavigate();
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  const toggleTheme = () => {
    setIsDarkTheme(!isDarkTheme);
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'light' : 'dark');
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log('Searching for:', searchQuery);
      // Implement search functionality
    }
  };

  const handleProfileClick = () => {
    if (onLoginRequired()) {
      onShowProfile();
    }
  };

  const handleMoreClick = () => {
    setShowMoreMenu(!showMoreMenu);
  };

  return (
    <header className="app-header">
      {/* Header Row 1 - More icon and horizontal group data */}
      <div className="header-row-1">
        <Container fluid>
          <Row className="align-items-center">
            <Col xs={2}>
              <Button 
                variant="link" 
                className="more-btn p-0"
                onClick={handleMoreClick}
              >
                <FaBars size={20} />
              </Button>
            </Col>
            <Col xs={10}>
              <div className="group-data-horizontal">
                {groupData.map((group) => (
                  <div 
                    key={group.id} 
                    className="group-item"
                    style={{ 
                      backgroundColor: group.background_color,
                      color: group.color 
                    }}
                  >
                    <span className="group-icon">{group.icon}</span>
                    <span className="group-name">{group.name}</span>
                  </div>
                ))}
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Header Row 2 - Profile, Search, Theme toggle, Group logos */}
      <div className="header-row-2">
        <Container fluid>
          <Row className="align-items-center">
            <Col xs={2}>
              <Button 
                variant="link" 
                className="profile-btn p-0"
                onClick={handleProfileClick}
              >
                <FaUser size={18} />
              </Button>
            </Col>
            
            <Col xs={6}>
              <Form onSubmit={handleSearch} className="search-form">
                <div className="search-input-group">
                  <Form.Control
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="search-input"
                  />
                  <Button 
                    type="submit" 
                    variant="link" 
                    className="search-btn p-0"
                  >
                    <FaSearch size={16} />
                  </Button>
                </div>
              </Form>
            </Col>
            
            <Col xs={2}>
              <Button 
                variant="link" 
                className="theme-btn p-0"
                onClick={toggleTheme}
              >
                {isDarkTheme ? <FaSun size={18} /> : <FaMoon size={18} />}
              </Button>
            </Col>
            
            <Col xs={2}>
              <div className="group-logos">
                {groupData.slice(0, 2).map((group) => (
                  <img 
                    key={group.id}
                    src={group.logo} 
                    alt={group.name}
                    className="group-logo"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                ))}
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* More Menu Dropdown */}
      {showMoreMenu && (
        <div className="more-menu">
          <Container fluid>
            <div className="more-menu-content">
              <div className="menu-item">
                <FaBell className="menu-icon" />
                <span>Notifications</span>
              </div>
              <div className="menu-item">
                <FaCog className="menu-icon" />
                <span>Settings</span>
              </div>
              {isAuthenticated && canAccessDashboard() && (
                <div className="menu-item" onClick={() => navigate('/admin')}>
                  <FaTachometerAlt className="menu-icon" />
                  <span>Admin Dashboard</span>
                </div>
              )}
              {isAuthenticated && (
                <div className="menu-item" onClick={onShowProfile}>
                  <FaUser className="menu-icon" />
                  <span>Profile</span>
                </div>
              )}
              {!isAuthenticated && (
                <div className="menu-item" onClick={onShowLogin}>
                  <FaUser className="menu-icon" />
                  <span>Login</span>
                </div>
              )}
            </div>
          </Container>
        </div>
      )}
    </header>
  );
};

export default Header;
