import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge } from 'react-bootstrap';

const AdsPricingManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [viewMode, setViewMode] = useState('calendar'); // 'calendar', 'table'

    // Form data
    const [formData, setFormData] = useState({
        date: '',
        category: '',
        priceType: 'daily',
        basePrice: '',
        premiumPrice: '',
        discountPrice: '',
        releaseDate: '',
        status: 'Active'
    });

    // Pricing data
    const [pricingData, setPricingData] = useState([
        {
            id: 1,
            date: '2024-01-20',
            category: 'Header Ads',
            priceType: 'daily',
            basePrice: 500,
            premiumPrice: 750,
            discountPrice: 400,
            releaseDate: '2024-01-15',
            status: 'Active',
            bookings: 12,
            revenue: 6000
        },
        {
            id: 2,
            date: '2024-01-21',
            category: 'Main Page Ads',
            priceType: 'daily',
            basePrice: 800,
            premiumPrice: 1200,
            discountPrice: 650,
            releaseDate: '2024-01-16',
            status: 'Active',
            bookings: 8,
            revenue: 6400
        },
        {
            id: 3,
            date: '2024-01-22',
            category: 'Header Ads',
            priceType: 'weekly',
            basePrice: 3000,
            premiumPrice: 4500,
            discountPrice: 2400,
            releaseDate: '2024-01-17',
            status: 'Active',
            bookings: 5,
            revenue: 15000
        },
        {
            id: 4,
            date: '2024-01-23',
            category: 'Footer Ads',
            priceType: 'monthly',
            basePrice: 10000,
            premiumPrice: 15000,
            discountPrice: 8000,
            releaseDate: '2024-01-18',
            status: 'Inactive',
            bookings: 2,
            revenue: 20000
        }
    ]);

    // Categories
    const categories = [
        'Header Ads',
        'Main Page Ads',
        'Footer Ads',
        'Sidebar Ads',
        'Banner Ads',
        'Video Ads',
        'Popup Ads',
        'Newsletter Ads'
    ];

    const priceTypes = [
        { value: 'daily', label: 'Daily' },
        { value: 'weekly', label: 'Weekly' },
        { value: 'monthly', label: 'Monthly' },
        { value: 'quarterly', label: 'Quarterly' },
        { value: 'yearly', label: 'Yearly' }
    ];

    useEffect(() => {
        fetchPricingData();
    }, []);

    const fetchPricingData = async () => {
        setLoading(true);
        try {
            // API call to fetch pricing data
            // const response = await fetch('/api/ads-pricing');
            // const data = await response.json();
            // setPricingData(data);
        } catch (error) {
            console.error('Error fetching pricing data:', error);
            setError('Failed to fetch pricing data');
        } finally {
            setLoading(false);
        }
    };

    const handleCreate = (date = null) => {
        setFormData({
            date: date ? date.toISOString().split('T')[0] : '',
            category: '',
            priceType: 'daily',
            basePrice: '',
            premiumPrice: '',
            discountPrice: '',
            releaseDate: new Date().toISOString().split('T')[0],
            status: 'Active'
        });
        setShowModal(true);
    };

    const handleEdit = (pricing) => {
        setFormData({
            date: pricing.date,
            category: pricing.category,
            priceType: pricing.priceType,
            basePrice: pricing.basePrice,
            premiumPrice: pricing.premiumPrice,
            discountPrice: pricing.discountPrice,
            releaseDate: pricing.releaseDate,
            status: pricing.status
        });
        setShowModal(true);
    };

    const handleDelete = (pricingId) => {
        if (window.confirm('Are you sure you want to delete this pricing entry?')) {
            setPricingData(pricingData.filter(pricing => pricing.id !== pricingId));
            setSuccess('Pricing entry deleted successfully');
        }
    };

    const toggleStatus = (pricingId) => {
        setPricingData(pricingData.map(pricing => 
            pricing.id === pricingId 
                ? { ...pricing, status: pricing.status === 'Active' ? 'Inactive' : 'Active' }
                : pricing
        ));
        setSuccess('Pricing status updated successfully');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            const newPricing = {
                id: Date.now(),
                date: formData.date,
                category: formData.category,
                priceType: formData.priceType,
                basePrice: parseFloat(formData.basePrice),
                premiumPrice: parseFloat(formData.premiumPrice),
                discountPrice: parseFloat(formData.discountPrice),
                releaseDate: formData.releaseDate,
                status: formData.status,
                bookings: 0,
                revenue: 0
            };

            setPricingData([...pricingData, newPricing]);
            setSuccess('Pricing entry created successfully');
            setShowModal(false);
            setFormData({
                date: '',
                category: '',
                priceType: 'daily',
                basePrice: '',
                premiumPrice: '',
                discountPrice: '',
                releaseDate: '',
                status: 'Active'
            });
        } catch (error) {
            setError('Failed to save pricing entry');
        } finally {
            setLoading(false);
        }
    };

    const getCalendarEvents = () => {
        return pricingData.map(pricing => ({
            id: pricing.id,
            title: `${pricing.category} - $${pricing.basePrice}`,
            start: new Date(pricing.date),
            end: new Date(pricing.date),
            resource: pricing
        }));
    };

    const renderCalendarView = () => (
        <Card>
            <Card.Header>
                <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">
                        <i className="fas fa-calendar me-2"></i>Pricing Calendar
                    </h5>
                    <Button variant="primary" onClick={() => handleCreate()}>
                        <i className="fas fa-plus me-2"></i>Add Pricing
                    </Button>
                </div>
            </Card.Header>
            <Card.Body>
                <div className="calendar-container" style={{ height: '500px' }}>
                    {/* Simple calendar grid for demonstration */}
                    <div className="simple-calendar">
                        <div className="calendar-header mb-3">
                            <h6>January 2024</h6>
                        </div>
                        <div className="calendar-grid">
                            {Array.from({ length: 31 }, (_, i) => {
                                const day = i + 1;
                                const dateStr = `2024-01-${day.toString().padStart(2, '0')}`;
                                const dayPricing = pricingData.filter(p => p.date === dateStr);
                                
                                return (
                                    <div 
                                        key={day} 
                                        className="calendar-day"
                                        onClick={() => handleCreate(new Date(dateStr))}
                                        style={{
                                            border: '1px solid #ddd',
                                            padding: '8px',
                                            minHeight: '80px',
                                            cursor: 'pointer',
                                            backgroundColor: dayPricing.length > 0 ? '#e3f2fd' : 'white'
                                        }}
                                    >
                                        <div className="day-number">{day}</div>
                                        {dayPricing.map(pricing => (
                                            <div key={pricing.id} className="pricing-item">
                                                <small className="text-primary">
                                                    {pricing.category}: ${pricing.basePrice}
                                                </small>
                                            </div>
                                        ))}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </Card.Body>
        </Card>
    );

    const renderTableView = () => (
        <Card>
            <Card.Header>
                <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">
                        <i className="fas fa-table me-2"></i>Pricing Table
                    </h5>
                    <Button variant="primary" onClick={() => handleCreate()}>
                        <i className="fas fa-plus me-2"></i>Add Pricing
                    </Button>
                </div>
            </Card.Header>
            <Card.Body>
                {pricingData.length === 0 ? (
                    <div className="text-center py-5">
                        <i className="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
                        <h5 className="text-muted">No Pricing Data</h5>
                        <p className="text-muted">Create your first pricing entry</p>
                        <Button variant="primary" onClick={() => handleCreate()}>
                            Create First Pricing
                        </Button>
                    </div>
                ) : (
                    <Table responsive hover>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Date</th>
                                <th>Category</th>
                                <th>Type</th>
                                <th>Base Price</th>
                                <th>Premium Price</th>
                                <th>Discount Price</th>
                                <th>Release Date</th>
                                <th>Status</th>
                                <th>Performance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {pricingData.map((pricing, index) => (
                                <tr key={pricing.id}>
                                    <td>{index + 1}</td>
                                    <td>{pricing.date}</td>
                                    <td>
                                        <Badge bg="info">{pricing.category}</Badge>
                                    </td>
                                    <td>
                                        <Badge bg="secondary">{pricing.priceType}</Badge>
                                    </td>
                                    <td>
                                        <strong>${pricing.basePrice}</strong>
                                    </td>
                                    <td>
                                        <span className="text-success">${pricing.premiumPrice}</span>
                                    </td>
                                    <td>
                                        <span className="text-warning">${pricing.discountPrice}</span>
                                    </td>
                                    <td>{pricing.releaseDate}</td>
                                    <td>
                                        <Badge 
                                            bg={pricing.status === 'Active' ? 'success' : 'danger'}
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => toggleStatus(pricing.id)}
                                        >
                                            {pricing.status}
                                        </Badge>
                                    </td>
                                    <td>
                                        <small>
                                            <div>Bookings: {pricing.bookings}</div>
                                            <div>Revenue: ${pricing.revenue.toLocaleString()}</div>
                                        </small>
                                    </td>
                                    <td>
                                        <Button
                                            variant="outline-primary"
                                            size="sm"
                                            className="me-1"
                                            onClick={() => handleEdit(pricing)}
                                            title="Edit Pricing"
                                        >
                                            <i className="fas fa-edit"></i>
                                        </Button>
                                        <Button
                                            variant="outline-danger"
                                            size="sm"
                                            onClick={() => handleDelete(pricing.id)}
                                            title="Delete Pricing"
                                        >
                                            <i className="fas fa-trash"></i>
                                        </Button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </Table>
                )}
            </Card.Body>
        </Card>
    );

    return (
        <Container fluid className="ads-pricing-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Ads Pricing Management</h4>
                <div>
                    <Button 
                        variant={viewMode === 'calendar' ? 'primary' : 'outline-primary'}
                        className="me-2"
                        onClick={() => setViewMode('calendar')}
                    >
                        <i className="fas fa-calendar me-1"></i>Calendar View
                    </Button>
                    <Button 
                        variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
                        onClick={() => setViewMode('table')}
                    >
                        <i className="fas fa-table me-1"></i>Table View
                    </Button>
                </div>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{pricingData.length}</h3>
                            <p className="mb-0">Total Pricing Entries</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {pricingData.filter(p => p.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Pricing</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {pricingData.reduce((sum, p) => sum + p.bookings, 0)}
                            </h3>
                            <p className="mb-0">Total Bookings</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                ${pricingData.reduce((sum, p) => sum + p.revenue, 0).toLocaleString()}
                            </h3>
                            <p className="mb-0">Total Revenue</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Main Content */}
            {viewMode === 'calendar' ? renderCalendarView() : renderTableView()}

            {/* Create/Edit Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-dollar-sign me-2"></i>
                        Add Pricing Entry
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Date *</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={formData.date}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            date: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Category *</Form.Label>
                                    <Form.Select
                                        value={formData.category}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            category: e.target.value
                                        })}
                                        required
                                    >
                                        <option value="">Select Category</option>
                                        {categories.map(category => (
                                            <option key={category} value={category}>
                                                {category}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Price Type *</Form.Label>
                                    <Form.Select
                                        value={formData.priceType}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            priceType: e.target.value
                                        })}
                                        required
                                    >
                                        {priceTypes.map(type => (
                                            <option key={type.value} value={type.value}>
                                                {type.label}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Release Date *</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={formData.releaseDate}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            releaseDate: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Base Price *</Form.Label>
                                    <Form.Control
                                        type="number"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={formData.basePrice}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            basePrice: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Premium Price *</Form.Label>
                                    <Form.Control
                                        type="number"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={formData.premiumPrice}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            premiumPrice: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Discount Price *</Form.Label>
                                    <Form.Control
                                        type="number"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={formData.discountPrice}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            discountPrice: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Status</Form.Label>
                                    <Form.Select
                                        value={formData.status}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            status: e.target.value
                                        })}
                                    >
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <div className="bg-light p-3 rounded">
                            <small className="text-muted">
                                <i className="fas fa-info-circle me-1"></i>
                                <strong>Note:</strong> Pricing will be available for booking from the release date. 
                                Different price types (daily, weekly, monthly) allow flexible booking options.
                            </small>
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <i className="fas fa-save me-2"></i>Save Pricing
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default AdsPricingManagement;
