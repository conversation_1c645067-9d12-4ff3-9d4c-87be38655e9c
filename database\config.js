// Database Configuration for MyGroup
// Supports both PostgreSQL and MySQL databases

const config = {
  development: {
    // PostgreSQL Configuration (recommended)
    postgres: {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'my_group_db',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      dialect: 'postgres',
      logging: console.log,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    },
    
    // MySQL Configuration (alternative)
    mysql: {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      database: process.env.DB_NAME || 'my_group_db',
      username: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'password',
      dialect: 'mysql',
      logging: console.log,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    }
  },
  
  production: {
    postgres: {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME,
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      dialect: 'postgres',
      logging: false,
      pool: {
        max: 20,
        min: 5,
        acquire: 30000,
        idle: 10000
      },
      ssl: process.env.DB_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    }
  }
};

// Get current environment
const env = process.env.NODE_ENV || 'development';
const dbType = process.env.DB_TYPE || 'postgres'; // 'postgres' or 'mysql'

// Export the appropriate configuration
module.exports = config[env][dbType];

// Database connection string builder
const buildConnectionString = (config) => {
  if (config.dialect === 'postgres') {
    return `postgresql://${config.username}:${config.password}@${config.host}:${config.port}/${config.database}`;
  } else if (config.dialect === 'mysql') {
    return `mysql://${config.username}:${config.password}@${config.host}:${config.port}/${config.database}`;
  }
  return null;
};

module.exports.connectionString = buildConnectionString(module.exports);
module.exports.config = config;
module.exports.currentConfig = config[env][dbType];
