# Admin Dashboard Database Setup Guide

## Prerequisites

1. **PostgreSQL 12+** installed and running
2. **Node.js 16+** and npm installed
3. **Database client** (pgAdmin, DBeaver, or psql command line)

## Step 1: Create Database

```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE my_group_db;
CREATE USER my_group_user WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE my_group_db TO my_group_user;

-- Connect to the new database
\c my_group_db;

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO my_group_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO my_group_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO my_group_user;
```

## Step 2: Run Schema Scripts

Execute the following scripts in order:

### 1. Base Schema (if not already exists)
```bash
psql -U my_group_user -d my_group_db -f database/schema.sql
```

### 2. Admin Dashboard Schema
```bash
psql -U my_group_user -d my_group_db -f database/admin_dashboard_schema.sql
```

### 3. Sample Data
```bash
psql -U my_group_user -d my_group_db -f database/admin_dashboard_sample_data.sql
```

## Step 3: Update Environment Variables

Update your `.env` file in the server directory:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=my_group_db
DB_USER=my_group_user
DB_PASSWORD=secure_password_here

# Server Configuration
PORT=3001
NODE_ENV=development

# Security
JWT_SECRET=your_jwt_secret_key_here_make_it_long_and_secure
BCRYPT_ROUNDS=10

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
```

## Step 4: Install Backend Dependencies

```bash
cd server
npm install
```

## Step 5: Create Backend API Routes

The following route files need to be created in `server/routes/`:

### 1. Profile Groups Routes (`server/routes/profile-groups.routes.js`)
### 2. Languages Routes (`server/routes/languages.routes.js`)
### 3. Education Routes (`server/routes/education.routes.js`)
### 4. Professions Routes (`server/routes/professions.routes.js`)
### 5. Categories Routes (`server/routes/categories.routes.js`)
### 6. Corporate Users Routes (`server/routes/corporate-users.routes.js`)

## Step 6: Database Schema Overview

### Tables Created:

1. **profile_groups** - User profile group management
2. **languages** - Language support by country
3. **education_levels** - Education level definitions
4. **professions** - Profession categories and industries
5. **main_categories** - Top-level categories (max 6 per group)
6. **category_level1** - First level subcategories
7. **category_level2** - Second level subcategories
8. **category_level3** - Third level subcategories
9. **corporate_users** - Enhanced corporate user management

### Key Features:

- **Hierarchical Categories**: 4-level category system with proper relationships
- **Constraints**: Business rules enforced at database level
- **Indexes**: Optimized for performance
- **Triggers**: Automatic timestamp updates
- **Foreign Keys**: Data integrity maintained
- **Status Management**: Active/Inactive status for all entities

## Step 7: Verify Installation

### Check Database Connection:
```bash
cd server
npm start
```

### Test API Endpoints:
```bash
# Health check
curl http://localhost:3001/api/health

# Get profile groups
curl http://localhost:3001/api/profile-groups

# Get languages
curl http://localhost:3001/api/languages

# Get education levels
curl http://localhost:3001/api/education-levels

# Get professions
curl http://localhost:3001/api/professions

# Get categories
curl http://localhost:3001/api/categories/group/1

# Get corporate users
curl http://localhost:3001/api/corporate-users
```

## Step 8: Frontend Integration

### Start Frontend:
```bash
# In project root
npm run dev
```

### Access Admin Dashboard:
1. Open http://localhost:3000
2. Login with admin credentials
3. Navigate to Admin Dashboard
4. Test all sections:
   - Profile Management
   - Languages
   - Education
   - Professions
   - Categories
   - Corporate Login

## Troubleshooting

### Common Issues:

1. **Connection Refused**
   - Check PostgreSQL is running
   - Verify connection parameters in .env
   - Check firewall settings

2. **Permission Denied**
   - Ensure user has proper database permissions
   - Check schema ownership

3. **Foreign Key Violations**
   - Run scripts in correct order
   - Ensure base schema exists first

4. **API Endpoints Not Found**
   - Verify route files are created
   - Check server.js imports routes correctly

### Database Queries for Verification:

```sql
-- Check all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check sample data
SELECT 'profile_groups' as table_name, count(*) as count FROM profile_groups
UNION ALL
SELECT 'languages', count(*) FROM languages
UNION ALL
SELECT 'education_levels', count(*) FROM education_levels
UNION ALL
SELECT 'professions', count(*) FROM professions
UNION ALL
SELECT 'main_categories', count(*) FROM main_categories
UNION ALL
SELECT 'corporate_users', count(*) FROM corporate_users;

-- Check category hierarchy
SELECT 
    pg.group_name,
    mc.name as main_category,
    c1.name as level1,
    c2.name as level2,
    c3.name as level3
FROM profile_groups pg
LEFT JOIN main_categories mc ON pg.id = mc.group_id
LEFT JOIN category_level1 c1 ON mc.id = c1.main_category_id
LEFT JOIN category_level2 c2 ON c1.id = c2.level1_category_id
LEFT JOIN category_level3 c3 ON c2.id = c3.level2_category_id
ORDER BY pg.id, mc.display_order, c1.display_order, c2.display_order, c3.display_order;
```

## Performance Optimization

### Recommended Indexes (already included):
- Foreign key columns
- Status columns
- Frequently queried columns
- Composite indexes for common queries

### Query Optimization:
- Use prepared statements
- Implement pagination for large datasets
- Cache frequently accessed data
- Use connection pooling

## Security Considerations

1. **Password Hashing**: Use bcrypt for corporate user passwords
2. **Input Validation**: Validate all inputs on both client and server
3. **SQL Injection**: Use parameterized queries
4. **File Uploads**: Validate file types and sizes
5. **Authentication**: Implement proper JWT token management
6. **Authorization**: Role-based access control

## Backup and Maintenance

### Regular Backups:
```bash
# Full database backup
pg_dump -U my_group_user -h localhost my_group_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Schema only backup
pg_dump -U my_group_user -h localhost -s my_group_db > schema_backup.sql

# Data only backup
pg_dump -U my_group_user -h localhost -a my_group_db > data_backup.sql
```

### Maintenance Tasks:
- Regular VACUUM and ANALYZE
- Monitor query performance
- Update statistics
- Check for unused indexes
- Monitor disk space usage

## Next Steps

1. Implement remaining API endpoints
2. Add file upload functionality
3. Implement email notifications
4. Add audit logging
5. Set up monitoring and alerting
6. Implement caching strategy
7. Add API rate limiting
8. Set up automated backups
