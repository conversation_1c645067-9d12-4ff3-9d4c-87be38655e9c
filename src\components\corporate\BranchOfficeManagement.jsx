import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge } from 'react-bootstrap';

const BranchOfficeManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState(''); // 'create', 'edit', 'resetPassword'
    const [editingUser, setEditingUser] = useState(null);

    // Form data
    const [formData, setFormData] = useState({
        state: '',
        district: '',
        name: '',
        mobileNumber: '',
        email: '',
        username: '',
        password: '',
        confirmPassword: '',
        status: 'Active'
    });

    // Branch office users data
    const [branchUsers, setBranchUsers] = useState([
        {
            id: 1,
            state: 'Maharashtra',
            stateId: 1,
            district: 'Mumbai',
            districtId: 1,
            name: '<PERSON><PERSON>',
            mobileNumber: '+91-9876543215',
            email: '<EMAIL>',
            username: 'rahulbranch',
            status: 'Active',
            createdAt: '2024-01-15',
            lastLogin: '2024-01-20 08:45 AM'
        },
        {
            id: 2,
            state: 'Karnataka',
            stateId: 2,
            district: 'Bangalore',
            districtId: 2,
            name: 'Sneha Rao',
            mobileNumber: '+91-9876543216',
            email: '<EMAIL>',
            username: 'snehab',
            status: 'Active',
            createdAt: '2024-01-12',
            lastLogin: '2024-01-19 04:20 PM'
        },
        {
            id: 3,
            state: 'Tamil Nadu',
            stateId: 3,
            district: 'Chennai',
            districtId: 3,
            name: 'Karthik Murugan',
            mobileNumber: '+91-9876543217',
            email: '<EMAIL>',
            username: 'karthikb',
            status: 'Inactive',
            createdAt: '2024-01-10',
            lastLogin: '2024-01-17 01:15 PM'
        },
        {
            id: 4,
            state: 'Gujarat',
            stateId: 4,
            district: 'Ahmedabad',
            districtId: 4,
            name: 'Nisha Patel',
            mobileNumber: '+91-9876543218',
            email: '<EMAIL>',
            username: 'nishab',
            status: 'Active',
            createdAt: '2024-01-08',
            lastLogin: '2024-01-20 12:30 PM'
        }
    ]);

    // States and Districts data (from Location API)
    const [states, setStates] = useState([
        { id: 1, name: 'Maharashtra' },
        { id: 2, name: 'Karnataka' },
        { id: 3, name: 'Tamil Nadu' },
        { id: 4, name: 'Gujarat' },
        { id: 5, name: 'Rajasthan' }
    ]);

    const [districts, setDistricts] = useState([
        { id: 1, name: 'Mumbai', stateId: 1 },
        { id: 2, name: 'Pune', stateId: 1 },
        { id: 3, name: 'Nagpur', stateId: 1 },
        { id: 4, name: 'Bangalore', stateId: 2 },
        { id: 5, name: 'Mysore', stateId: 2 },
        { id: 6, name: 'Chennai', stateId: 3 },
        { id: 7, name: 'Coimbatore', stateId: 3 },
        { id: 8, name: 'Ahmedabad', stateId: 4 },
        { id: 9, name: 'Surat', stateId: 4 },
        { id: 10, name: 'Jaipur', stateId: 5 }
    ]);

    const [filteredDistricts, setFilteredDistricts] = useState([]);

    useEffect(() => {
        fetchBranchUsers();
        fetchLocationData();
    }, []);

    useEffect(() => {
        // Filter districts based on selected state
        if (formData.state) {
            const filtered = districts.filter(district => 
                district.stateId === parseInt(formData.state)
            );
            setFilteredDistricts(filtered);
        } else {
            setFilteredDistricts([]);
        }
        // Reset district selection when state changes
        setFormData(prev => ({ ...prev, district: '' }));
    }, [formData.state, districts]);

    const fetchBranchUsers = async () => {
        setLoading(true);
        try {
            // API call to fetch branch users
            // const response = await fetch('/api/branch-office-users');
            // const data = await response.json();
            // setBranchUsers(data);
        } catch (error) {
            console.error('Error fetching branch users:', error);
            setError('Failed to fetch branch office users');
        } finally {
            setLoading(false);
        }
    };

    const fetchLocationData = async () => {
        try {
            // API call to fetch states and districts
            // const statesResponse = await fetch('/api/states');
            // const statesData = await statesResponse.json();
            // setStates(statesData);
            
            // const districtsResponse = await fetch('/api/districts');
            // const districtsData = await districtsResponse.json();
            // setDistricts(districtsData);
        } catch (error) {
            console.error('Error fetching location data:', error);
        }
    };

    const handleCreate = () => {
        setModalType('create');
        setEditingUser(null);
        setFormData({
            state: '',
            district: '',
            name: '',
            mobileNumber: '',
            email: '',
            username: '',
            password: '',
            confirmPassword: '',
            status: 'Active'
        });
        setShowModal(true);
    };

    const handleEdit = (user) => {
        setModalType('edit');
        setEditingUser(user);
        setFormData({
            state: user.stateId,
            district: user.districtId,
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: '',
            status: user.status
        });
        setShowModal(true);
    };

    const handleResetPassword = (user) => {
        setModalType('resetPassword');
        setEditingUser(user);
        setFormData({
            state: user.stateId,
            district: user.districtId,
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: '',
            status: user.status
        });
        setShowModal(true);
    };

    const toggleUserStatus = (userId) => {
        setBranchUsers(branchUsers.map(user => 
            user.id === userId 
                ? { ...user, status: user.status === 'Active' ? 'Inactive' : 'Active' }
                : user
        ));
        setSuccess('User status updated successfully');
    };

    const handleDelete = (userId) => {
        if (window.confirm('Are you sure you want to delete this branch office user?')) {
            setBranchUsers(branchUsers.filter(user => user.id !== userId));
            setSuccess('Branch office user deleted successfully');
        }
    };

    const validateForm = () => {
        if (modalType === 'create' || modalType === 'resetPassword') {
            if (formData.password !== formData.confirmPassword) {
                setError('Passwords do not match');
                return false;
            }
            if (formData.password.length < 6) {
                setError('Password must be at least 6 characters long');
                return false;
            }
        }

        if (!formData.email.includes('@')) {
            setError('Please enter a valid email address');
            return false;
        }

        if (formData.mobileNumber.length < 10) {
            setError('Please enter a valid mobile number');
            return false;
        }

        return true;
    };

    const sendEmailNotification = async (user, credentials = null) => {
        try {
            // Mock email sending
            console.log('Sending email to:', user.email);
            if (credentials) {
                console.log('Username:', credentials.username);
                console.log('Password:', credentials.password);
            }
            // In real implementation, call email API here
        } catch (error) {
            console.error('Error sending email:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const selectedState = states.find(s => s.id === parseInt(formData.state));
            const selectedDistrict = districts.find(d => d.id === parseInt(formData.district));
            
            if (modalType === 'create') {
                const newUser = {
                    id: Date.now(),
                    state: selectedState?.name || '',
                    stateId: parseInt(formData.state),
                    district: selectedDistrict?.name || '',
                    districtId: parseInt(formData.district),
                    name: formData.name,
                    mobileNumber: formData.mobileNumber,
                    email: formData.email,
                    username: formData.username,
                    status: formData.status,
                    createdAt: new Date().toISOString().split('T')[0],
                    lastLogin: 'Never'
                };

                setBranchUsers([...branchUsers, newUser]);
                setSuccess('Branch office user created successfully');

                // Send email with credentials
                await sendEmailNotification(newUser, {
                    username: formData.username,
                    password: formData.password
                });

            } else if (modalType === 'edit') {
                setBranchUsers(branchUsers.map(user => 
                    user.id === editingUser.id 
                        ? {
                            ...user,
                            state: selectedState?.name || user.state,
                            stateId: parseInt(formData.state),
                            district: selectedDistrict?.name || user.district,
                            districtId: parseInt(formData.district),
                            name: formData.name,
                            mobileNumber: formData.mobileNumber,
                            email: formData.email,
                            username: formData.username,
                            status: formData.status
                        }
                        : user
                ));
                setSuccess('Branch office user updated successfully');

            } else if (modalType === 'resetPassword') {
                setSuccess('Password reset successfully. New credentials sent to email.');
                
                // Send email with new credentials
                await sendEmailNotification(editingUser, {
                    username: formData.username,
                    password: formData.password
                });
            }

            setShowModal(false);
            setFormData({
                state: '',
                district: '',
                name: '',
                mobileNumber: '',
                email: '',
                username: '',
                password: '',
                confirmPassword: '',
                status: 'Active'
            });
        } catch (error) {
            setError('Failed to save branch office user');
        } finally {
            setLoading(false);
        }
    };

    const getModalTitle = () => {
        switch (modalType) {
            case 'create': return 'Create Branch Office User';
            case 'edit': return 'Edit Branch Office User';
            case 'resetPassword': return 'Reset Password';
            default: return 'Branch Office User';
        }
    };

    return (
        <Container fluid className="branch-office-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Branch Office Login Management</h4>
                <Button variant="primary" onClick={handleCreate}>
                    <i className="fas fa-plus me-2"></i>Create Branch Office User
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{branchUsers.length}</h3>
                            <p className="mb-0">Total Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {branchUsers.filter(user => user.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                {branchUsers.filter(user => user.status === 'Inactive').length}
                            </h3>
                            <p className="mb-0">Inactive Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {new Set(branchUsers.map(user => user.district)).size}
                            </h3>
                            <p className="mb-0">Districts Covered</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-store me-2"></i>Branch Office Users
                    </h5>
                </Card.Header>
                <Card.Body>
                    {branchUsers.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-store fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Branch Office Users</h5>
                            <p className="text-muted">Create your first branch office user to get started</p>
                            <Button variant="primary" onClick={handleCreate}>
                                Create First User
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>State</th>
                                    <th>District</th>
                                    <th>Name</th>
                                    <th>Mobile Number</th>
                                    <th>Email</th>
                                    <th>Username</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {branchUsers.map((user, index) => (
                                    <tr key={user.id}>
                                        <td>{index + 1}</td>
                                        <td>
                                            <Badge bg="info">{user.state}</Badge>
                                        </td>
                                        <td>
                                            <Badge bg="secondary">{user.district}</Badge>
                                        </td>
                                        <td>
                                            <strong>{user.name}</strong>
                                        </td>
                                        <td>{user.mobileNumber}</td>
                                        <td>{user.email}</td>
                                        <td>
                                            <Badge bg="dark">{user.username}</Badge>
                                        </td>
                                        <td>
                                            <Badge 
                                                bg={user.status === 'Active' ? 'success' : 'danger'}
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => toggleUserStatus(user.id)}
                                            >
                                                {user.status}
                                            </Badge>
                                        </td>
                                        <td>
                                            <small className="text-muted">{user.lastLogin}</small>
                                        </td>
                                        <td>
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleEdit(user)}
                                                title="Edit User"
                                            >
                                                <i className="fas fa-edit"></i>
                                            </Button>
                                            <Button
                                                variant="outline-warning"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleResetPassword(user)}
                                                title="Reset Password"
                                            >
                                                <i className="fas fa-key"></i>
                                            </Button>
                                            <Button
                                                variant="outline-danger"
                                                size="sm"
                                                onClick={() => handleDelete(user.id)}
                                                title="Delete User"
                                            >
                                                <i className="fas fa-trash"></i>
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Create/Edit/Reset Password Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-store me-2"></i>
                        {getModalTitle()}
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>State *</Form.Label>
                                    <Form.Select
                                        value={formData.state}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            state: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    >
                                        <option value="">Select State</option>
                                        {states.map(state => (
                                            <option key={state.id} value={state.id}>
                                                {state.name}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>District *</Form.Label>
                                    <Form.Select
                                        value={formData.district}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            district: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword' || !formData.state}
                                    >
                                        <option value="">Select District</option>
                                        {filteredDistricts.map(district => (
                                            <option key={district.id} value={district.id}>
                                                {district.name}
                                            </option>
                                        ))}
                                    </Form.Select>
                                    <Form.Text className="text-muted">
                                        {!formData.state && 'Please select a state first'}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Full Name *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter full name"
                                        value={formData.name}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            name: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Mobile Number *</Form.Label>
                                    <Form.Control
                                        type="tel"
                                        placeholder="Enter mobile number"
                                        value={formData.mobileNumber}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            mobileNumber: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Email Address *</Form.Label>
                                    <Form.Control
                                        type="email"
                                        placeholder="Enter email address"
                                        value={formData.email}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            email: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Username *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter username"
                                        value={formData.username}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            username: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Status</Form.Label>
                                    <Form.Select
                                        value={formData.status}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            status: e.target.value
                                        })}
                                        disabled={modalType === 'resetPassword'}
                                    >
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        {(modalType === 'create' || modalType === 'resetPassword') && (
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>
                                            {modalType === 'resetPassword' ? 'New Password' : 'Password'} *
                                        </Form.Label>
                                        <Form.Control
                                            type="password"
                                            placeholder="Enter password"
                                            value={formData.password}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                password: e.target.value
                                            })}
                                            required
                                            minLength={6}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Confirm Password *</Form.Label>
                                        <Form.Control
                                            type="password"
                                            placeholder="Confirm password"
                                            value={formData.confirmPassword}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                confirmPassword: e.target.value
                                            })}
                                            required
                                            minLength={6}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        )}

                        <div className="bg-light p-3 rounded">
                            <small className="text-muted">
                                <i className="fas fa-info-circle me-1"></i>
                                <strong>Note:</strong> 
                                {modalType === 'create' && ' Login credentials will be sent to the provided email address.'}
                                {modalType === 'edit' && ' User information will be updated. Password remains unchanged.'}
                                {modalType === 'resetPassword' && ' New password will be sent to the user\'s email address.'}
                            </small>
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <i className={`fas ${modalType === 'create' ? 'fa-plus' : modalType === 'edit' ? 'fa-save' : 'fa-key'} me-2`}></i>
                                    {modalType === 'create' && 'Create User'}
                                    {modalType === 'edit' && 'Update User'}
                                    {modalType === 'resetPassword' && 'Reset Password'}
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default BranchOfficeManagement;
