-- Migration: Create my_organizations table
-- Description: Stores corporate/organization information
-- Created: 2024-01-18

CREATE TABLE my_organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_name VARCHAR(255) NOT NULL,
    organization_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON>reate indexes
CREATE INDEX idx_organizations_code ON my_organizations(organization_code);
CREATE INDEX idx_organizations_active ON my_organizations(is_active);
CREATE INDEX idx_organizations_name ON my_organizations(organization_name);
