import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Modal, Form, Alert, Table, Badge } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import Navbar from './Navbar';

// Import Branch Dashboard Components
// import UserManagement from './branch/UserManagement';
import TransactionManagement from './branch/TransactionManagement';
// import ServicesManagement from './branch/ServicesManagement';
// import CustomerManagement from './branch/CustomerManagement';
// import BranchReports from './branch/BranchReports';
// import ChangePasswordManagement from './branch/ChangePasswordManagement';
import NotificationSystem from './NotificationSystem';

import '../styles/BranchDashboard.css';

const BranchDashboard = () => {
  const { currentUser, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  // Sidebar menu items for Branch Dashboard
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    {
      id: 'customer-management',
      label: 'Customer Management',
      icon: 'fas fa-users',
      hasSubmenu: true,
      submenu: [
        { id: 'customers', label: 'Customer Accounts' },
        { id: 'new-customer', label: 'New Customer' },
        { id: 'customer-services', label: 'Customer Services' }
      ]
    },
    {
      id: 'transaction-management',
      label: 'Transaction Management',
      icon: 'fas fa-exchange-alt',
      hasSubmenu: true,
      submenu: [
        { id: 'transactions', label: 'All Transactions' },
        { id: 'deposits', label: 'Deposits' },
        { id: 'withdrawals', label: 'Withdrawals' },
        { id: 'transfers', label: 'Transfers' }
      ]
    },
    {
      id: 'services-management',
      label: 'Services Management',
      icon: 'fas fa-cogs',
      hasSubmenu: true,
      submenu: [
        { id: 'available-services', label: 'Available Services' },
        { id: 'service-requests', label: 'Service Requests' },
        { id: 'service-history', label: 'Service History' }
      ]
    },
    {
      id: 'user-management',
      label: 'User Management',
      icon: 'fas fa-user-friends',
      hasSubmenu: true,
      submenu: [
        { id: 'branch-users', label: 'Branch Users' },
        { id: 'user-permissions', label: 'User Permissions' },
        { id: 'user-activity', label: 'User Activity' }
      ]
    },
    {
      id: 'reports-analytics',
      label: 'Reports & Analytics',
      icon: 'fas fa-chart-bar',
      hasSubmenu: true,
      submenu: [
        { id: 'daily-reports', label: 'Daily Reports' },
        { id: 'monthly-reports', label: 'Monthly Reports' },
        { id: 'performance-analytics', label: 'Performance Analytics' }
      ]
    },
    { id: 'notifications', label: 'Notifications', icon: 'fas fa-bell' },
    { id: 'change-password', label: 'Change Password', icon: 'fas fa-key' }
  ];

  // Mock data for branch operations
  const [transactions] = useState([
    { id: 1, type: 'Deposit', amount: 5000, customer: 'John Doe', date: '2024-01-18 09:30 AM', status: 'Completed' },
    { id: 2, type: 'Withdrawal', amount: 2000, customer: 'Jane Smith', date: '2024-01-18 10:15 AM', status: 'Completed' },
    { id: 3, type: 'Transfer', amount: 1500, customer: 'Bob Johnson', date: '2024-01-18 11:45 AM', status: 'Pending' },
    { id: 4, type: 'Deposit', amount: 3200, customer: 'Alice Brown', date: '2024-01-18 14:20 PM', status: 'Completed' },
    { id: 5, type: 'Withdrawal', amount: 800, customer: 'Charlie Wilson', date: '2024-01-18 15:10 PM', status: 'Completed' },
  ]);

  const [customers] = useState([
    { id: 1, name: 'John Doe', accountNumber: 'ACC001', balance: 15000, status: 'Active', joinDate: '2023-06-15' },
    { id: 2, name: 'Jane Smith', accountNumber: 'ACC002', balance: 8500, status: 'Active', joinDate: '2023-07-20' },
    { id: 3, name: 'Bob Johnson', accountNumber: 'ACC003', balance: 12300, status: 'Active', joinDate: '2023-08-10' },
    { id: 4, name: 'Alice Brown', accountNumber: 'ACC004', balance: 6700, status: 'Inactive', joinDate: '2023-09-05' },
  ]);

  // Calculate statistics
  const totalDeposits = transactions
    .filter(t => t.type === 'Deposit')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalWithdrawals = transactions
    .filter(t => t.type === 'Withdrawal')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(c => c.status === 'Active').length;
  const totalBalance = customers.reduce((sum, c) => sum + c.balance, 0);

  const renderMainContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h4>Branch Dashboard</h4>
              <div className="d-flex gap-2">
                <Button variant="outline-primary" size="sm">
                  <i className="fas fa-download me-2"></i>Export Report
                </Button>
                <Button variant="primary" size="sm">
                  <i className="fas fa-plus me-2"></i>Quick Action
                </Button>
              </div>
            </div>

            {/* Dashboard Statistics */}
            <Row className="mb-4">
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-arrow-down fa-2x text-success mb-2"></i>
                    <h3 className="text-success">${totalDeposits.toLocaleString()}</h3>
                    <p className="mb-0">Total Deposits Today</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-arrow-up fa-2x text-danger mb-2"></i>
                    <h3 className="text-danger">${totalWithdrawals.toLocaleString()}</h3>
                    <p className="mb-0">Total Withdrawals Today</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-users fa-2x text-info mb-2"></i>
                    <h3 className="text-info">{activeCustomers}</h3>
                    <p className="mb-0">Active Customers</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-exchange-alt fa-2x text-warning mb-2"></i>
                    <h3 className="text-warning">{transactions.length}</h3>
                    <p className="mb-0">Transactions Today</p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Quick Actions and Branch Information */}
            <Row className="mb-4">
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Row>
                      <Col md={6} className="mb-3">
                        <Button variant="success" className="w-100" size="lg">
                          <i className="fas fa-plus me-2"></i>New Deposit
                        </Button>
                      </Col>
                      <Col md={6} className="mb-3">
                        <Button variant="warning" className="w-100" size="lg">
                          <i className="fas fa-minus me-2"></i>Process Withdrawal
                        </Button>
                      </Col>
                      <Col md={6} className="mb-3">
                        <Button variant="info" className="w-100" size="lg">
                          <i className="fas fa-search me-2"></i>Account Inquiry
                        </Button>
                      </Col>
                      <Col md={6} className="mb-3">
                        <Button variant="primary" className="w-100" size="lg">
                          <i className="fas fa-user-plus me-2"></i>New Customer
                        </Button>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-building me-2"></i>Branch Information
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Table borderless>
                      <tbody>
                        <tr>
                          <td><strong>Branch Code:</strong></td>
                          <td>BR001</td>
                        </tr>
                        <tr>
                          <td><strong>Manager:</strong></td>
                          <td>{currentUser?.name}</td>
                        </tr>
                        <tr>
                          <td><strong>Location:</strong></td>
                          <td>Main Street Branch</td>
                        </tr>
                        <tr>
                          <td><strong>Status:</strong></td>
                          <td><Badge bg="success">Active</Badge></td>
                        </tr>
                        <tr>
                          <td><strong>Total Balance:</strong></td>
                          <td className="text-success"><strong>${totalBalance.toLocaleString()}</strong></td>
                        </tr>
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Recent Transactions */}
            <Row>
              <Col md={12}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-history me-2"></i>Recent Transactions
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Table responsive hover>
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Type</th>
                          <th>Customer</th>
                          <th>Amount</th>
                          <th>Date & Time</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {transactions.slice(0, 5).map(transaction => (
                          <tr key={transaction.id}>
                            <td>#{transaction.id.toString().padStart(4, '0')}</td>
                            <td>
                              <Badge
                                bg={transaction.type === 'Deposit' ? 'success' :
                                    transaction.type === 'Withdrawal' ? 'danger' : 'info'}
                              >
                                {transaction.type}
                              </Badge>
                            </td>
                            <td>{transaction.customer}</td>
                            <td>${transaction.amount.toLocaleString()}</td>
                            <td>{transaction.date}</td>
                            <td>
                              <Badge bg={transaction.status === 'Completed' ? 'success' : 'warning'}>
                                {transaction.status}
                              </Badge>
                            </td>
                            <td>
                              <Button variant="outline-primary" size="sm">
                                <i className="fas fa-eye"></i>
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                    <div className="text-center mt-3">
                      <Button variant="outline-primary" onClick={() => setActiveSection('transactions')}>
                        View All Transactions
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </div>
        );

      // Placeholder cases for components to be created
      case 'customers':
        return (
          <div className="text-center py-5">
            <i className="fas fa-users fa-3x text-muted mb-3"></i>
            <h3>Customer Management</h3>
            <p className="text-muted">Manage customer accounts and profiles</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'transactions':
        return <TransactionManagement />;

      case 'available-services':
        return (
          <div className="text-center py-5">
            <i className="fas fa-cogs fa-3x text-muted mb-3"></i>
            <h3>Services Management</h3>
            <p className="text-muted">Manage available services and requests</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'branch-users':
        return (
          <div className="text-center py-5">
            <i className="fas fa-user-friends fa-3x text-muted mb-3"></i>
            <h3>User Management</h3>
            <p className="text-muted">Manage branch users and permissions</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'daily-reports':
        return (
          <div className="text-center py-5">
            <i className="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h3>Reports & Analytics</h3>
            <p className="text-muted">Generate reports and view analytics</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'notifications':
        return <NotificationSystem />;

      case 'change-password':
        return (
          <div className="text-center py-5">
            <i className="fas fa-key fa-3x text-muted mb-3"></i>
            <h3>Change Password</h3>
            <p className="text-muted">Update your account password</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      default:
        return (
          <div className="text-center py-5">
            <i className="fas fa-store fa-3x text-muted mb-3"></i>
            <h3>Welcome to Branch Dashboard</h3>
            <p className="text-muted">Select a menu item to get started</p>
          </div>
        );
    }
  };

  return (
    <div className="branch-dashboard">
      <Navbar />

      <div className="dashboard-container">
        {/* Sidebar */}
        <div className="sidebar">
          <div className="sidebar-header">
            <h5 className="text-white mb-0">
              <i className="fas fa-store me-2"></i>Branch Office
            </h5>
            <small className="text-white-50">Management Portal</small>
          </div>

          <div className="sidebar-menu">
            {sidebarItems.map((item) => (
              <div key={item.id} className="menu-item">
                {item.hasSubmenu ? (
                  <div className="menu-group">
                    <div className="menu-header">
                      <i className={`${item.icon} me-2`}></i>
                      {item.label}
                    </div>
                    <div className="submenu">
                      {item.submenu.map((subItem) => (
                        <div
                          key={subItem.id}
                          className={`submenu-item ${activeSection === subItem.id ? 'active' : ''}`}
                          onClick={() => setActiveSection(subItem.id)}
                        >
                          {subItem.label}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div
                    className={`menu-link ${activeSection === item.id ? 'active' : ''}`}
                    onClick={() => setActiveSection(item.id)}
                  >
                    <i className={`${item.icon} me-2`}></i>
                    {item.label}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <Container fluid>
            {error && (
              <Alert variant="danger" dismissible onClose={() => setError('')}>
                {error}
              </Alert>
            )}
            {success && (
              <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                {success}
              </Alert>
            )}

            {renderMainContent()}
          </Container>
        </div>
      </div>
    </div>
  );
};

export default BranchDashboard;
