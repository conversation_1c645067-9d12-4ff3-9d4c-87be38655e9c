const GroupData = require('../models/GroupData');

// Get all groups
const getAllGroups = async (req, res) => {
  try {
    const groups = await GroupData.getAll();
    
    res.json({
      message: 'Groups retrieved successfully',
      groups
    });

  } catch (error) {
    console.error('Get all groups error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get group by ID
const getGroupById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const group = await GroupData.findById(id);
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    res.json({
      message: 'Group retrieved successfully',
      group
    });

  } catch (error) {
    console.error('Get group by ID error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get group categories
const getGroupCategories = async (req, res) => {
  try {
    const { id } = req.params;
    
    const categories = await GroupData.getGroupCategories(id);
    
    res.json({
      message: 'Group categories retrieved successfully',
      categories
    });

  } catch (error) {
    console.error('Get group categories error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get all groups with categories
const getAllGroupsWithCategories = async (req, res) => {
  try {
    const groupsWithCategories = await GroupData.getAllWithCategories();
    
    res.json({
      message: 'Groups with categories retrieved successfully',
      groups: groupsWithCategories
    });

  } catch (error) {
    console.error('Get all groups with categories error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Create new group
const createGroup = async (req, res) => {
  try {
    const groupData = req.body;
    
    const newGroup = await GroupData.create(groupData);
    
    res.status(201).json({
      message: 'Group created successfully',
      group: newGroup
    });

  } catch (error) {
    console.error('Create group error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Update group
const updateGroup = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined || updateData[key] === '') {
        delete updateData[key];
      }
    });

    const updatedGroup = await GroupData.update(id, updateData);
    if (!updatedGroup) {
      return res.status(404).json({ message: 'Group not found' });
    }

    res.json({
      message: 'Group updated successfully',
      group: updatedGroup
    });

  } catch (error) {
    console.error('Update group error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Delete group
const deleteGroup = async (req, res) => {
  try {
    const { id } = req.params;
    
    const deletedGroup = await GroupData.delete(id);
    if (!deletedGroup) {
      return res.status(404).json({ message: 'Group not found' });
    }

    res.json({
      message: 'Group deleted successfully',
      group: deletedGroup
    });

  } catch (error) {
    console.error('Delete group error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  getAllGroups,
  getGroupById,
  getGroupCategories,
  getAllGroupsWithCategories,
  createGroup,
  updateGroup,
  deleteGroup
};
