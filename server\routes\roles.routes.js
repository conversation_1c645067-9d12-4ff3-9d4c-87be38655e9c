import express from 'express';
import RolesController from '../controllers/roles.controller.js';

const router = express.Router();

// Role-based authentication routes
router.post('/admin/login', RolesController.adminLogin);
router.post('/corporate/login', RolesController.corporateLogin);
router.post('/headoffice/login', RolesController.headOfficeLogin);
router.post('/regional/login', RolesController.regionalLogin);
router.post('/branch/login', RolesController.branchLogin);

// Role-based user management routes
router.post('/users', RolesController.createRoleUser);
router.get('/users/:role', RolesController.getUsersByRole);
router.put('/users/:userId', RolesController.updateRoleUser);
router.delete('/users/:userId', RolesController.deleteRoleUser);

// Role hierarchy and permissions
router.get('/hierarchy/:role', RolesController.getRoleHierarchy);

// Dashboard data
router.get('/dashboard', RolesController.getDashboardData);

// Export router
export default router;
