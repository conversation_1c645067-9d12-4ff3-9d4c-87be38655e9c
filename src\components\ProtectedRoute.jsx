import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Navigate to="/" replace />;
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(currentUser.role)) {
    // Redirect to appropriate dashboard based on user role
    switch (currentUser.role) {
      case 'SUPER_ADMIN':
        return <Navigate to="/super-admin" replace />;
      case 'CORPORATE':
        return <Navigate to="/corporate" replace />;
      case 'HEAD_OFFICE':
        return <Navigate to="/head-office" replace />;
      case 'REGIONAL':
        return <Navigate to="/regional" replace />;
      case 'BRANCH':
        return <Navigate to="/branch" replace />;
      default:
        return <Navigate to="/" replace />;
    }
  }

  return children;
};

export default ProtectedRoute;
