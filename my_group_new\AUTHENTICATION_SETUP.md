# 🔐 Authentication & Admin Dashboard Setup Guide

## Overview

The My Group New application now implements a comprehensive authentication system similar to the original mygroup application:

- **🚫 No Home Screen Access Without Login**: Users must authenticate before accessing any content
- **👤 Role-Based Access Control**: Different user roles have different permissions
- **🛡️ Admin Dashboard**: Comprehensive admin panel for user management
- **📱 Mobile-First Design**: Optimized for mobile devices with desktop restrictions

## 🔑 Authentication Flow

### 1. Initial Access
- Users see a welcome screen with login requirement
- No content is accessible without authentication
- Mobile-first design with desktop restriction message

### 2. Login Process
- Modal-based login system
- JWT token authentication
- Role-based redirection after login
- Secure password hashing with bcrypt

### 3. Role-Based Access
- **Admin/App Admin (roles 1, 8)**: Full access + Admin Dashboard
- **Other Roles (2-7)**: Standard user access
- **Customers (role 6)**: Basic user functionality

## 🛠️ Setup Instructions

### 1. Database Setup

First, ensure your PostgreSQL database is set up with the schema:

```sql
-- Create database
CREATE DATABASE my_group_db;

-- Run the schema from your existing database folder
\i ../database/schema_new.sql
```

### 2. Backend Configuration

Update `backend/.env` with your database credentials:

```env
# Server Configuration
PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=my_group_db
DB_USER=your_postgres_user
DB_PASSWORD=your_postgres_password

# JWT Configuration
JWT_SECRET=your_secret_key_here_make_it_long_and_secure
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
```

### 3. Create Default Admin User

Run the admin user creation script:

```bash
cd backend
npm run create-admin
```

This creates a default admin user:
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: Admin (role_id: 1)

### 4. Start the Application

**Terminal 1 - Backend:**
```bash
cd backend
npm run dev
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

## 🧪 Testing the Authentication Flow

### 1. Initial Access Test
1. Open http://localhost:3000 on mobile browser or mobile view
2. ✅ Should see welcome screen requiring login
3. ✅ No home screen content should be visible
4. ✅ Desktop browsers should show "not available" message

### 2. Login Test
1. Click "Login" button
2. ✅ Login modal should appear
3. Enter admin credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`
4. ✅ Should successfully login and see home screen

### 3. Admin Dashboard Test
1. After logging in as admin, tap the hamburger menu (☰)
2. ✅ Should see "Admin Dashboard" option in menu
3. Tap "Admin Dashboard"
4. ✅ Should navigate to `/admin` route
5. ✅ Should see comprehensive admin dashboard with:
   - User statistics
   - User management table
   - Sidebar navigation
   - Mobile-responsive design

### 4. User Management Test
1. In admin dashboard, go to "User Management" tab
2. ✅ Should see list of users
3. ✅ Should be able to view user details
4. ✅ Should be able to activate/deactivate users
5. ✅ Should see user statistics

### 5. Role-Based Access Test
1. Create a regular user (role_id: 6)
2. Login with regular user credentials
3. ✅ Should see home screen
4. ✅ Should NOT see "Admin Dashboard" in menu
5. ✅ Attempting to access `/admin` directly should show access denied

## 🎯 Key Features Implemented

### ✅ Authentication Guard
- Home screen requires login
- JWT token validation
- Automatic token refresh
- Secure logout functionality

### ✅ Admin Dashboard
- **Dashboard Overview**: User statistics and activity
- **User Management**: View, edit, activate/deactivate users
- **Settings**: Application configuration
- **Mobile Responsive**: Sidebar navigation for mobile
- **Role-Based Access**: Only admins can access

### ✅ Role-Based Security
- Route protection based on user roles
- API endpoint security with middleware
- Frontend role checking
- Access denied screens for unauthorized users

### ✅ Mobile-First Design
- Responsive admin dashboard
- Mobile-optimized navigation
- Touch-friendly interface
- Desktop restriction maintained

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/verify` - Verify JWT token
- `GET /api/auth/profile` - Get user profile

### Admin-Only Endpoints
- `GET /api/users` - Get all users (admin only)
- `PATCH /api/users/:id/status` - Update user status (admin only)
- `POST /api/groups` - Create group (admin only)
- `PUT /api/groups/:id` - Update group (admin only)
- `DELETE /api/groups/:id` - Delete group (admin only)

## 🚨 Security Features

### ✅ Password Security
- bcrypt hashing with salt rounds
- Minimum password requirements
- Secure password validation

### ✅ JWT Security
- Secure token generation
- Token expiration handling
- Automatic token refresh
- Secure token storage

### ✅ API Security
- CORS protection
- Authentication middleware
- Role-based authorization
- Input validation and sanitization

### ✅ Frontend Security
- Protected routes
- Role-based component rendering
- Secure token handling
- XSS protection

## 📱 Mobile Features

### ✅ Responsive Design
- Mobile-first admin dashboard
- Touch-friendly navigation
- Optimized for small screens
- Sidebar navigation for mobile

### ✅ Mobile Navigation
- Hamburger menu
- Swipe gestures
- Touch-optimized buttons
- Mobile-friendly modals

## 🎉 Success Criteria

The authentication system is working correctly if:

1. ✅ Users cannot access home screen without login
2. ✅ Admin users can access admin dashboard
3. ✅ Regular users cannot access admin dashboard
4. ✅ All API endpoints are properly secured
5. ✅ Mobile interface is fully functional
6. ✅ Desktop shows restriction message
7. ✅ User management works correctly
8. ✅ Role-based access is enforced

## 🔄 Next Steps

1. **User Registration**: Test user registration flow
2. **Profile Management**: Test profile editing functionality
3. **Group Management**: Test group and category management
4. **Mobile Testing**: Test on actual mobile devices
5. **Security Testing**: Verify all security measures

## 📞 Support

If you encounter any issues:

1. Check database connection
2. Verify environment variables
3. Ensure admin user is created
4. Check browser console for errors
5. Verify API endpoints are responding

The application now follows the same authentication pattern as the original mygroup application with enhanced mobile-first design and comprehensive admin functionality!
