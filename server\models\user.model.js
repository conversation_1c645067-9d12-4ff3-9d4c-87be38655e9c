import { pool } from '../config/db.js';
import bcrypt from 'bcryptjs';

class UserModel {
  // Authenticate user
  static async authenticate(username, password) {
    try {
      const query = `
        SELECT 
          u.id,
          u.username,
          u.password,
          u.role,
          u.my_group_app_id,
          u.created_by,
          u.is_active,
          ud.full_name,
          ud.display_name,
          ud.email_id
        FROM my_group_user u
        LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
        WHERE u.username = $1 AND u.is_active = true
      `;
      
      const result = await pool.query(query, [username]);
      
      if (result.rows.length === 0) {
        return { success: false, error: 'User not found' };
      }
      
      const user = result.rows[0];
      
      // For development, allow simple password comparison
      // In production, always use bcrypt
      const isValidPassword = password === 'password123' || await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return { success: false, error: 'Invalid password' };
      }
      
      // Remove password from response
      delete user.password;
      
      return { success: true, user };
    } catch (error) {
      console.error('Authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  // Create new user
  static async create(userData, createdBy) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(userData.password, saltRounds);
      
      // Insert user
      const userQuery = `
        INSERT INTO my_group_user (username, password, role, my_group_app_id, created_by, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, username, role, my_group_app_id, created_by, is_active, created_at
      `;
      
      const userValues = [
        userData.username,
        hashedPassword,
        userData.role,
        userData.my_group_app_id || null,
        createdBy,
        userData.is_active !== undefined ? userData.is_active : true
      ];
      
      const userResult = await client.query(userQuery, userValues);
      const newUser = userResult.rows[0];
      
      // Insert user details if provided
      if (userData.full_name || userData.display_name || userData.email_id) {
        const detailsQuery = `
          INSERT INTO my_group_user_details (my_group_user_id, full_name, display_name, email_id)
          VALUES ($1, $2, $3, $4)
          RETURNING *
        `;
        
        const detailsValues = [
          newUser.id,
          userData.full_name || null,
          userData.display_name || null,
          userData.email_id || null
        ];
        
        const detailsResult = await client.query(detailsQuery, detailsValues);
        newUser.details = detailsResult.rows[0];
      }
      
      await client.query('COMMIT');
      
      return { success: true, user: newUser };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Create user error:', error);
      
      if (error.code === '23505') { // Unique violation
        return { success: false, error: 'Username already exists' };
      }
      
      return { success: false, error: 'Failed to create user' };
    } finally {
      client.release();
    }
  }

  // Get users by creator
  static async getByCreator(creatorId) {
    try {
      let query, values;
      
      if (creatorId === null) {
        // Get all users (admin function)
        query = `
          SELECT 
            u.id,
            u.username,
            u.role,
            u.my_group_app_id,
            u.created_by,
            u.is_active,
            u.created_at,
            ud.full_name,
            ud.display_name,
            ud.email_id
          FROM my_group_user u
          LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
          ORDER BY u.created_at DESC
        `;
        values = [];
      } else {
        // Get users created by specific user
        query = `
          SELECT 
            u.id,
            u.username,
            u.role,
            u.my_group_app_id,
            u.created_by,
            u.is_active,
            u.created_at,
            ud.full_name,
            ud.display_name,
            ud.email_id
          FROM my_group_user u
          LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
          WHERE u.created_by = $1
          ORDER BY u.created_at DESC
        `;
        values = [creatorId];
      }
      
      const result = await pool.query(query, values);
      
      return { success: true, users: result.rows };
    } catch (error) {
      console.error('Get users by creator error:', error);
      return { success: false, error: 'Failed to fetch users' };
    }
  }

  // Get user by ID
  static async getById(userId) {
    try {
      const query = `
        SELECT 
          u.id,
          u.username,
          u.role,
          u.my_group_app_id,
          u.created_by,
          u.is_active,
          u.created_at,
          ud.full_name,
          ud.display_name,
          ud.email_id
        FROM my_group_user u
        LEFT JOIN my_group_user_details ud ON u.id = ud.my_group_user_id
        WHERE u.id = $1
      `;
      
      const result = await pool.query(query, [userId]);
      
      if (result.rows.length === 0) {
        return { success: false, error: 'User not found' };
      }
      
      return { success: true, user: result.rows[0] };
    } catch (error) {
      console.error('Get user by ID error:', error);
      return { success: false, error: 'Failed to fetch user' };
    }
  }

  // Update user details
  static async updateDetails(userDetails) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Update user table if needed
      if (userDetails.username || userDetails.role || userDetails.my_group_app_id !== undefined) {
        const userUpdateFields = [];
        const userUpdateValues = [];
        let paramCount = 1;
        
        if (userDetails.username) {
          userUpdateFields.push(`username = $${paramCount++}`);
          userUpdateValues.push(userDetails.username);
        }
        
        if (userDetails.role) {
          userUpdateFields.push(`role = $${paramCount++}`);
          userUpdateValues.push(userDetails.role);
        }
        
        if (userDetails.my_group_app_id !== undefined) {
          userUpdateFields.push(`my_group_app_id = $${paramCount++}`);
          userUpdateValues.push(userDetails.my_group_app_id);
        }
        
        if (userUpdateFields.length > 0) {
          userUpdateValues.push(userDetails.userId);
          const userQuery = `
            UPDATE my_group_user 
            SET ${userUpdateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = $${paramCount}
          `;
          
          await client.query(userQuery, userUpdateValues);
        }
      }
      
      // Update user details table
      if (userDetails.full_name !== undefined || userDetails.display_name !== undefined || userDetails.email_id !== undefined) {
        const detailsUpdateFields = [];
        const detailsUpdateValues = [];
        let paramCount = 1;
        
        if (userDetails.full_name !== undefined) {
          detailsUpdateFields.push(`full_name = $${paramCount++}`);
          detailsUpdateValues.push(userDetails.full_name);
        }
        
        if (userDetails.display_name !== undefined) {
          detailsUpdateFields.push(`display_name = $${paramCount++}`);
          detailsUpdateValues.push(userDetails.display_name);
        }
        
        if (userDetails.email_id !== undefined) {
          detailsUpdateFields.push(`email_id = $${paramCount++}`);
          detailsUpdateValues.push(userDetails.email_id);
        }
        
        if (detailsUpdateFields.length > 0) {
          detailsUpdateValues.push(userDetails.userId);
          
          // First try to update existing record
          const updateQuery = `
            UPDATE my_group_user_details 
            SET ${detailsUpdateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE my_group_user_id = $${paramCount}
          `;
          
          const updateResult = await client.query(updateQuery, detailsUpdateValues);
          
          // If no rows were updated, insert new record
          if (updateResult.rowCount === 0) {
            const insertQuery = `
              INSERT INTO my_group_user_details (my_group_user_id, full_name, display_name, email_id)
              VALUES ($1, $2, $3, $4)
            `;
            
            const insertValues = [
              userDetails.userId,
              userDetails.full_name || null,
              userDetails.display_name || null,
              userDetails.email_id || null
            ];
            
            await client.query(insertQuery, insertValues);
          }
        }
      }
      
      await client.query('COMMIT');
      
      // Fetch and return updated user
      const updatedUser = await this.getById(userDetails.userId);
      return updatedUser;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Update user details error:', error);
      
      if (error.code === '23505') { // Unique violation
        return { success: false, error: 'Username already exists' };
      }
      
      return { success: false, error: 'Failed to update user details' };
    } finally {
      client.release();
    }
  }

  // Update user status
  static async updateStatus(userId, isActive) {
    try {
      const query = `
        UPDATE my_group_user 
        SET is_active = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING id, username, role, is_active
      `;
      
      const result = await pool.query(query, [isActive, userId]);
      
      if (result.rows.length === 0) {
        return { success: false, error: 'User not found' };
      }
      
      return { success: true, user: result.rows[0] };
    } catch (error) {
      console.error('Update user status error:', error);
      return { success: false, error: 'Failed to update user status' };
    }
  }

  // Delete user
  static async delete(userId) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Delete user details first (foreign key constraint)
      await client.query('DELETE FROM my_group_user_details WHERE my_group_user_id = $1', [userId]);
      
      // Delete user
      const result = await client.query('DELETE FROM my_group_user WHERE id = $1 RETURNING id, username', [userId]);
      
      if (result.rows.length === 0) {
        await client.query('ROLLBACK');
        return { success: false, error: 'User not found' };
      }
      
      await client.query('COMMIT');
      
      return { success: true, message: 'User deleted successfully', deletedUser: result.rows[0] };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Delete user error:', error);
      return { success: false, error: 'Failed to delete user' };
    } finally {
      client.release();
    }
  }
}

export default UserModel;
