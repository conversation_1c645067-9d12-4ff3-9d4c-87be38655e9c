# Admin Dashboard Database Schema Diagram

## Entity Relationship Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   users (base)  │    │   countries     │    │ profile_groups  │
│                 │    │                 │    │                 │
│ • id (UUID)     │    │ • id            │    │ • id            │
│ • username      │    │ • name          │    │ • group_type    │
│ • role          │    │ • code          │    │ • group_name    │
│ • ...           │    │ • ...           │    │ • icon_url      │
└─────────────────┘    └─────────────────┘    │ • logo_url      │
         │                       │             │ • background_color│
         │                       │             │ • created_by ───┘
         │                       │             └─────────────────┘
         │                       │                      │
         │                       │                      │
         ▼                       ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ corporate_users │    │   languages     │    │ main_categories │
│                 │    │                 │    │                 │
│ • id (UUID)     │    │ • id            │    │ • id            │
│ • full_name     │    │ • country_id ───┤    │ • group_id ─────┤
│ • email_id      │    │ • language_1    │    │ • name          │
│ • username      │    │ • language_2    │    │ • description   │
│ • password_hash │    │ • description   │    │ • icon_class    │
│ • role          │    │ • created_by ───┤    │ • display_order │
│ • status        │    │ • ...           │    │ • created_by ───┤
│ • created_by ───┤    └─────────────────┘    └─────────────────┘
│ • ...           │                                    │
└─────────────────┘                                    │
         │                                             ▼
         │                            ┌─────────────────┐
         │                            │ category_level1 │
         │                            │                 │
         │                            │ • id            │
         │                            │ • main_cat_id ──┤
         │                            │ • name          │
         │                            │ • description   │
         │                            │ • icon_class    │
         │                            │ • display_order │
         │                            │ • created_by ───┤
         │                            └─────────────────┘
         │                                     │
         │                                     ▼
         │                            ┌─────────────────┐
         │                            │ category_level2 │
         │                            │                 │
         │                            │ • id            │
         │                            │ • level1_cat_id─┤
         │                            │ • name          │
         │                            │ • description   │
         │                            │ • icon_class    │
         │                            │ • display_order │
         │                            │ • created_by ───┤
         │                            └─────────────────┘
         │                                     │
         │                                     ▼
         │                            ┌─────────────────┐
         │                            │ category_level3 │
         │                            │                 │
         │                            │ • id            │
         │                            │ • level2_cat_id─┤
         │                            │ • name          │
         │                            │ • description   │
         │                            │ • display_order │
         │                            │ • created_by ───┤
         │                            └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│ education_levels│    │   professions   │
│                 │    │                 │
│ • id            │    │ • id            │
│ • education_level│   │ • profession_name│
│ • description   │    │ • description   │
│ • category      │    │ • category      │
│ • display_order │    │ • industry      │
│ • status        │    │ • display_order │
│ • created_by ───┤    │ • status        │
│ • ...           │    │ • created_by ───┤
└─────────────────┘    │ • ...           │
                       └─────────────────┘
```

## Table Relationships

### 1. Profile Groups → Main Categories (1:N)
- One profile group can have multiple main categories (max 6)
- Each main category belongs to one profile group

### 2. Category Hierarchy (1:N at each level)
```
profile_groups (1) → main_categories (N)
main_categories (1) → category_level1 (N)
category_level1 (1) → category_level2 (N)
category_level2 (1) → category_level3 (N)
```

### 3. Countries → Languages (1:N)
- One country can have multiple language configurations
- Each language entry belongs to one country

### 4. Users → All Tables (1:N)
- All tables have `created_by` field referencing users table
- Tracks who created each record

## Detailed Table Structures

### Profile Groups
```sql
profile_groups (
    id SERIAL PRIMARY KEY,
    group_type VARCHAR(50) CHECK (group_type IN ('My Apps', 'My Company', 'My Online Apps', 'My Offline Apps')),
    group_name VARCHAR(100) NOT NULL,
    icon_url TEXT,
    logo_url TEXT,
    name_image_url TEXT,
    background_color VARCHAR(7) DEFAULT '#ffffff',
    status VARCHAR(20) DEFAULT 'Active',
    display_order INTEGER DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### Languages
```sql
languages (
    id SERIAL PRIMARY KEY,
    country_id INTEGER REFERENCES countries(id) ON DELETE CASCADE,
    language_1 VARCHAR(100) NOT NULL,
    language_2 VARCHAR(100),
    description TEXT,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### Education Levels
```sql
education_levels (
    id SERIAL PRIMARY KEY,
    education_level VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) CHECK (category IN ('Primary', 'Secondary', ...)),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### Professions
```sql
professions (
    id SERIAL PRIMARY KEY,
    profession_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) CHECK (category IN ('Technology', 'Healthcare', ...)),
    industry VARCHAR(100) CHECK (industry IN ('Information Technology', ...)),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### Category Hierarchy
```sql
main_categories (
    id SERIAL PRIMARY KEY,
    group_id INTEGER REFERENCES profile_groups(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_class VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT max_main_categories_per_group CHECK (...)
)

category_level1 (
    id SERIAL PRIMARY KEY,
    main_category_id INTEGER REFERENCES main_categories(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_class VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)

category_level2 (
    id SERIAL PRIMARY KEY,
    level1_category_id INTEGER REFERENCES category_level1(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_class VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)

category_level3 (
    id SERIAL PRIMARY KEY,
    level2_category_id INTEGER REFERENCES category_level2(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### Corporate Users (Enhanced)
```sql
corporate_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name VARCHAR(100) NOT NULL,
    mobile_number VARCHAR(20) NOT NULL,
    email_id VARCHAR(100) NOT NULL UNIQUE,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'CORPORATE',
    status VARCHAR(20) DEFAULT 'Active',
    last_login TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

## Indexes and Performance

### Primary Indexes
- All tables have primary key indexes
- Foreign key columns are automatically indexed

### Custom Indexes
```sql
-- Performance indexes
CREATE INDEX idx_profile_groups_type ON profile_groups(group_type);
CREATE INDEX idx_languages_country ON languages(country_id);
CREATE INDEX idx_education_category ON education_levels(category);
CREATE INDEX idx_professions_category ON professions(category);
CREATE INDEX idx_main_categories_group ON main_categories(group_id);
CREATE INDEX idx_corporate_users_email ON corporate_users(email_id);
```

## Constraints and Business Rules

### 1. Profile Groups
- Unique combination of group_type and group_name
- Valid group_type values enforced
- Status must be 'Active' or 'Inactive'

### 2. Categories
- Maximum 6 main categories per group (enforced by constraint)
- Hierarchical integrity maintained by foreign keys
- Cascade delete ensures orphaned records are cleaned up

### 3. Languages
- Unique combination of country_id and language_1
- Must reference valid country

### 4. Education & Professions
- Unique names enforced
- Valid category/industry values enforced
- Status management with check constraints

### 5. Corporate Users
- Unique email and username
- Password security features (reset tokens, lockout)
- Email verification system
- Role-based access control

## Data Flow Examples

### 1. Creating a Category Hierarchy
```
1. Create Profile Group → profile_groups
2. Create Main Category → main_categories (references profile_groups.id)
3. Create Level 1 → category_level1 (references main_categories.id)
4. Create Level 2 → category_level2 (references category_level1.id)
5. Create Level 3 → category_level3 (references category_level2.id)
```

### 2. Language Configuration
```
1. Country exists → countries (from base schema)
2. Add Language → languages (references countries.id)
```

### 3. User Management
```
1. Admin creates Corporate User → corporate_users
2. Corporate User can create other users → users table
3. All actions tracked via created_by fields
```

## Migration Strategy

### Phase 1: Core Tables
1. profile_groups
2. languages
3. education_levels
4. professions

### Phase 2: Category System
1. main_categories
2. category_level1
3. category_level2
4. category_level3

### Phase 3: Enhanced Features
1. corporate_users (enhanced)
2. Additional indexes
3. Triggers and functions

## Backup and Recovery

### Critical Tables (High Priority)
- users
- corporate_users
- profile_groups
- main_categories

### Reference Tables (Medium Priority)
- languages
- education_levels
- professions

### Hierarchy Tables (Low Priority - Can be rebuilt)
- category_level1
- category_level2
- category_level3
