import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Modal, Alert, Badge, Tab, Tabs, Accordion } from 'react-bootstrap';

const FooterManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [activeSection, setActiveSection] = useState('about');
    const [editingItem, setEditingItem] = useState(null);

    // Form data
    const [formData, setFormData] = useState({
        title: '',
        content: '',
        url: '',
        icon: '',
        order: 0,
        status: 'Active'
    });

    // Footer sections data
    const [footerData, setFooterData] = useState({
        about: {
            title: 'About Us',
            content: 'We are a leading company providing innovative solutions for businesses worldwide. Our commitment to excellence and customer satisfaction drives everything we do.',
            lastUpdated: '2024-01-20',
            status: 'Active'
        },
        awards: [
            { id: 1, title: 'Best Innovation Award 2023', content: 'Recognized for outstanding innovation in technology', order: 1, status: 'Active' },
            { id: 2, title: 'Customer Choice Award 2023', content: 'Voted by customers as the preferred service provider', order: 2, status: 'Active' },
            { id: 3, title: 'Excellence in Service 2022', content: 'Awarded for exceptional customer service quality', order: 3, status: 'Active' }
        ],
        newsroom: [
            { id: 1, title: 'Company Expansion Announcement', content: 'We are excited to announce our expansion into new markets', url: '/news/expansion', order: 1, status: 'Active' },
            { id: 2, title: 'New Product Launch', content: 'Introducing our latest innovative product line', url: '/news/product-launch', order: 2, status: 'Active' },
            { id: 3, title: 'Partnership Agreement', content: 'Strategic partnership with industry leaders', url: '/news/partnership', order: 3, status: 'Active' }
        ],
        events: [
            { id: 1, title: 'Annual Conference 2024', content: 'Join us for our biggest event of the year', url: '/events/conference', order: 1, status: 'Active' },
            { id: 2, title: 'Product Demo Webinar', content: 'Live demonstration of our latest features', url: '/events/webinar', order: 2, status: 'Active' },
            { id: 3, title: 'Industry Summit', content: 'Networking event for industry professionals', url: '/events/summit', order: 3, status: 'Active' }
        ],
        careers: [
            { id: 1, title: 'Software Developer', content: 'Join our development team', url: '/careers/developer', order: 1, status: 'Active' },
            { id: 2, title: 'Marketing Manager', content: 'Lead our marketing initiatives', url: '/careers/marketing', order: 2, status: 'Active' },
            { id: 3, title: 'Sales Representative', content: 'Expand our customer base', url: '/careers/sales', order: 3, status: 'Active' }
        ],
        clients: [
            { id: 1, title: 'Fortune 500 Companies', content: 'Trusted by leading corporations', icon: 'fas fa-building', order: 1, status: 'Active' },
            { id: 2, title: 'Small Businesses', content: 'Supporting growing enterprises', icon: 'fas fa-store', order: 2, status: 'Active' },
            { id: 3, title: 'Startups', content: 'Empowering innovative startups', icon: 'fas fa-rocket', order: 3, status: 'Active' }
        ],
        milestones: [
            { id: 1, title: '10+ Years Experience', content: 'Over a decade of industry expertise', icon: 'fas fa-calendar', order: 1, status: 'Active' },
            { id: 2, title: '1000+ Projects Completed', content: 'Successfully delivered projects worldwide', icon: 'fas fa-check-circle', order: 2, status: 'Active' },
            { id: 3, title: '50+ Countries Served', content: 'Global presence and reach', icon: 'fas fa-globe', order: 3, status: 'Active' }
        ],
        testimonials: [
            { id: 1, title: 'John Smith, CEO', content: 'Excellent service and outstanding results. Highly recommended!', order: 1, status: 'Active' },
            { id: 2, title: 'Sarah Johnson, Manager', content: 'Professional team with great attention to detail.', order: 2, status: 'Active' },
            { id: 3, title: 'Mike Wilson, Director', content: 'Exceeded our expectations in every aspect.', order: 3, status: 'Active' }
        ],
        gallery: [
            { id: 1, title: 'Office Tour', content: 'Take a virtual tour of our modern facilities', url: '/gallery/office', order: 1, status: 'Active' },
            { id: 2, title: 'Team Events', content: 'Photos from our team building activities', url: '/gallery/events', order: 2, status: 'Active' },
            { id: 3, title: 'Product Showcase', content: 'Visual presentation of our products', url: '/gallery/products', order: 3, status: 'Active' }
        ],
        contact: {
            address: '123 Business Street, City, State 12345',
            phone: '+****************',
            email: '<EMAIL>',
            hours: 'Monday - Friday: 9:00 AM - 6:00 PM',
            status: 'Active'
        },
        social: [
            { id: 1, platform: 'Facebook', url: 'https://facebook.com/company', icon: 'fab fa-facebook', order: 1, status: 'Active' },
            { id: 2, platform: 'Twitter', url: 'https://twitter.com/company', icon: 'fab fa-twitter', order: 2, status: 'Active' },
            { id: 3, platform: 'LinkedIn', url: 'https://linkedin.com/company/company', icon: 'fab fa-linkedin', order: 3, status: 'Active' },
            { id: 4, platform: 'Instagram', url: 'https://instagram.com/company', icon: 'fab fa-instagram', order: 4, status: 'Active' }
        ],
        legal: [
            { id: 1, title: 'Terms & Conditions', content: 'Our terms of service', url: '/legal/terms', order: 1, status: 'Active' },
            { id: 2, title: 'Privacy Policy', content: 'How we protect your privacy', url: '/legal/privacy', order: 2, status: 'Active' },
            { id: 3, title: 'Cookie Policy', content: 'Information about our cookie usage', url: '/legal/cookies', order: 3, status: 'Active' }
        ]
    });

    const footerSections = [
        { key: 'about', label: 'About Us', type: 'text' },
        { key: 'awards', label: 'Awards', type: 'list' },
        { key: 'newsroom', label: 'Newsroom', type: 'list' },
        { key: 'events', label: 'Events', type: 'list' },
        { key: 'careers', label: 'Careers', type: 'list' },
        { key: 'clients', label: 'Clients', type: 'list' },
        { key: 'milestones', label: 'Milestones', type: 'list' },
        { key: 'testimonials', label: 'Testimonials', type: 'list' },
        { key: 'gallery', label: 'Gallery', type: 'list' },
        { key: 'contact', label: 'Contact Us', type: 'contact' },
        { key: 'social', label: 'Social Media', type: 'social' },
        { key: 'legal', label: 'Legal', type: 'list' }
    ];

    useEffect(() => {
        fetchFooterData();
    }, []);

    const fetchFooterData = async () => {
        setLoading(true);
        try {
            // API call to fetch footer data
            // const response = await fetch('/api/footer-content');
            // const data = await response.json();
            // setFooterData(data);
        } catch (error) {
            console.error('Error fetching footer data:', error);
            setError('Failed to fetch footer data');
        } finally {
            setLoading(false);
        }
    };

    const handleCreate = (section) => {
        setEditingItem(null);
        setActiveSection(section);
        setFormData({
            title: '',
            content: '',
            url: '',
            icon: '',
            order: Array.isArray(footerData[section]) ? footerData[section].length + 1 : 1,
            status: 'Active'
        });
        setShowModal(true);
    };

    const handleEdit = (section, item) => {
        setEditingItem(item);
        setActiveSection(section);
        setFormData({
            title: item.title || '',
            content: item.content || '',
            url: item.url || '',
            icon: item.icon || '',
            order: item.order || 1,
            status: item.status || 'Active'
        });
        setShowModal(true);
    };

    const handleDelete = (section, itemId) => {
        if (window.confirm('Are you sure you want to delete this item?')) {
            if (Array.isArray(footerData[section])) {
                setFooterData({
                    ...footerData,
                    [section]: footerData[section].filter(item => item.id !== itemId)
                });
            }
            setSuccess('Item deleted successfully');
        }
    };

    const toggleStatus = (section, itemId) => {
        if (Array.isArray(footerData[section])) {
            setFooterData({
                ...footerData,
                [section]: footerData[section].map(item => 
                    item.id === itemId 
                        ? { ...item, status: item.status === 'Active' ? 'Inactive' : 'Active' }
                        : item
                )
            });
        }
        setSuccess('Status updated successfully');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            if (Array.isArray(footerData[activeSection])) {
                const newItem = {
                    id: editingItem ? editingItem.id : Date.now(),
                    title: formData.title,
                    content: formData.content,
                    url: formData.url,
                    icon: formData.icon,
                    order: formData.order,
                    status: formData.status
                };

                if (editingItem) {
                    setFooterData({
                        ...footerData,
                        [activeSection]: footerData[activeSection].map(item => 
                            item.id === editingItem.id ? newItem : item
                        )
                    });
                    setSuccess('Item updated successfully');
                } else {
                    setFooterData({
                        ...footerData,
                        [activeSection]: [...footerData[activeSection], newItem]
                    });
                    setSuccess('Item created successfully');
                }
            }

            setShowModal(false);
            setFormData({
                title: '',
                content: '',
                url: '',
                icon: '',
                order: 0,
                status: 'Active'
            });
        } catch (error) {
            setError('Failed to save item');
        } finally {
            setLoading(false);
        }
    };

    const updateAboutUs = (content) => {
        setFooterData({
            ...footerData,
            about: {
                ...footerData.about,
                content: content,
                lastUpdated: new Date().toISOString().split('T')[0]
            }
        });
        setSuccess('About Us section updated successfully');
    };

    const updateContactInfo = (field, value) => {
        setFooterData({
            ...footerData,
            contact: {
                ...footerData.contact,
                [field]: value
            }
        });
        setSuccess('Contact information updated successfully');
    };

    const renderSectionContent = (section) => {
        const sectionData = footerData[section.key];
        
        if (section.type === 'text') {
            return (
                <Card className="mb-3">
                    <Card.Header>
                        <div className="d-flex justify-content-between align-items-center">
                            <h6 className="mb-0">{section.label}</h6>
                            <Badge bg="success">Active</Badge>
                        </div>
                    </Card.Header>
                    <Card.Body>
                        <Form.Group>
                            <Form.Label>Content</Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={4}
                                value={sectionData.content}
                                onChange={(e) => updateAboutUs(e.target.value)}
                            />
                        </Form.Group>
                        <small className="text-muted">Last updated: {sectionData.lastUpdated}</small>
                    </Card.Body>
                </Card>
            );
        }

        if (section.type === 'contact') {
            return (
                <Card className="mb-3">
                    <Card.Header>
                        <div className="d-flex justify-content-between align-items-center">
                            <h6 className="mb-0">{section.label}</h6>
                            <Badge bg="success">Active</Badge>
                        </div>
                    </Card.Header>
                    <Card.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Address</Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={sectionData.address}
                                        onChange={(e) => updateContactInfo('address', e.target.value)}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Phone</Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={sectionData.phone}
                                        onChange={(e) => updateContactInfo('phone', e.target.value)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Email</Form.Label>
                                    <Form.Control
                                        type="email"
                                        value={sectionData.email}
                                        onChange={(e) => updateContactInfo('email', e.target.value)}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Business Hours</Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={sectionData.hours}
                                        onChange={(e) => updateContactInfo('hours', e.target.value)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>
            );
        }

        if (section.type === 'list' || section.type === 'social') {
            return (
                <Card className="mb-3">
                    <Card.Header>
                        <div className="d-flex justify-content-between align-items-center">
                            <h6 className="mb-0">{section.label}</h6>
                            <Button 
                                variant="primary" 
                                size="sm"
                                onClick={() => handleCreate(section.key)}
                            >
                                <i className="fas fa-plus me-1"></i>Add Item
                            </Button>
                        </div>
                    </Card.Header>
                    <Card.Body>
                        {Array.isArray(sectionData) && sectionData.length === 0 ? (
                            <div className="text-center py-3">
                                <i className="fas fa-inbox fa-2x text-muted mb-2"></i>
                                <p className="text-muted">No items added yet</p>
                                <Button 
                                    variant="primary" 
                                    size="sm"
                                    onClick={() => handleCreate(section.key)}
                                >
                                    Add First Item
                                </Button>
                            </div>
                        ) : (
                            <div className="list-items">
                                {Array.isArray(sectionData) && sectionData.map(item => (
                                    <div key={item.id} className="list-item border-bottom py-2">
                                        <div className="d-flex justify-content-between align-items-start">
                                            <div className="flex-grow-1">
                                                <div className="d-flex align-items-center mb-1">
                                                    {item.icon && <i className={`${item.icon} me-2`}></i>}
                                                    <strong>{item.title || item.platform}</strong>
                                                    <Badge 
                                                        bg={item.status === 'Active' ? 'success' : 'secondary'}
                                                        className="ms-2"
                                                        style={{ cursor: 'pointer' }}
                                                        onClick={() => toggleStatus(section.key, item.id)}
                                                    >
                                                        {item.status}
                                                    </Badge>
                                                </div>
                                                <p className="mb-1 text-muted small">{item.content}</p>
                                                {item.url && (
                                                    <small className="text-primary">{item.url}</small>
                                                )}
                                            </div>
                                            <div className="ms-2">
                                                <Button
                                                    variant="outline-primary"
                                                    size="sm"
                                                    className="me-1"
                                                    onClick={() => handleEdit(section.key, item)}
                                                >
                                                    <i className="fas fa-edit"></i>
                                                </Button>
                                                <Button
                                                    variant="outline-danger"
                                                    size="sm"
                                                    onClick={() => handleDelete(section.key, item.id)}
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </Card.Body>
                </Card>
            );
        }

        return null;
    };

    return (
        <Container fluid className="footer-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Footer Content Management</h4>
                <Button variant="outline-info">
                    <i className="fas fa-eye me-1"></i>Preview Footer
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{footerSections.length}</h3>
                            <p className="mb-0">Footer Sections</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {footerData.social?.filter(item => item.status === 'Active').length || 0}
                            </h3>
                            <p className="mb-0">Social Links</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {footerData.legal?.filter(item => item.status === 'Active').length || 0}
                            </h3>
                            <p className="mb-0">Legal Pages</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                {footerData.careers?.filter(item => item.status === 'Active').length || 0}
                            </h3>
                            <p className="mb-0">Career Openings</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Footer Sections */}
            <Accordion defaultActiveKey="0">
                {footerSections.map((section, index) => (
                    <Accordion.Item eventKey={index.toString()} key={section.key}>
                        <Accordion.Header>
                            <div className="d-flex align-items-center">
                                <i className="fas fa-cog me-2"></i>
                                <strong>{section.label}</strong>
                                <Badge bg="secondary" className="ms-2">{section.type}</Badge>
                            </div>
                        </Accordion.Header>
                        <Accordion.Body>
                            {renderSectionContent(section)}
                        </Accordion.Body>
                    </Accordion.Item>
                ))}
            </Accordion>

            {/* Add/Edit Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-edit me-2"></i>
                        {editingItem ? 'Edit' : 'Add'} {footerSections.find(s => s.key === activeSection)?.label} Item
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={8}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Title *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter title"
                                        value={formData.title}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            title: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Order</Form.Label>
                                    <Form.Control
                                        type="number"
                                        min="1"
                                        value={formData.order}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            order: parseInt(e.target.value)
                                        })}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Content</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={3}
                                        placeholder="Enter content"
                                        value={formData.content}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            content: e.target.value
                                        })}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>URL</Form.Label>
                                    <Form.Control
                                        type="url"
                                        placeholder="https://example.com"
                                        value={formData.url}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            url: e.target.value
                                        })}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Icon Class</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="fas fa-icon"
                                        value={formData.icon}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            icon: e.target.value
                                        })}
                                    />
                                    <Form.Text className="text-muted">
                                        FontAwesome icon class (optional)
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Status</Form.Label>
                                    <Form.Select
                                        value={formData.status}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            status: e.target.value
                                        })}
                                    >
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <i className={`fas ${editingItem ? 'fa-save' : 'fa-plus'} me-2`}></i>
                                    {editingItem ? 'Update' : 'Create'} Item
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default FooterManagement;
