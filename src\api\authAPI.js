// Authentication API Service
// Handles all authentication-related API calls

class AuthAPI {
  constructor() {
    // Configuration for API base URL
    this.baseURL = this.getApiBaseUrl();
    this.endpoints = {
      // Customer login
      customerLogin: '/auth/login',
      // Role-based logins
      adminLogin: '/roles/admin/login',
      corporateLogin: '/roles/corporate/login',
      headOfficeLogin: '/roles/headoffice/login',
      regionalLogin: '/roles/regional/login',
      branchLogin: '/roles/branch/login',
      // Other endpoints
      logout: '/auth/logout',
      validate: '/auth/validate',
      refresh: '/auth/refresh',
      changePassword: '/auth/change-password',
      resetPassword: '/auth/reset-password'
    };

    // Role hierarchy for access control
    this.roleHierarchy = {
      'admin': 8,
      'app_admin': 7,
      'corporate': 6,
      'headoffice': 5,
      'regional': 4,
      'branch': 3,
      'partner': 2,
      'customer': 1
    };
  }

  // Get API base URL from various sources
  getApiBaseUrl() {
    // Check for environment variables in different contexts
    if (typeof window !== 'undefined') {
      // Browser environment - check for runtime config
      if (window.env?.REACT_APP_API_URL) {
        return window.env.REACT_APP_API_URL;
      }
      if (window.ENV?.API_URL) {
        return window.ENV.API_URL;
      }
    }

    // Fallback to default
    return 'http://localhost:3001/api';
  }

  // Get authorization headers
  getAuthHeaders(token = null) {
    const headers = {
      'Content-Type': 'application/json',
    };

    const authToken = token || localStorage.getItem('authToken');
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    return headers;
  }

  // Handle API response
  async handleResponse(response) {
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }
    
    return data;
  }

  // Customer login
  async login(username, password) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.customerLogin}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          username,
          password
        })
      });

      const result = await this.handleResponse(response);

      if (result.success) {
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('currentUser', JSON.stringify(result.user));
        return result;
      }

      return result;

    } catch (error) {
      console.error('Customer login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  // Role-based login
  async roleLogin(username, password, role) {
    try {
      let endpoint;
      switch (role) {
        case 'admin':
          endpoint = this.endpoints.adminLogin;
          break;
        case 'corporate':
          endpoint = this.endpoints.corporateLogin;
          break;
        case 'headoffice':
          endpoint = this.endpoints.headOfficeLogin;
          break;
        case 'regional':
          endpoint = this.endpoints.regionalLogin;
          break;
        case 'branch':
          endpoint = this.endpoints.branchLogin;
          break;
        default:
          throw new Error('Invalid role specified');
      }

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          username,
          password
        })
      });

      const result = await this.handleResponse(response);

      if (result.success) {
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('currentUser', JSON.stringify(result.user));
        return result;
      }

      return result;

    } catch (error) {
      console.error(`${role} login error:`, error);
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }



  // Validate user role and permissions
  hasPermission(userRole, requiredRole) {
    const userLevel = this.roleHierarchy[userRole] || 0;
    const requiredLevel = this.roleHierarchy[requiredRole] || 0;
    return userLevel >= requiredLevel;
  }

  // Get user role level
  getRoleLevel(role) {
    return this.roleHierarchy[role] || 0;
  }

  // Check if user can access specific features based on role
  canAccessFeature(userRole, feature) {
    const permissions = {
      'user_management': ['admin', 'app_admin', 'corporate'],
      'advertisement_management': ['admin', 'app_admin', 'corporate', 'headoffice', 'regional', 'branch', 'partner'],
      'location_management': ['admin', 'app_admin'],
      'group_management': ['admin', 'app_admin'],
      'role_management': ['admin', 'app_admin'],
      'reports': ['admin', 'app_admin', 'corporate', 'headoffice', 'regional', 'branch']
    };

    return permissions[feature]?.includes(userRole) || false;
  }

  // Logout user
  async logout() {
    try {
      const token = localStorage.getItem('authToken');

      if (token) {
        await fetch(`${this.baseURL}${this.endpoints.logout}`, {
          method: 'POST',
          headers: this.getAuthHeaders()
        });
      }
      
      // Clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('currentUser');
      
      return { success: true };
      
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local storage even if API call fails
      localStorage.removeItem('authToken');
      localStorage.removeItem('currentUser');
      return { success: true };
    }
  }

  // Validate current session
  async validateSession(token = null) {
    try {
      const authToken = token || localStorage.getItem('authToken');

      if (!authToken) {
        return { valid: false, error: 'No token found' };
      }

      const response = await fetch(`${this.baseURL}${this.endpoints.validate}`, {
        method: 'POST',
        headers: this.getAuthHeaders(authToken),
        body: JSON.stringify({ token: authToken })
      });

      return await this.handleResponse(response);
      
    } catch (error) {
      console.error('Session validation error:', error);
      return { valid: false, error: error.message };
    }
  }

  // Get current user from localStorage
  getCurrentUser() {
    try {
      const userStr = localStorage.getItem('currentUser');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = localStorage.getItem('authToken');
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  // Get user role
  getUserRole() {
    const user = this.getCurrentUser();
    return user ? user.role : null;
  }

  // Check if user has specific role
  hasRole(role) {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    const userRole = this.getUserRole();
    return roles.includes(userRole);
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.changePassword}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      });

      return await this.handleResponse(response);
      
    } catch (error) {
      console.error('Change password error:', error);
      return {
        success: false,
        error: error.message || 'Failed to change password'
      };
    }
  }

  // Reset password (admin function)
  async resetPassword(userId, newPassword) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.resetPassword}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          userId,
          newPassword
        })
      });

      return await this.handleResponse(response);
      
    } catch (error) {
      console.error('Reset password error:', error);
      return {
        success: false,
        error: error.message || 'Failed to reset password'
      };
    }
  }

  // Refresh token
  async refreshToken() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.refresh}`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      const result = await this.handleResponse(response);
      
      if (result.success && result.token) {
        localStorage.setItem('authToken', result.token);
      }
      
      return result;
      
    } catch (error) {
      console.error('Token refresh error:', error);
      return {
        success: false,
        error: error.message || 'Failed to refresh token'
      };
    }
  }

  // Get user permissions
  async getUserPermissions() {
    try {
      const user = this.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // For development, return mock permissions based on role
      if (process.env.NODE_ENV === 'development') {
        const rolePermissions = {
          'ADMIN': ['*'], // All permissions
          'CORPORATE': ['user.create', 'user.read', 'user.update', 'user.delete', 'organization.manage', 'head_office.manage', 'report.generate', 'report.view', 'data.export'],
          'HEAD_OFFICE': ['user.create', 'user.read', 'user.update', 'regional.manage', 'branch.manage', 'report.generate', 'report.view', 'data.export'],
          'REGIONAL': ['user.create', 'user.read', 'user.update', 'branch.manage', 'transaction.view', 'transaction.approve', 'report.generate', 'report.view'],
          'BRANCH': ['user.read', 'transaction.process', 'transaction.view', 'report.view']
        };

        return {
          success: true,
          permissions: rolePermissions[user.role] || []
        };
      }

      const response = await fetch(`${this.baseURL}/auth/permissions`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      return await this.handleResponse(response);
      
    } catch (error) {
      console.error('Get permissions error:', error);
      return {
        success: false,
        error: error.message || 'Failed to get permissions'
      };
    }
  }

  // Check if user has specific permission
  async hasPermission(permission) {
    const result = await this.getUserPermissions();
    if (!result.success) return false;
    
    const permissions = result.permissions;
    return permissions.includes('*') || permissions.includes(permission);
  }
}

export default new AuthAPI();
