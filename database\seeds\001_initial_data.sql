-- Seed Data: Initial system setup
-- Description: Creates initial organizations, offices, and users for testing
-- Created: 2024-01-18

-- Insert sample organization
INSERT INTO my_organizations (organization_name, organization_code, address, city, state, country, postal_code, phone, email, website) VALUES
('MyGroup Corporation', 'MYGROUP', '123 Corporate Blvd', 'New York', 'NY', 'USA', '10001', '******-0100', '<EMAIL>', 'www.mygroup.com');

-- Insert sample head office
INSERT INTO my_head_offices (organization_id, office_name, office_code, address, city, state, country, postal_code, phone, email) VALUES
(1, 'MyGroup Head Office', 'HO001', '456 Head Office Ave', 'New York', 'NY', 'USA', '10002', '******-0200', '<EMAIL>');

-- Insert sample regional offices
INSERT INTO my_regional_offices (head_office_id, region_name, region_code, address, city, state, country, postal_code, phone, email) VALUES
(1, 'North Region', 'REG001', '789 North Regional St', 'Boston', 'MA', 'USA', '02101', '******-0301', '<EMAIL>'),
(1, 'South Region', 'REG002', '321 South Regional Ave', 'Atlanta', 'GA', 'USA', '30301', '******-0302', '<EMAIL>'),
(1, 'West Region', 'REG003', '654 West Regional Blvd', 'Los Angeles', 'CA', 'USA', '90001', '******-0303', '<EMAIL>');

-- Insert sample branch offices
INSERT INTO my_branch_offices (regional_office_id, branch_name, branch_code, address, city, state, country, postal_code, phone, email, services_offered) VALUES
(1, 'Downtown Boston Branch', 'BR001', '111 Downtown St', 'Boston', 'MA', 'USA', '02102', '******-0401', '<EMAIL>', '["Banking", "Loans", "Insurance"]'),
(1, 'Cambridge Branch', 'BR002', '222 Cambridge Ave', 'Cambridge', 'MA', 'USA', '02139', '******-0402', '<EMAIL>', '["Banking", "Investments"]'),
(2, 'Atlanta Central Branch', 'BR003', '333 Central Plaza', 'Atlanta', 'GA', 'USA', '30302', '******-0403', '<EMAIL>', '["Banking", "Loans", "Credit Cards"]'),
(2, 'Buckhead Branch', 'BR004', '444 Buckhead Rd', 'Atlanta', 'GA', 'USA', '30305', '******-0404', '<EMAIL>', '["Banking", "Mortgages"]'),
(3, 'Hollywood Branch', 'BR005', '555 Hollywood Blvd', 'Los Angeles', 'CA', 'USA', '90028', '******-0405', '<EMAIL>', '["Banking", "Loans", "Insurance", "Investments"]');

-- Insert sample users (passwords are hashed version of 'password123')
-- Note: In production, use proper password hashing like bcrypt
INSERT INTO my_users (username, email, password_hash, first_name, last_name, mobile_number, role_id, organization_id, head_office_id, regional_office_id, branch_office_id, is_active, email_verified) VALUES
-- Super Admin
('admin', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'System', 'Administrator', '******-1001', 1, NULL, NULL, NULL, NULL, TRUE, TRUE),

-- Corporate Manager
('corporate.manager', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'John', 'Corporate', '******-1002', 2, 1, NULL, NULL, NULL, TRUE, TRUE),

-- Head Office Manager
('headoffice.manager', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Sarah', 'HeadOffice', '******-1003', 3, 1, 1, NULL, NULL, TRUE, TRUE),

-- Regional Managers
('north.regional', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Michael', 'North', '******-1004', 4, 1, 1, 1, NULL, TRUE, TRUE),
('south.regional', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Emily', 'South', '******-1005', 4, 1, 1, 2, NULL, TRUE, TRUE),
('west.regional', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'David', 'West', '******-1006', 4, 1, 1, 3, NULL, TRUE, TRUE),

-- Branch Managers
('downtown.boston', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Alice', 'Boston', '******-1007', 5, 1, 1, 1, 1, TRUE, TRUE),
('cambridge.branch', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Bob', 'Cambridge', '******-1008', 5, 1, 1, 1, 2, TRUE, TRUE),
('atlanta.central', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Carol', 'Atlanta', '******-1009', 5, 1, 1, 2, 3, TRUE, TRUE),
('buckhead.branch', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Daniel', 'Buckhead', '******-1010', 5, 1, 1, 2, 4, TRUE, TRUE),
('hollywood.branch', '<EMAIL>', '$2b$10$rQZ8kJQXQXQXQXQXQXQXQeJ8kJQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'Eva', 'Hollywood', '******-1011', 5, 1, 1, 3, 5, TRUE, TRUE);

-- Log initial data creation
INSERT INTO my_audit_logs (user_id, action, table_name, new_values, ip_address) VALUES
(NULL, 'SEED_DATA_CREATED', 'multiple', '{"message": "Initial seed data created", "timestamp": "2024-01-18"}', '127.0.0.1');
