import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, InputGroup, Row, Col } from 'react-bootstrap';
import { 
  <PERSON>a<PERSON>ye, 
  FaEyeSlash, 
  FaEnvelope, 
  FaLock, 
  FaUser, 
  FaPhone,
  FaCalendarAlt
} from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import './Register.css';

const Register = ({ onSwitchToLogin, onClose }) => {
  const [formData, setFormData] = useState({
    // User basic info
    user_name: '',
    email: '',
    password: '',
    confirmPassword: '',
    mobile_number: '',
    
    // User details
    first_name: '',
    last_name: '',
    middle_name: '',
    display_name: '',
    gender: '',
    date_of_birth: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [validated, setValidated] = useState(false);
  
  const { register } = useAuth();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Auto-generate display name
    if (name === 'first_name' || name === 'last_name') {
      const firstName = name === 'first_name' ? value : formData.first_name;
      const lastName = name === 'last_name' ? value : formData.last_name;
      setFormData(prev => ({
        ...prev,
        display_name: `${firstName} ${lastName}`.trim()
      }));
    }
    
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const validateForm = () => {
    const { 
      user_name, 
      email, 
      password, 
      confirmPassword, 
      mobile_number,
      first_name,
      last_name
    } = formData;
    
    if (!user_name || !email || !password || !confirmPassword || !mobile_number || !first_name || !last_name) {
      setError('Please fill in all required fields');
      return false;
    }
    
    if (!email.includes('@')) {
      setError('Please enter a valid email address');
      return false;
    }
    
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    
    if (mobile_number.length < 10) {
      setError('Please enter a valid mobile number');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    setValidated(true);
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      const registrationData = {
        ...formData,
        display_name: formData.display_name || `${formData.first_name} ${formData.last_name}`
      };
      
      await register(registrationData);
      onClose && onClose();
    } catch (err) {
      setError(err.message || 'Failed to register. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="register-form">
      <div className="text-center mb-4">
        <h5 className="mb-2">Create Your Account</h5>
        <p className="text-muted small">Join us today and get started</p>
      </div>
      
      {error && (
        <Alert variant="danger" className="py-2">
          <small>{error}</small>
        </Alert>
      )}
      
      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Row>
          <Col xs={12} sm={6}>
            <Form.Group className="mb-3">
              <Form.Label className="small fw-semibold">First Name *</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaUser className="text-muted" />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  placeholder="First name"
                  required
                />
              </InputGroup>
            </Form.Group>
          </Col>
          
          <Col xs={12} sm={6}>
            <Form.Group className="mb-3">
              <Form.Label className="small fw-semibold">Last Name *</Form.Label>
              <Form.Control
                type="text"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                placeholder="Last name"
                required
              />
            </Form.Group>
          </Col>
        </Row>

        <Form.Group className="mb-3">
          <Form.Label className="small fw-semibold">Username *</Form.Label>
          <Form.Control
            type="text"
            name="user_name"
            value={formData.user_name}
            onChange={handleChange}
            placeholder="Choose a username"
            required
          />
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label className="small fw-semibold">Email Address *</Form.Label>
          <InputGroup>
            <InputGroup.Text>
              <FaEnvelope className="text-muted" />
            </InputGroup.Text>
            <Form.Control
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              required
            />
          </InputGroup>
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label className="small fw-semibold">Mobile Number *</Form.Label>
          <InputGroup>
            <InputGroup.Text>
              <FaPhone className="text-muted" />
            </InputGroup.Text>
            <Form.Control
              type="tel"
              name="mobile_number"
              value={formData.mobile_number}
              onChange={handleChange}
              placeholder="Enter mobile number"
              required
            />
          </InputGroup>
        </Form.Group>

        <Row>
          <Col xs={12} sm={6}>
            <Form.Group className="mb-3">
              <Form.Label className="small fw-semibold">Password *</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaLock className="text-muted" />
                </InputGroup.Text>
                <Form.Control
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Create password"
                  required
                />
                <Button
                  variant="outline-secondary"
                  onClick={() => setShowPassword(!showPassword)}
                  className="password-toggle"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </Button>
              </InputGroup>
            </Form.Group>
          </Col>
          
          <Col xs={12} sm={6}>
            <Form.Group className="mb-3">
              <Form.Label className="small fw-semibold">Confirm Password *</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaLock className="text-muted" />
                </InputGroup.Text>
                <Form.Control
                  type={showConfirmPassword ? 'text' : 'password'}
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder="Confirm password"
                  required
                />
                <Button
                  variant="outline-secondary"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="password-toggle"
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </Button>
              </InputGroup>
            </Form.Group>
          </Col>
        </Row>

        <Row>
          <Col xs={12} sm={6}>
            <Form.Group className="mb-3">
              <Form.Label className="small fw-semibold">Gender</Form.Label>
              <Form.Select
                name="gender"
                value={formData.gender}
                onChange={handleChange}
              >
                <option value="">Select gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </Form.Select>
            </Form.Group>
          </Col>
          
          <Col xs={12} sm={6}>
            <Form.Group className="mb-3">
              <Form.Label className="small fw-semibold">Date of Birth</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaCalendarAlt className="text-muted" />
                </InputGroup.Text>
                <Form.Control
                  type="date"
                  name="date_of_birth"
                  value={formData.date_of_birth}
                  onChange={handleChange}
                />
              </InputGroup>
            </Form.Group>
          </Col>
        </Row>

        <Form.Group className="mb-3">
          <Form.Check 
            type="checkbox" 
            id="terms-agreement"
            label={
              <small>
                I agree to the{' '}
                <Button variant="link" className="p-0 small text-decoration-none">
                  Terms of Service
                </Button>
                {' '}and{' '}
                <Button variant="link" className="p-0 small text-decoration-none">
                  Privacy Policy
                </Button>
              </small>
            }
            required
          />
        </Form.Group>

        <Button 
          variant="primary" 
          type="submit" 
          className="w-100 mb-3 py-2"
          disabled={loading}
        >
          {loading ? (
            <>
              <Spinner animation="border" size="sm" className="me-2" />
              Creating account...
            </>
          ) : (
            'Create Account'
          )}
        </Button>
      </Form>

      <div className="text-center">
        <p className="mb-0 small text-muted">
          Already have an account?{' '}
          <Button 
            variant="link" 
            className="p-0 small text-decoration-none fw-semibold"
            onClick={onSwitchToLogin}
          >
            Sign in here
          </Button>
        </p>
      </div>
    </div>
  );
};

export default Register;
