/* LoginModal.css */
.login-modal .modal-dialog {
  max-width: 500px;
}

.login-modal .modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.login-modal .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  padding: 20px 30px;
}

.login-modal .modal-title {
  font-weight: 600;
  font-size: 1.3rem;
}

.login-modal .btn-close {
  filter: invert(1);
}

.login-modal .modal-body {
  padding: 30px;
}

.custom-tabs .nav-link {
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  margin: 0 5px;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.custom-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.custom-tabs .nav-link:hover {
  color: #667eea;
  border: none;
}

/* Dark theme */
[data-theme="dark"] .login-modal .modal-content {
  background: #2c2c2c;
  color: white;
}

[data-theme="dark"] .login-modal .modal-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .custom-tabs .nav-link {
  color: #ccc;
}

[data-theme="dark"] .custom-tabs .nav-link.active {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
}

/* Responsive */
@media (max-width: 576px) {
  .login-modal .modal-dialog {
    margin: 10px;
    max-width: calc(100% - 20px);
  }
  
  .login-modal .modal-header {
    padding: 15px 20px;
  }
  
  .login-modal .modal-title {
    font-size: 1.1rem;
  }
  
  .login-modal .modal-body {
    padding: 20px;
  }
  
  .custom-tabs .nav-link {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
}
