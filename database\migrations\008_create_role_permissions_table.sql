-- Migration: Create my_role_permissions table
-- Description: Maps roles to permissions for role-based access control
-- Created: 2024-01-18

CREATE TABLE my_role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES my_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES my_permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role_id, permission_id)
);

-- Assign permissions to roles
-- Admin (Super Admin) - All permissions
INSERT INTO my_role_permissions (role_id, permission_id)
SELECT 1, id FROM my_permissions WHERE is_active = TRUE;

-- Corporate Manager - Organization and user management
INSERT INTO my_role_permissions (role_id, permission_id)
SELECT 2, id FROM my_permissions 
WHERE permission_code IN (
    'user.create', 'user.read', 'user.update', 'user.delete',
    'organization.manage', 'head_office.manage',
    'report.generate', 'report.view', 'data.export'
);

-- Head Office Manager - Regional and branch management
INSERT INTO my_role_permissions (role_id, permission_id)
SELECT 3, id FROM my_permissions 
WHERE permission_code IN (
    'user.create', 'user.read', 'user.update',
    'regional.manage', 'branch.manage',
    'report.generate', 'report.view', 'data.export'
);

-- Regional Manager - Branch management and transactions
INSERT INTO my_role_permissions (role_id, permission_id)
SELECT 4, id FROM my_permissions 
WHERE permission_code IN (
    'user.create', 'user.read', 'user.update',
    'branch.manage',
    'transaction.view', 'transaction.approve',
    'report.generate', 'report.view'
);

-- Branch Manager - Local operations
INSERT INTO my_role_permissions (role_id, permission_id)
SELECT 5, id FROM my_permissions 
WHERE permission_code IN (
    'user.read',
    'transaction.process', 'transaction.view',
    'report.view'
);

-- Create indexes
CREATE INDEX idx_role_permissions_role ON my_role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission ON my_role_permissions(permission_id);
