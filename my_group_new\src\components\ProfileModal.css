/* ProfileModal.css */
.profile-modal .modal-dialog {
  max-width: 600px;
}

.profile-modal .modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.profile-modal .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  padding: 20px 30px;
}

.profile-modal .modal-title {
  font-weight: 600;
  font-size: 1.3rem;
}

.profile-modal .btn-close {
  filter: invert(1);
}

.profile-modal .modal-body {
  padding: 30px;
  max-height: 70vh;
  overflow-y: auto;
}

.custom-tabs .nav-link {
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  margin: 0 5px;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.custom-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.custom-tabs .nav-link:hover {
  color: #667eea;
  border: none;
}

/* Profile View */
.profile-view .profile-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.profile-avatar {
  position: relative;
  display: inline-block;
}

.avatar-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #667eea;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.profile-field {
  margin-bottom: 15px;
}

.profile-field label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
  display: block;
}

.profile-field p {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.profile-actions {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

/* Settings */
.settings-content .setting-item {
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.settings-content .setting-item:last-child {
  border-bottom: none;
}

.settings-content h6 {
  margin: 0;
  font-weight: 600;
}

.settings-content .text-muted {
  margin: 0;
}

/* Form styles */
.profile-modal .form-label {
  color: #333;
  font-weight: 500;
  margin-bottom: 5px;
}

.profile-modal .form-control,
.profile-modal .form-select {
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 10px 12px;
}

.profile-modal .form-control:focus,
.profile-modal .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.profile-modal .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  font-weight: 500;
  padding: 10px 20px;
}

.profile-modal .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.profile-modal .btn-outline-secondary {
  border-radius: 25px;
  padding: 10px 20px;
}

.profile-modal .btn-outline-danger {
  border-radius: 25px;
  padding: 8px 16px;
}

/* Dark theme */
[data-theme="dark"] .profile-modal .modal-content {
  background: #2c2c2c;
  color: white;
}

[data-theme="dark"] .profile-modal .modal-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .custom-tabs .nav-link {
  color: #ccc;
}

[data-theme="dark"] .custom-tabs .nav-link.active {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
}

[data-theme="dark"] .profile-view .card {
  background: #3c3c3c;
  color: white;
}

[data-theme="dark"] .profile-field label {
  color: #ccc;
}

[data-theme="dark"] .profile-field p {
  color: white;
}

[data-theme="dark"] .profile-view .profile-header {
  border-bottom-color: #555;
}

[data-theme="dark"] .profile-actions {
  border-top-color: #555;
}

[data-theme="dark"] .settings-content .card {
  background: #3c3c3c;
  color: white;
}

[data-theme="dark"] .settings-content .setting-item {
  border-bottom-color: #555;
}

[data-theme="dark"] .profile-modal .form-label {
  color: #ccc;
}

[data-theme="dark"] .profile-modal .form-control,
[data-theme="dark"] .profile-modal .form-select {
  background: #3c3c3c;
  color: white;
  border-color: #555;
}

[data-theme="dark"] .profile-modal .form-control:focus,
[data-theme="dark"] .profile-modal .form-select:focus {
  background: #3c3c3c;
  color: white;
  border-color: #667eea;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-modal .modal-dialog {
    margin: 10px;
    max-width: calc(100% - 20px);
  }
  
  .profile-modal .modal-header {
    padding: 15px 20px;
  }
  
  .profile-modal .modal-title {
    font-size: 1.1rem;
  }
  
  .profile-modal .modal-body {
    padding: 20px;
    max-height: 80vh;
  }
  
  .custom-tabs .nav-link {
    padding: 8px 12px;
    font-size: 0.9rem;
    margin: 0 2px;
  }
  
  .avatar-img,
  .avatar-placeholder {
    width: 60px;
    height: 60px;
  }
  
  .profile-field {
    margin-bottom: 10px;
  }
  
  .profile-actions .btn {
    font-size: 0.9rem;
    padding: 8px 16px;
  }
}
