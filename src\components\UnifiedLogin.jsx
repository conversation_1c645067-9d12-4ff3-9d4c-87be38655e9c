import React, { useState } from 'react';
import { Container, <PERSON>, Col, <PERSON>, <PERSON>, <PERSON><PERSON>, Al<PERSON>, Spin<PERSON>, Tab, Tabs } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const UnifiedLogin = () => {
    const { login } = useAuth();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [activeTab, setActiveTab] = useState('admin');
    const [formData, setFormData] = useState({
        username: '',
        password: ''
    });

    // Role configurations for different login types
    const roleConfigs = {
        admin: {
            title: 'Super Admin Login',
            subtitle: 'System Administration Portal',
            icon: 'fas fa-user-shield',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            redirectPath: '/super-admin',
            expectedRole: 'ADMIN',
            description: 'Complete system control and management'
        },
        corporate: {
            title: 'Corporate Login',
            subtitle: 'Corporate Management Portal',
            icon: 'fas fa-building',
            gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            redirectPath: '/corporate',
            expectedRole: 'CORPORATE',
            description: 'Organization and head office management'
        },
        headoffice: {
            title: 'Head Office Login',
            subtitle: 'Head Office Management Portal',
            icon: 'fas fa-landmark',
            gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            redirectPath: '/head-office',
            expectedRole: 'HEAD_OFFICE',
            description: 'Regional and branch office oversight'
        },
        regional: {
            title: 'Regional Login',
            subtitle: 'Regional Management Portal',
            icon: 'fas fa-map-marker-alt',
            gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            redirectPath: '/regional',
            expectedRole: 'REGIONAL',
            description: 'Branch management and operations'
        },
        branch: {
            title: 'Branch Login',
            subtitle: 'Branch Management Portal',
            icon: 'fas fa-store',
            gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            redirectPath: '/branch',
            expectedRole: 'BRANCH',
            description: 'Local branch operations and services'
        }
    };

    const currentConfig = roleConfigs[activeTab];

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            const result = await login(formData.username, formData.password);
            console.log(result);
            
            if (result.success) {
                const userRole = result.user.role;
                
                // Role-based redirection
                switch (userRole) {
                    case 'ADMIN':
                        navigate('/super-admin');
                        break;
                    case 'CORPORATE':
                        navigate('/corporate');
                        break;
                    case 'HEAD_OFFICE':
                        navigate('/head-office');
                        break;
                    case 'REGIONAL':
                        navigate('/regional');
                        break;
                    case 'BRANCH':
                        navigate('/branch');
                        break;
                    default:
                        navigate('/');
                }
            } else {
                setError(result.error || 'Invalid username or password');
            }
        } catch (error) {
            setError('Login failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Quick fill demo credentials
    const fillDemoCredentials = (role) => {
        const demoCredentials = {
            admin: { username: 'admin', password: 'admin123' },
            corporate: { username: 'corporate.manager', password: 'password123' },
            headoffice: { username: 'headoffice.manager', password: 'password123' },
            regional: { username: 'north.regional', password: 'password123' },
            branch: { username: 'downtown.boston', password: 'password123' }
        };

        const credentials = demoCredentials[role];
        if (credentials) {
            setFormData(credentials);
        }
    };

    return (
        <div 
            className="unified-login-page" 
            style={{ 
                minHeight: '100vh', 
                background: currentConfig.gradient,
                display: 'flex',
                alignItems: 'center',
                transition: 'background 0.5s ease'
            }}
        >
            <Container>
                <Row className="justify-content-center">
                    <Col md={8} lg={6}>
                        <Card className="shadow-lg border-0" style={{ borderRadius: '20px', overflow: 'hidden' }}>
                            {/* Header */}
                            <div 
                                className="text-center text-white p-4"
                                style={{ background: currentConfig.gradient }}
                            >
                                <i className={`${currentConfig.icon} fa-3x mb-3`}></i>
                                <h2 className="mb-1">{currentConfig.title}</h2>
                                <p className="mb-0 opacity-75">{currentConfig.subtitle}</p>
                            </div>

                            <Card.Body className="p-4">
                                {/* Role Selection Tabs */}
                                <Tabs
                                    activeKey={activeTab}
                                    onSelect={(k) => {
                                        setActiveTab(k);
                                        setError('');
                                        setFormData({ username: '', password: '' });
                                    }}
                                    className="mb-4 nav-justified"
                                    variant="pills"
                                >
                                    <Tab eventKey="admin" title={
                                        <span><i className="fas fa-user-shield me-1"></i> Admin</span>
                                    } />
                                    <Tab eventKey="corporate" title={
                                        <span><i className="fas fa-building me-1"></i> Corporate</span>
                                    } />
                                    <Tab eventKey="headoffice" title={
                                        <span><i className="fas fa-landmark me-1"></i> Head Office</span>
                                    } />
                                    <Tab eventKey="regional" title={
                                        <span><i className="fas fa-map-marker-alt me-1"></i> Regional</span>
                                    } />
                                    <Tab eventKey="branch" title={
                                        <span><i className="fas fa-store me-1"></i> Branch</span>
                                    } />
                                </Tabs>

                                {/* Description */}
                                <div className="text-center mb-4">
                                    <p className="text-muted">{currentConfig.description}</p>
                                </div>

                                {error && (
                                    <Alert variant="danger" className="mb-4">
                                        <i className="fas fa-exclamation-triangle me-2"></i>
                                        {error}
                                    </Alert>
                                )}

                                <Form onSubmit={handleSubmit}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>
                                            <i className="fas fa-user me-2"></i>Username
                                        </Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="username"
                                            value={formData.username}
                                            onChange={handleChange}
                                            placeholder="Enter your username"
                                            required
                                            style={{ 
                                                borderRadius: '10px',
                                                padding: '12px 15px',
                                                border: '2px solid #e9ecef',
                                                fontSize: '1rem'
                                            }}
                                        />
                                    </Form.Group>

                                    <Form.Group className="mb-4">
                                        <Form.Label>
                                            <i className="fas fa-lock me-2"></i>Password
                                        </Form.Label>
                                        <Form.Control
                                            type="password"
                                            name="password"
                                            value={formData.password}
                                            onChange={handleChange}
                                            placeholder="Enter your password"
                                            required
                                            style={{ 
                                                borderRadius: '10px',
                                                padding: '12px 15px',
                                                border: '2px solid #e9ecef',
                                                fontSize: '1rem'
                                            }}
                                        />
                                    </Form.Group>

                                    <div className="d-grid mb-3">
                                        <Button 
                                            type="submit" 
                                            disabled={loading}
                                            style={{
                                                background: currentConfig.gradient,
                                                border: 'none',
                                                borderRadius: '10px',
                                                padding: '12px',
                                                fontSize: '1.1rem',
                                                fontWeight: '600'
                                            }}
                                        >
                                            {loading ? (
                                                <>
                                                    <Spinner
                                                        as="span"
                                                        animation="border"
                                                        size="sm"
                                                        role="status"
                                                        aria-hidden="true"
                                                        className="me-2"
                                                    />
                                                    Signing In...
                                                </>
                                            ) : (
                                                <>
                                                    <i className="fas fa-sign-in-alt me-2"></i>
                                                    Sign In to {currentConfig.title.replace(' Login', '')}
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                </Form>

                                {/* Demo Credentials */}
                                <div className="text-center">
                                    <small className="text-muted">Demo Credentials:</small>
                                    <div className="mt-2">
                                        <Button
                                            variant="outline-secondary"
                                            size="sm"
                                            onClick={() => fillDemoCredentials(activeTab)}
                                            style={{ borderRadius: '20px' }}
                                        >
                                            <i className="fas fa-magic me-1"></i>
                                            Fill Demo Login
                                        </Button>
                                    </div>
                                </div>

                                <hr className="my-4" />

                                <div className="text-center">
                                    <small className="text-muted">
                                        <i className="fas fa-shield-alt me-1"></i>
                                        Secure Multi-Role Authentication System
                                    </small>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>

                {/* Features Section */}
                <Row className="justify-content-center mt-4">
                    <Col md={10} lg={8}>
                        <Card className="bg-transparent border-0">
                            <Card.Body className="text-center text-white">
                                <h5 className="mb-3">System Features</h5>
                                <Row>
                                    <Col md={2} className="mb-3">
                                        <i className="fas fa-users fa-2x mb-2"></i>
                                        <p className="small mb-0">Multi-Role Access</p>
                                    </Col>
                                    <Col md={2} className="mb-3">
                                        <i className="fas fa-shield-alt fa-2x mb-2"></i>
                                        <p className="small mb-0">Secure Authentication</p>
                                    </Col>
                                    <Col md={2} className="mb-3">
                                        <i className="fas fa-sitemap fa-2x mb-2"></i>
                                        <p className="small mb-0">Hierarchical Management</p>
                                    </Col>
                                    <Col md={2} className="mb-3">
                                        <i className="fas fa-chart-bar fa-2x mb-2"></i>
                                        <p className="small mb-0">Analytics & Reports</p>
                                    </Col>
                                    <Col md={2} className="mb-3">
                                        <i className="fas fa-mobile-alt fa-2x mb-2"></i>
                                        <p className="small mb-0">Responsive Design</p>
                                    </Col>
                                    <Col md={2} className="mb-3">
                                        <i className="fas fa-clock fa-2x mb-2"></i>
                                        <p className="small mb-0">Real-time Updates</p>
                                    </Col>
                                </Row>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default UnifiedLogin;
