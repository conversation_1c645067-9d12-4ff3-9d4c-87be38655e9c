import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import HomeScreen from './HomeScreen';
import BackendDashboardLogin from './BackendDashboardLogin';

const AuthPage = () => {
  const { currentUser } = useAuth();

  // If user is logged in, show home screen
  if (currentUser) {
    return <HomeScreen />;
  }

  // If not logged in, show backend dashboard login
  return <BackendDashboardLogin />;
};

export default AuthPage;
