-- =====================================================
-- ADMIN DASHBOARD COMPREHENSIVE DATABASE SCHEMA
-- =====================================================

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS category_level3 CASCADE;
DROP TABLE IF EXISTS category_level2 CASCADE;
DROP TABLE IF EXISTS category_level1 CASCADE;
DROP TABLE IF EXISTS main_categories CASCADE;
DROP TABLE IF EXISTS professions CASCADE;
DROP TABLE IF EXISTS education_levels CASCADE;
DROP TABLE IF EXISTS languages CASCADE;
DROP TABLE IF EXISTS profile_groups CASCADE;
DROP TABLE IF EXISTS corporate_users CASCADE;

-- =====================================================
-- 1. PROFILE GROUPS TABLE
-- =====================================================
CREATE TABLE profile_groups (
    id SERIAL PRIMARY KEY,
    group_type VARCHAR(50) NOT NULL CHECK (group_type IN ('My Apps', 'My Company', 'My Online Apps', 'My Offline Apps')),
    group_name VARCHAR(100) NOT NULL,
    icon_url TEXT,
    logo_url TEXT,
    name_image_url TEXT,
    background_color VARCHAR(7) DEFAULT '#ffffff',
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    display_order INTEGER DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(group_type, group_name)
);

-- =====================================================
-- 2. LANGUAGES TABLE
-- =====================================================
CREATE TABLE languages (
    id SERIAL PRIMARY KEY,
    country_id INTEGER REFERENCES countries(id) ON DELETE CASCADE,
    language_1 VARCHAR(100) NOT NULL,
    language_2 VARCHAR(100),
    description TEXT,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(country_id, language_1)
);

-- =====================================================
-- 3. EDUCATION LEVELS TABLE
-- =====================================================
CREATE TABLE education_levels (
    id SERIAL PRIMARY KEY,
    education_level VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'Primary', 'Secondary', 'Higher Secondary', 'Diploma', 
        'Undergraduate', 'Postgraduate', 'Doctoral', 'Professional', 
        'Vocational', 'Certification'
    )),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. PROFESSIONS TABLE
-- =====================================================
CREATE TABLE professions (
    id SERIAL PRIMARY KEY,
    profession_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'Technology', 'Healthcare', 'Education', 'Business', 'Engineering',
        'Finance', 'Legal', 'Arts & Design', 'Sales & Marketing', 
        'Manufacturing', 'Construction', 'Transportation', 'Government', 
        'Non-Profit', 'Other'
    )),
    industry VARCHAR(100) CHECK (industry IN (
        'Information Technology', 'Healthcare & Medicine', 'Education',
        'Business Services', 'Engineering', 'Financial Services', 'Legal Services',
        'Creative & Design', 'Sales & Marketing', 'Manufacturing', 'Construction',
        'Transportation & Logistics', 'Government & Public Sector', 
        'Non-Profit Organizations', 'Retail & E-commerce', 'Hospitality & Tourism',
        'Agriculture', 'Energy & Utilities', 'Real Estate', 'Other'
    )),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. MAIN CATEGORIES TABLE
-- =====================================================
CREATE TABLE main_categories (
    id SERIAL PRIMARY KEY,
    group_id INTEGER REFERENCES profile_groups(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_class VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(group_id, name),
    CONSTRAINT max_main_categories_per_group CHECK (
        (SELECT COUNT(*) FROM main_categories WHERE group_id = main_categories.group_id) <= 6
    )
);

-- =====================================================
-- 6. CATEGORY LEVEL 1 TABLE
-- =====================================================
CREATE TABLE category_level1 (
    id SERIAL PRIMARY KEY,
    main_category_id INTEGER REFERENCES main_categories(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_class VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(main_category_id, name)
);

-- =====================================================
-- 7. CATEGORY LEVEL 2 TABLE
-- =====================================================
CREATE TABLE category_level2 (
    id SERIAL PRIMARY KEY,
    level1_category_id INTEGER REFERENCES category_level1(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_class VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(level1_category_id, name)
);

-- =====================================================
-- 8. CATEGORY LEVEL 3 TABLE
-- =====================================================
CREATE TABLE category_level3 (
    id SERIAL PRIMARY KEY,
    level2_category_id INTEGER REFERENCES category_level2(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(level2_category_id, name)
);

-- =====================================================
-- 9. CORPORATE USERS TABLE (Enhanced)
-- =====================================================
CREATE TABLE corporate_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name VARCHAR(100) NOT NULL,
    mobile_number VARCHAR(20) NOT NULL,
    email_id VARCHAR(100) NOT NULL UNIQUE,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'CORPORATE' CHECK (role IN ('CORPORATE', 'HEAD_OFFICE', 'REGIONAL', 'BRANCH')),
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
    last_login TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Profile Groups Indexes
CREATE INDEX idx_profile_groups_type ON profile_groups(group_type);
CREATE INDEX idx_profile_groups_status ON profile_groups(status);
CREATE INDEX idx_profile_groups_created_by ON profile_groups(created_by);

-- Languages Indexes
CREATE INDEX idx_languages_country ON languages(country_id);
CREATE INDEX idx_languages_status ON languages(status);

-- Education Levels Indexes
CREATE INDEX idx_education_category ON education_levels(category);
CREATE INDEX idx_education_status ON education_levels(status);

-- Professions Indexes
CREATE INDEX idx_professions_category ON professions(category);
CREATE INDEX idx_professions_industry ON professions(industry);
CREATE INDEX idx_professions_status ON professions(status);

-- Categories Indexes
CREATE INDEX idx_main_categories_group ON main_categories(group_id);
CREATE INDEX idx_main_categories_status ON main_categories(status);
CREATE INDEX idx_level1_main_category ON category_level1(main_category_id);
CREATE INDEX idx_level2_level1_category ON category_level2(level1_category_id);
CREATE INDEX idx_level3_level2_category ON category_level3(level2_category_id);

-- Corporate Users Indexes
CREATE INDEX idx_corporate_users_email ON corporate_users(email_id);
CREATE INDEX idx_corporate_users_username ON corporate_users(username);
CREATE INDEX idx_corporate_users_status ON corporate_users(status);
CREATE INDEX idx_corporate_users_role ON corporate_users(role);
CREATE INDEX idx_corporate_users_created_by ON corporate_users(created_by);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
CREATE TRIGGER update_profile_groups_updated_at BEFORE UPDATE ON profile_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_languages_updated_at BEFORE UPDATE ON languages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_education_levels_updated_at BEFORE UPDATE ON education_levels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_professions_updated_at BEFORE UPDATE ON professions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_main_categories_updated_at BEFORE UPDATE ON main_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_category_level1_updated_at BEFORE UPDATE ON category_level1 FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_category_level2_updated_at BEFORE UPDATE ON category_level2 FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_category_level3_updated_at BEFORE UPDATE ON category_level3 FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_corporate_users_updated_at BEFORE UPDATE ON corporate_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
