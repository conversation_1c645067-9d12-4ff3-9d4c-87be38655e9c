/* Header.css */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-row-1 {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-row-2 {
  padding: 10px 0;
}

/* More button */
.more-btn {
  color: white !important;
  text-decoration: none !important;
}

.more-btn:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Group data horizontal display */
.group-data-horizontal {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.group-data-horizontal::-webkit-scrollbar {
  display: none;
}

.group-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 15px;
  white-space: nowrap;
  font-size: 0.8rem;
  min-width: fit-content;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.group-icon {
  font-size: 1rem;
}

.group-name {
  font-weight: 500;
}

/* Profile button */
.profile-btn {
  color: white !important;
  text-decoration: none !important;
}

.profile-btn:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Search form */
.search-form {
  margin: 0;
}

.search-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  border-radius: 20px !important;
  border: none !important;
  padding-right: 40px !important;
  font-size: 0.9rem;
  background: rgba(255, 255, 255, 0.9);
}

.search-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
  background: white;
}

.search-btn {
  position: absolute;
  right: 10px;
  color: #666 !important;
  text-decoration: none !important;
  z-index: 10;
}

.search-btn:hover {
  color: #333 !important;
}

/* Theme button */
.theme-btn {
  color: white !important;
  text-decoration: none !important;
}

.theme-btn:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Group logos */
.group-logos {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
}

.group-logo {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* More menu */
.more-menu {
  background: white;
  color: #333;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.more-menu-content {
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 18px;
  color: #666;
}

/* Dark theme styles */
[data-theme="dark"] .app-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .search-input {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

[data-theme="dark"] .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .search-btn {
  color: rgba(255, 255, 255, 0.7) !important;
}

[data-theme="dark"] .more-menu {
  background: #2c3e50;
  color: white;
}

[data-theme="dark"] .menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .menu-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .header-row-1,
  .header-row-2 {
    padding: 8px 0;
  }
  
  .group-item {
    font-size: 0.7rem;
    padding: 3px 8px;
  }
  
  .search-input {
    font-size: 0.8rem;
  }
  
  .group-logo {
    width: 20px;
    height: 20px;
  }
}
