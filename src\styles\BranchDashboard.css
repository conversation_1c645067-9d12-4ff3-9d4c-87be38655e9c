/* =====================================================
   BRANCH DASHBOARD STYLES
   ===================================================== */

.branch-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
}

/* =====================================================
   DASHBOARD CONTAINER
   ===================================================== */

.dashboard-container {
  display: flex;
  min-height: calc(100vh - 60px); /* Account for navbar height */
  margin-top: 60px; /* Navbar height */
}

/* =====================================================
   SIDEBAR STYLES
   ===================================================== */

.branch-dashboard .sidebar {
  width: 280px;
  min-height: calc(100vh - 60px);
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 60px;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.branch-dashboard .sidebar-header {
  padding: 25px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
}

.branch-dashboard .sidebar-header h5 {
  margin: 0;
  font-weight: 700;
  color: #ecf0f1;
  font-size: 1.3rem;
}

.branch-dashboard .sidebar-header small {
  color: #bdc3c7;
  font-size: 0.85rem;
}

.branch-dashboard .sidebar-menu {
  padding: 20px 0;
}

.branch-dashboard .menu-item {
  margin-bottom: 5px;
}

.branch-dashboard .menu-link {
  padding: 15px 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  color: #ecf0f1;
  text-decoration: none;
}

.branch-dashboard .menu-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #ff6b6b;
  color: white;
  text-decoration: none;
}

.branch-dashboard .menu-link.active {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: #ff4757;
  color: white;
  font-weight: 600;
}

.branch-dashboard .menu-group .menu-header {
  padding: 15px 25px;
  font-weight: 600;
  color: #bdc3c7;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 5px;
}

.branch-dashboard .submenu {
  background: rgba(0, 0, 0, 0.1);
}

.branch-dashboard .submenu-item {
  padding: 12px 25px 12px 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bdc3c7;
  font-size: 0.9rem;
  border-left: 3px solid transparent;
}

.branch-dashboard .submenu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: #ff6b6b;
}

.branch-dashboard .submenu-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 600;
  border-left-color: #ff4757;
}

/* =====================================================
   MAIN CONTENT STYLES
   ===================================================== */

.branch-dashboard .main-content {
  flex: 1;
  margin-left: 280px;
  padding: 30px;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
}

/* =====================================================
   DASHBOARD CARDS
   ===================================================== */

.branch-dashboard .dashboard-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 20px;
  overflow: hidden;
}

.branch-dashboard .dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.branch-dashboard .dashboard-card .card-body {
  padding: 25px;
}

.branch-dashboard .dashboard-card h3 {
  font-weight: 700;
  margin-bottom: 5px;
  font-size: 2rem;
}

.branch-dashboard .dashboard-card p {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
}

/* =====================================================
   CARD HEADERS
   ===================================================== */

.branch-dashboard .card-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  padding: 15px 20px;
  font-weight: 600;
}

.branch-dashboard .card-header h5 {
  margin: 0;
  font-size: 1.1rem;
}

/* =====================================================
   BUTTONS
   ===================================================== */

.branch-dashboard .btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.branch-dashboard .btn-primary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
}

.branch-dashboard .btn-primary:hover {
  background: linear-gradient(135deg, #e084fc 0%, #f4476b 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
}

.branch-dashboard .btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  border: none;
}

.branch-dashboard .btn-success:hover {
  background: linear-gradient(135deg, #4e9a2a 0%, #9dd9c3 100%);
  transform: translateY(-1px);
}

.branch-dashboard .btn-warning {
  background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
  border: none;
  color: #333;
}

.branch-dashboard .btn-warning:hover {
  background: linear-gradient(135deg, #f68b1e 0%, #ffcc00 100%);
  transform: translateY(-1px);
  color: #333;
}

.branch-dashboard .btn-info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border: none;
}

.branch-dashboard .btn-info:hover {
  background: linear-gradient(135deg, #6bb6ff 0%, #0875d1 100%);
  transform: translateY(-1px);
}

.branch-dashboard .btn-outline-primary {
  border-color: #f093fb;
  color: #f093fb;
}

.branch-dashboard .btn-outline-primary:hover {
  background: #f093fb;
  border-color: #f093fb;
  transform: translateY(-1px);
}

/* =====================================================
   TABLE STYLES
   ===================================================== */

.branch-dashboard .table {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.branch-dashboard .table thead th {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  font-weight: 600;
  padding: 15px;
}

.branch-dashboard .table tbody tr {
  transition: all 0.3s ease;
}

.branch-dashboard .table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
}

.branch-dashboard .table tbody td {
  padding: 15px;
  vertical-align: middle;
  border-color: #e9ecef;
}

/* =====================================================
   BADGES
   ===================================================== */

.branch-dashboard .badge {
  font-size: 0.8rem;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
  .branch-dashboard .sidebar {
    width: 100%;
    position: relative;
    min-height: auto;
  }
  
  .branch-dashboard .main-content {
    margin-left: 0;
    padding: 20px 15px;
  }
  
  .dashboard-container {
    flex-direction: column;
  }
  
  .branch-dashboard .dashboard-card h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .branch-dashboard .sidebar-header {
    padding: 20px 15px;
  }
  
  .branch-dashboard .menu-link,
  .branch-dashboard .submenu-item {
    padding-left: 20px;
    font-size: 0.9rem;
  }
  
  .branch-dashboard .submenu-item {
    padding-left: 40px;
  }
  
  .branch-dashboard .main-content {
    padding: 15px 10px;
  }
  
  .branch-dashboard .table {
    font-size: 0.85rem;
  }
}

/* =====================================================
   ANIMATIONS
   ===================================================== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.branch-dashboard .dashboard-card {
  animation: fadeInUp 0.6s ease-out;
}

.branch-dashboard .dashboard-card:nth-child(2) {
  animation-delay: 0.1s;
}

.branch-dashboard .dashboard-card:nth-child(3) {
  animation-delay: 0.2s;
}

.branch-dashboard .dashboard-card:nth-child(4) {
  animation-delay: 0.3s;
}

/* =====================================================
   SPECIAL EFFECTS
   ===================================================== */

.branch-dashboard .sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.branch-dashboard .card-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.branch-dashboard .card:hover .card-header::after {
  transform: translateX(100%);
}
