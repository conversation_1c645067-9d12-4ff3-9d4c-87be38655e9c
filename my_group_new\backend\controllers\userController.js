const User = require('../models/User');
const UserDetails = require('../models/UserDetails');

// Get all users
const getAllUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    const users = await User.getAll(page, limit);
    
    res.json({
      message: 'Users retrieved successfully',
      users,
      pagination: {
        page,
        limit,
        total: users.length
      }
    });

  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get user by ID
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      message: 'User retrieved successfully',
      user
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Update user status
const updateUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (![0, 1].includes(status)) {
      return res.status(400).json({ message: 'Status must be 0 (inactive) or 1 (active)' });
    }

    const updatedUser = await User.updateStatus(id, status);
    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      message: 'User status updated successfully',
      user: updatedUser
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get user details
const getUserDetails = async (req, res) => {
  try {
    const { id } = req.params;
    
    const userDetails = await UserDetails.findByUserId(id);
    if (!userDetails) {
      return res.status(404).json({ message: 'User details not found' });
    }

    res.json({
      message: 'User details retrieved successfully',
      userDetails
    });

  } catch (error) {
    console.error('Get user details error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Update user details
const updateUserDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined || updateData[key] === '') {
        delete updateData[key];
      }
    });

    const updatedDetails = await UserDetails.update(id, updateData);
    if (!updatedDetails) {
      return res.status(404).json({ message: 'User details not found' });
    }

    res.json({
      message: 'User details updated successfully',
      userDetails: updatedDetails
    });

  } catch (error) {
    console.error('Update user details error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get complete user profile
const getCompleteProfile = async (req, res) => {
  try {
    const { id } = req.params;
    
    const profile = await UserDetails.getCompleteProfile(id);
    if (!profile) {
      return res.status(404).json({ message: 'User profile not found' });
    }

    res.json({
      message: 'Complete profile retrieved successfully',
      profile
    });

  } catch (error) {
    console.error('Get complete profile error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Update current user profile
const updateProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const updateData = req.body;
    
    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined || updateData[key] === '') {
        delete updateData[key];
      }
    });

    const updatedDetails = await UserDetails.update(userId, updateData);
    
    res.json({
      message: 'Profile updated successfully',
      userDetails: updatedDetails
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  updateUserStatus,
  getUserDetails,
  updateUserDetails,
  getCompleteProfile,
  updateProfile
};
