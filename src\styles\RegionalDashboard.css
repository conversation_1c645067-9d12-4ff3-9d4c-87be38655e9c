/* =====================================================
   REGIONAL DASHBOARD STYLES
   ===================================================== */

.regional-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
}

/* =====================================================
   DASHBOARD CONTAINER
   ===================================================== */

.dashboard-container {
  display: flex;
  min-height: calc(100vh - 60px); /* Account for navbar height */
  margin-top: 60px; /* Navbar height */
}

/* =====================================================
   SIDEBAR STYLES
   ===================================================== */

.regional-dashboard .sidebar {
  width: 280px;
  min-height: calc(100vh - 60px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 60px;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.regional-dashboard .sidebar-header {
  padding: 25px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
}

.regional-dashboard .sidebar-header h5 {
  margin: 0;
  font-weight: 700;
  color: #ecf0f1;
  font-size: 1.3rem;
}

.regional-dashboard .sidebar-header small {
  color: #bdc3c7;
  font-size: 0.85rem;
}

.regional-dashboard .sidebar-menu {
  padding: 20px 0;
}

.regional-dashboard .menu-item {
  margin-bottom: 5px;
}

.regional-dashboard .menu-link {
  padding: 15px 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  color: #ecf0f1;
  text-decoration: none;
}

.regional-dashboard .menu-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #f39c12;
  color: white;
  text-decoration: none;
}

.regional-dashboard .menu-link.active {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: #e67e22;
  color: white;
  font-weight: 600;
}

.regional-dashboard .menu-group .menu-header {
  padding: 15px 25px;
  font-weight: 600;
  color: #bdc3c7;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 5px;
}

.regional-dashboard .submenu {
  background: rgba(0, 0, 0, 0.1);
}

.regional-dashboard .submenu-item {
  padding: 12px 25px 12px 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bdc3c7;
  font-size: 0.9rem;
  border-left: 3px solid transparent;
}

.regional-dashboard .submenu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: #f39c12;
}

.regional-dashboard .submenu-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 600;
  border-left-color: #e67e22;
}

/* =====================================================
   MAIN CONTENT STYLES
   ===================================================== */

.regional-dashboard .main-content {
  flex: 1;
  margin-left: 280px;
  padding: 30px;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
}

/* =====================================================
   DASHBOARD CARDS
   ===================================================== */

.regional-dashboard .dashboard-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 20px;
  overflow: hidden;
}

.regional-dashboard .dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.regional-dashboard .dashboard-card .card-body {
  padding: 25px;
}

.regional-dashboard .dashboard-card h3 {
  font-weight: 700;
  margin-bottom: 5px;
  font-size: 2rem;
}

.regional-dashboard .dashboard-card p {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
}

/* =====================================================
   PERFORMANCE METRICS
   ===================================================== */

.regional-dashboard .performance-metrics {
  padding: 10px 0;
}

.regional-dashboard .metric-item {
  padding: 10px 0;
}

.regional-dashboard .metric-item span {
  font-size: 0.9rem;
  font-weight: 500;
}

.regional-dashboard .progress {
  height: 8px;
  border-radius: 10px;
  background-color: #e9ecef;
}

.regional-dashboard .progress-bar {
  border-radius: 10px;
  transition: width 0.6s ease;
}

/* =====================================================
   ACTIVITY ITEMS
   ===================================================== */

.regional-dashboard .activity-item {
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.regional-dashboard .activity-item:last-child {
  border-bottom: none;
}

.regional-dashboard .activity-item small {
  font-size: 0.8rem;
  color: #6c757d;
}

.regional-dashboard .activity-item div {
  font-size: 0.9rem;
  color: #495057;
  margin-top: 2px;
}

/* =====================================================
   CARD HEADERS
   ===================================================== */

.regional-dashboard .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 20px;
  font-weight: 600;
}

.regional-dashboard .card-header h5 {
  margin: 0;
  font-size: 1.1rem;
}

/* =====================================================
   BUTTONS
   ===================================================== */

.regional-dashboard .btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.regional-dashboard .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.regional-dashboard .btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.regional-dashboard .btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.regional-dashboard .btn-outline-primary:hover {
  background: #667eea;
  border-color: #667eea;
  transform: translateY(-1px);
}

/* =====================================================
   TABLE STYLES
   ===================================================== */

.regional-dashboard .table {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.regional-dashboard .table thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-weight: 600;
  padding: 15px;
}

.regional-dashboard .table tbody tr {
  transition: all 0.3s ease;
}

.regional-dashboard .table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
}

.regional-dashboard .table tbody td {
  padding: 15px;
  vertical-align: middle;
  border-color: #e9ecef;
}

/* =====================================================
   BADGES
   ===================================================== */

.regional-dashboard .badge {
  font-size: 0.8rem;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
  .regional-dashboard .sidebar {
    width: 100%;
    position: relative;
    min-height: auto;
  }
  
  .regional-dashboard .main-content {
    margin-left: 0;
    padding: 20px 15px;
  }
  
  .dashboard-container {
    flex-direction: column;
  }
  
  .regional-dashboard .dashboard-card h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .regional-dashboard .sidebar-header {
    padding: 20px 15px;
  }
  
  .regional-dashboard .menu-link,
  .regional-dashboard .submenu-item {
    padding-left: 20px;
    font-size: 0.9rem;
  }
  
  .regional-dashboard .submenu-item {
    padding-left: 40px;
  }
  
  .regional-dashboard .main-content {
    padding: 15px 10px;
  }
  
  .regional-dashboard .table {
    font-size: 0.85rem;
  }
}

/* =====================================================
   ANIMATIONS
   ===================================================== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.regional-dashboard .dashboard-card {
  animation: fadeInUp 0.6s ease-out;
}

.regional-dashboard .dashboard-card:nth-child(2) {
  animation-delay: 0.1s;
}

.regional-dashboard .dashboard-card:nth-child(3) {
  animation-delay: 0.2s;
}

.regional-dashboard .dashboard-card:nth-child(4) {
  animation-delay: 0.3s;
}

/* =====================================================
   SPECIAL EFFECTS
   ===================================================== */

.regional-dashboard .sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.regional-dashboard .card-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.regional-dashboard .card:hover .card-header::after {
  transform: translateX(100%);
}
