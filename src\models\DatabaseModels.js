// Database Models for MyGroup Application
// This file contains model definitions and factory functions for database entities

// User Model
export class User {
  constructor(data = {}) {
    this.id = data.id || null;
    this.firstName = data.firstName || '';
    this.lastName = data.lastName || '';
    this.email = data.email || '';
    this.phone = data.phone || '';
    this.role = data.role || 'customer';
    this.status = data.status || 'active';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.createdBy = data.createdBy || null;
    this.lastLogin = data.lastLogin || null;
    this.profileImage = data.profileImage || null;
  }

  // Get full name
  getFullName() {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  // Check if user is active
  isActive() {
    return this.status === 'active';
  }

  // Get user display info
  getDisplayInfo() {
    return {
      id: this.id,
      name: this.getFullName(),
      email: this.email,
      role: this.role,
      status: this.status
    };
  }

  // Validate user data
  validate() {
    const errors = [];
    
    if (!this.firstName.trim()) {
      errors.push('First name is required');
    }
    
    if (!this.lastName.trim()) {
      errors.push('Last name is required');
    }
    
    if (!this.email.trim()) {
      errors.push('Email is required');
    } else if (!this.isValidEmail(this.email)) {
      errors.push('Invalid email format');
    }
    
    return errors;
  }

  // Email validation helper
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// Role Model
export class Role {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.displayName = data.displayName || '';
    this.description = data.description || '';
    this.permissions = data.permissions || [];
    this.level = data.level || 0;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // Check if role has specific permission
  hasPermission(permission) {
    return this.permissions.includes(permission);
  }

  // Get role hierarchy level
  getLevel() {
    const roleLevels = {
      'admin': 100,
      'corporate': 80,
      'headoffice': 60,
      'regional': 40,
      'branch': 20,
      'customer': 10,
      'partner': 15
    };
    return roleLevels[this.name] || 0;
  }
}

// Organization Model
export class Organization {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.type = data.type || ''; // corporate, headoffice, regional, branch
    this.parentId = data.parentId || null;
    this.address = data.address || '';
    this.phone = data.phone || '';
    this.email = data.email || '';
    this.managerId = data.managerId || null;
    this.status = data.status || 'active';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // Check if organization is active
  isActive() {
    return this.status === 'active';
  }
}

// Model Factory
export class ModelFactory {
  // Create User instance from API response
  static createUser(data) {
    return new User(data);
  }

  // Create multiple User instances
  static createUsers(dataArray) {
    return dataArray.map(data => new User(data));
  }

  // Create Role instance
  static createRole(data) {
    return new Role(data);
  }

  // Create multiple Role instances
  static createRoles(dataArray) {
    return dataArray.map(data => new Role(data));
  }

  // Create Organization instance
  static createOrganization(data) {
    return new Organization(data);
  }

  // Create multiple Organization instances
  static createOrganizations(dataArray) {
    return dataArray.map(data => new Organization(data));
  }

  // Create model based on type
  static createModel(type, data) {
    switch (type.toLowerCase()) {
      case 'user':
        return new User(data);
      case 'role':
        return new Role(data);
      case 'organization':
        return new Organization(data);
      default:
        throw new Error(`Unknown model type: ${type}`);
    }
  }
}

// Database Schema Definitions
export const DatabaseSchema = {
  users: {
    tableName: 'my_users',
    fields: {
      id: { type: 'integer', primaryKey: true, autoIncrement: true },
      firstName: { type: 'varchar', length: 100, required: true },
      lastName: { type: 'varchar', length: 100, required: true },
      email: { type: 'varchar', length: 255, required: true, unique: true },
      phone: { type: 'varchar', length: 20 },
      password: { type: 'varchar', length: 255, required: true },
      role: { type: 'varchar', length: 50, default: 'customer' },
      status: { type: 'varchar', length: 20, default: 'active' },
      createdBy: { type: 'integer', foreignKey: 'my_users.id' },
      createdAt: { type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
      updatedAt: { type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
      lastLogin: { type: 'timestamp', nullable: true },
      profileImage: { type: 'text', nullable: true }
    }
  },
  
  roles: {
    tableName: 'my_roles',
    fields: {
      id: { type: 'integer', primaryKey: true, autoIncrement: true },
      name: { type: 'varchar', length: 50, required: true, unique: true },
      displayName: { type: 'varchar', length: 100, required: true },
      description: { type: 'text' },
      permissions: { type: 'json' },
      level: { type: 'integer', default: 0 },
      createdAt: { type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
      updatedAt: { type: 'timestamp', default: 'CURRENT_TIMESTAMP' }
    }
  },

  organizations: {
    tableName: 'my_organizations',
    fields: {
      id: { type: 'integer', primaryKey: true, autoIncrement: true },
      name: { type: 'varchar', length: 200, required: true },
      type: { type: 'varchar', length: 50, required: true },
      parentId: { type: 'integer', foreignKey: 'my_organizations.id', nullable: true },
      address: { type: 'text' },
      phone: { type: 'varchar', length: 20 },
      email: { type: 'varchar', length: 255 },
      managerId: { type: 'integer', foreignKey: 'my_users.id', nullable: true },
      status: { type: 'varchar', length: 20, default: 'active' },
      createdAt: { type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
      updatedAt: { type: 'timestamp', default: 'CURRENT_TIMESTAMP' }
    }
  }
};

// Export default factory
export default ModelFactory;
