const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if user still exists and is active
    const user = await User.findById(decoded.userId);
    if (!user || user.status !== 1) {
      return res.status(401).json({ message: 'Invalid token. User not found or inactive.' });
    }

    req.user = decoded;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token.' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired.' });
    }
    
    console.error('Auth middleware error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Check if user has required role
const requireRole = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.userId;
      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }

      if (!allowedRoles.includes(user.role_id)) {
        return res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
      }

      req.userRole = user.role;
      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  };
};

// Role constants for easy reference
const ROLES = {
  ADMIN: 1,
  CORPORATE: 2,
  HEAD_OFFICE: 3,
  REGIONAL: 4,
  BRANCH: 5,
  CUSTOMER: 6,
  PARTNER: 7,
  APP_ADMIN: 8
};

module.exports = {
  verifyToken,
  requireRole,
  ROLES
};
