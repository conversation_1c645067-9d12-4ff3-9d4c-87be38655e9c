import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Modal, Form, Alert, Table, Badge } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import Navbar from './Navbar';

// Import Regional Dashboard Components
import BranchManagement from './regional/BranchManagement';
// import UserManagement from './regional/UserManagement';
// import LocalAdsManagement from './regional/LocalAdsManagement';
// import RegionalCampaigns from './regional/RegionalCampaigns';
// import RegionalReports from './regional/RegionalReports';
// import ChangePasswordManagement from './regional/ChangePasswordManagement';
// import NotificationSystem from './NotificationSystem';

import '../styles/RegionalDashboard.css';

const RegionalDashboard = () => {
  const { currentUser, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  // Sidebar menu items for Regional Dashboard
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    {
      id: 'branch-management',
      label: 'Branch Management',
      icon: 'fas fa-store',
      hasSubmenu: true,
      submenu: [
        { id: 'branch-offices', label: 'Branch Offices' },
        { id: 'branch-performance', label: 'Performance Analytics' },
        { id: 'branch-reports', label: 'Branch Reports' }
      ]
    },
    {
      id: 'user-management',
      label: 'User Management',
      icon: 'fas fa-users',
      hasSubmenu: true,
      submenu: [
        { id: 'branch-users', label: 'Branch Users' },
        { id: 'user-roles', label: 'User Roles' },
        { id: 'user-permissions', label: 'Permissions' }
      ]
    },
    {
      id: 'advertisement-management',
      label: 'Advertisement Management',
      icon: 'fas fa-ad',
      hasSubmenu: true,
      submenu: [
        { id: 'local-ads', label: 'Local Advertisements' },
        { id: 'regional-campaigns', label: 'Regional Campaigns' },
        { id: 'ad-analytics', label: 'Ad Analytics' }
      ]
    },
    {
      id: 'financial-overview',
      label: 'Financial Overview',
      icon: 'fas fa-chart-line',
      hasSubmenu: true,
      submenu: [
        { id: 'revenue-tracking', label: 'Revenue Tracking' },
        { id: 'expense-management', label: 'Expense Management' },
        { id: 'financial-reports', label: 'Financial Reports' }
      ]
    },
    {
      id: 'reports-analytics',
      label: 'Reports & Analytics',
      icon: 'fas fa-chart-bar',
      hasSubmenu: true,
      submenu: [
        { id: 'regional-reports', label: 'Regional Reports' },
        { id: 'performance-metrics', label: 'Performance Metrics' },
        { id: 'comparative-analysis', label: 'Comparative Analysis' }
      ]
    },
    { id: 'notifications', label: 'Notifications', icon: 'fas fa-bell' },
    { id: 'change-password', label: 'Change Password', icon: 'fas fa-key' }
  ];

  const renderMainContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h4>Regional Dashboard</h4>
              <div className="d-flex gap-2">
                <Button variant="outline-primary" size="sm">
                  <i className="fas fa-download me-2"></i>Export Report
                </Button>
                <Button variant="primary" size="sm">
                  <i className="fas fa-plus me-2"></i>Quick Action
                </Button>
              </div>
            </div>

            {/* Dashboard Statistics */}
            <Row className="mb-4">
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-store fa-2x text-primary mb-2"></i>
                    <h3 className="text-primary">12</h3>
                    <p className="mb-0">Branch Offices</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-users fa-2x text-success mb-2"></i>
                    <h3 className="text-success">345</h3>
                    <p className="mb-0">Active Users</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-ad fa-2x text-info mb-2"></i>
                    <h3 className="text-info">28</h3>
                    <p className="mb-0">Active Campaigns</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-dollar-sign fa-2x text-warning mb-2"></i>
                    <h3 className="text-warning">$125,450</h3>
                    <p className="mb-0">Monthly Revenue</p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Performance Overview and Recent Activities */}
            <Row>
              <Col md={8}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-chart-line me-2"></i>Regional Performance Overview
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="performance-metrics">
                      <Row>
                        <Col md={6}>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>Branch Performance</span>
                              <span className="text-success">+18%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-success" style={{width: '88%'}}></div>
                            </div>
                          </div>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>User Engagement</span>
                              <span className="text-info">+14%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-info" style={{width: '76%'}}></div>
                            </div>
                          </div>
                        </Col>
                        <Col md={6}>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>Campaign Effectiveness</span>
                              <span className="text-primary">+25%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-primary" style={{width: '92%'}}></div>
                            </div>
                          </div>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>Revenue Growth</span>
                              <span className="text-warning">+19%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-warning" style={{width: '85%'}}></div>
                            </div>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-tasks me-2"></i>Recent Activities
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="activity-item mb-2">
                      <small className="text-muted">15 minutes ago</small>
                      <div>New branch user registered</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">1 hour ago</small>
                      <div>Regional campaign launched</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">3 hours ago</small>
                      <div>Branch performance report generated</div>
                    </div>
                    <div className="activity-item">
                      <small className="text-muted">6 hours ago</small>
                      <div>User permissions updated</div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Top Performing Branches */}
            <Row className="mt-4">
              <Col md={12}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-trophy me-2"></i>Top Performing Branches
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Table responsive hover>
                      <thead>
                        <tr>
                          <th>Rank</th>
                          <th>Branch Name</th>
                          <th>Location</th>
                          <th>Users</th>
                          <th>Revenue</th>
                          <th>Growth</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><Badge bg="warning">1</Badge></td>
                          <td><strong>Downtown Branch</strong></td>
                          <td>San Francisco, CA</td>
                          <td>45</td>
                          <td className="text-success">$35,200</td>
                          <td><span className="text-success">+22%</span></td>
                          <td><Badge bg="success">Excellent</Badge></td>
                        </tr>
                        <tr>
                          <td><Badge bg="secondary">2</Badge></td>
                          <td><strong>Westside Branch</strong></td>
                          <td>Los Angeles, CA</td>
                          <td>38</td>
                          <td className="text-success">$28,900</td>
                          <td><span className="text-success">+18%</span></td>
                          <td><Badge bg="success">Excellent</Badge></td>
                        </tr>
                        <tr>
                          <td><Badge bg="secondary">3</Badge></td>
                          <td><strong>Central Branch</strong></td>
                          <td>Sacramento, CA</td>
                          <td>32</td>
                          <td className="text-success">$24,500</td>
                          <td><span className="text-info">+15%</span></td>
                          <td><Badge bg="info">Good</Badge></td>
                        </tr>
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </div>
        );

      // Component cases
      case 'branch-offices':
        return <BranchManagement />;

      case 'branch-users':
        return (
          <div className="text-center py-5">
            <i className="fas fa-users fa-3x text-muted mb-3"></i>
            <h3>User Management</h3>
            <p className="text-muted">Manage users within your regional jurisdiction</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'local-ads':
        return (
          <div className="text-center py-5">
            <i className="fas fa-ad fa-3x text-muted mb-3"></i>
            <h3>Local Advertisement Management</h3>
            <p className="text-muted">Manage local advertisements and campaigns</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'regional-campaigns':
        return (
          <div className="text-center py-5">
            <i className="fas fa-bullhorn fa-3x text-muted mb-3"></i>
            <h3>Regional Campaigns</h3>
            <p className="text-muted">Manage regional advertising campaigns</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'revenue-tracking':
        return (
          <div className="text-center py-5">
            <i className="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h3>Revenue Tracking</h3>
            <p className="text-muted">Track and analyze regional revenue</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'regional-reports':
        return (
          <div className="text-center py-5">
            <i className="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h3>Regional Reports</h3>
            <p className="text-muted">Comprehensive regional reporting and analytics</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'notifications':
        return <NotificationSystem />;

      case 'change-password':
        return (
          <div className="text-center py-5">
            <i className="fas fa-key fa-3x text-muted mb-3"></i>
            <h3>Change Password</h3>
            <p className="text-muted">Update your account password</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      default:
        return (
          <div className="text-center py-5">
            <i className="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
            <h3>Welcome to Regional Dashboard</h3>
            <p className="text-muted">Select a menu item to get started</p>
          </div>
        );
    }
  };

  return (
    <div className="regional-dashboard">
      <Navbar />
      
      <div className="dashboard-container">
        {/* Sidebar */}
        <div className="sidebar">
          <div className="sidebar-header">
            <h5 className="text-white mb-0">
              <i className="fas fa-map-marker-alt me-2"></i>Regional Office
            </h5>
            <small className="text-white-50">Management Portal</small>
          </div>
          
          <div className="sidebar-menu">
            {sidebarItems.map((item) => (
              <div key={item.id} className="menu-item">
                {item.hasSubmenu ? (
                  <div className="menu-group">
                    <div className="menu-header">
                      <i className={`${item.icon} me-2`}></i>
                      {item.label}
                    </div>
                    <div className="submenu">
                      {item.submenu.map((subItem) => (
                        <div
                          key={subItem.id}
                          className={`submenu-item ${activeSection === subItem.id ? 'active' : ''}`}
                          onClick={() => setActiveSection(subItem.id)}
                        >
                          {subItem.label}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div
                    className={`menu-link ${activeSection === item.id ? 'active' : ''}`}
                    onClick={() => setActiveSection(item.id)}
                  >
                    <i className={`${item.icon} me-2`}></i>
                    {item.label}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <Container fluid>
            {error && (
              <Alert variant="danger" dismissible onClose={() => setError('')}>
                {error}
              </Alert>
            )}
            {success && (
              <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                {success}
              </Alert>
            )}
            
            {renderMainContent()}
          </Container>
        </div>
      </div>
    </div>
  );
};

export default RegionalDashboard;
