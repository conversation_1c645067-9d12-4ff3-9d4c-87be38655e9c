import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge, Dropdown, InputGroup } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';

const TransactionManagement = () => {
    const { currentUser } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState(''); // 'deposit', 'withdrawal', 'transfer', 'view'
    const [selectedTransaction, setSelectedTransaction] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('All');
    const [filterStatus, setFilterStatus] = useState('All');
    const [dateFilter, setDateFilter] = useState('Today');

    // Sample transaction data
    const [transactions, setTransactions] = useState([
        {
            id: 1,
            type: 'Deposit',
            amount: 5000,
            customer: 'John Doe',
            accountNumber: 'ACC001',
            date: '2024-01-18 09:30 AM',
            status: 'Completed',
            reference: 'TXN001',
            description: 'Cash deposit',
            processedBy: 'Alice Johnson'
        },
        {
            id: 2,
            type: 'Withdrawal',
            amount: 2000,
            customer: 'Jane Smith',
            accountNumber: 'ACC002',
            date: '2024-01-18 10:15 AM',
            status: 'Completed',
            reference: 'TXN002',
            description: 'ATM withdrawal',
            processedBy: 'Bob Wilson'
        },
        {
            id: 3,
            type: 'Transfer',
            amount: 1500,
            customer: 'Bob Johnson',
            accountNumber: 'ACC003',
            date: '2024-01-18 11:45 AM',
            status: 'Pending',
            reference: 'TXN003',
            description: 'Transfer to ACC005',
            processedBy: 'Carol Davis'
        },
        {
            id: 4,
            type: 'Deposit',
            amount: 3200,
            customer: 'Alice Brown',
            accountNumber: 'ACC004',
            date: '2024-01-18 14:20 PM',
            status: 'Completed',
            reference: 'TXN004',
            description: 'Check deposit',
            processedBy: 'David Lee'
        },
        {
            id: 5,
            type: 'Withdrawal',
            amount: 800,
            customer: 'Charlie Wilson',
            accountNumber: 'ACC005',
            date: '2024-01-18 15:10 PM',
            status: 'Failed',
            reference: 'TXN005',
            description: 'Insufficient funds',
            processedBy: 'Eva Martinez'
        }
    ]);

    // Form data for new transactions
    const [formData, setFormData] = useState({
        type: 'Deposit',
        accountNumber: '',
        customerName: '',
        amount: '',
        description: '',
        reference: ''
    });

    const handleInputChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const generateReference = () => {
        const lastTransaction = transactions[transactions.length - 1];
        const lastNumber = lastTransaction ? parseInt(lastTransaction.reference.slice(3)) : 0;
        return `TXN${String(lastNumber + 1).padStart(3, '0')}`;
    };

    const handleNewTransaction = (type) => {
        setModalType(type);
        setSelectedTransaction(null);
        setFormData({
            type: type,
            accountNumber: '',
            customerName: '',
            amount: '',
            description: '',
            reference: generateReference()
        });
        setShowModal(true);
    };

    const handleViewTransaction = (transaction) => {
        setModalType('view');
        setSelectedTransaction(transaction);
        setShowModal(true);
    };

    const handleSubmitTransaction = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            const newTransaction = {
                id: Date.now(),
                type: formData.type,
                amount: parseFloat(formData.amount),
                customer: formData.customerName,
                accountNumber: formData.accountNumber,
                date: new Date().toLocaleString(),
                status: 'Completed',
                reference: formData.reference,
                description: formData.description,
                processedBy: currentUser?.name || 'Current User'
            };

            setTransactions([newTransaction, ...transactions]);
            setSuccess(`${formData.type} transaction processed successfully`);
            setShowModal(false);
        } catch (error) {
            setError('Transaction processing failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Filter transactions
    const filteredTransactions = transactions.filter(transaction => {
        const matchesSearch = transaction.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            transaction.accountNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            transaction.reference.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesType = filterType === 'All' || transaction.type === filterType;
        const matchesStatus = filterStatus === 'All' || transaction.status === filterStatus;
        return matchesSearch && matchesType && matchesStatus;
    });

    // Calculate statistics
    const totalDeposits = transactions.filter(t => t.type === 'Deposit' && t.status === 'Completed')
                                    .reduce((sum, t) => sum + t.amount, 0);
    const totalWithdrawals = transactions.filter(t => t.type === 'Withdrawal' && t.status === 'Completed')
                                       .reduce((sum, t) => sum + t.amount, 0);
    const totalTransfers = transactions.filter(t => t.type === 'Transfer' && t.status === 'Completed')
                                     .reduce((sum, t) => sum + t.amount, 0);
    const pendingTransactions = transactions.filter(t => t.status === 'Pending').length;

    const getStatusBadge = (status) => {
        switch (status) {
            case 'Completed': return 'success';
            case 'Pending': return 'warning';
            case 'Failed': return 'danger';
            default: return 'secondary';
        }
    };

    const getTypeBadge = (type) => {
        switch (type) {
            case 'Deposit': return 'success';
            case 'Withdrawal': return 'danger';
            case 'Transfer': return 'info';
            default: return 'secondary';
        }
    };

    return (
        <Container fluid className="transaction-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Transaction Management</h4>
                <div className="d-flex gap-2">
                    <Button variant="success" onClick={() => handleNewTransaction('Deposit')}>
                        <i className="fas fa-plus me-2"></i>New Deposit
                    </Button>
                    <Button variant="warning" onClick={() => handleNewTransaction('Withdrawal')}>
                        <i className="fas fa-minus me-2"></i>New Withdrawal
                    </Button>
                    <Button variant="info" onClick={() => handleNewTransaction('Transfer')}>
                        <i className="fas fa-exchange-alt me-2"></i>New Transfer
                    </Button>
                </div>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-arrow-down fa-2x text-success mb-2"></i>
                            <h3 className="text-success">${totalDeposits.toLocaleString()}</h3>
                            <p className="mb-0">Total Deposits</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-arrow-up fa-2x text-danger mb-2"></i>
                            <h3 className="text-danger">${totalWithdrawals.toLocaleString()}</h3>
                            <p className="mb-0">Total Withdrawals</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-exchange-alt fa-2x text-info mb-2"></i>
                            <h3 className="text-info">${totalTransfers.toLocaleString()}</h3>
                            <p className="mb-0">Total Transfers</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h3 className="text-warning">{pendingTransactions}</h3>
                            <p className="mb-0">Pending Transactions</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Search and Filter */}
            <Row className="mb-3">
                <Col md={4}>
                    <InputGroup>
                        <InputGroup.Text>
                            <i className="fas fa-search"></i>
                        </InputGroup.Text>
                        <Form.Control
                            type="text"
                            placeholder="Search by customer, account, or reference..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </InputGroup>
                </Col>
                <Col md={2}>
                    <Form.Select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                    >
                        <option value="All">All Types</option>
                        <option value="Deposit">Deposits</option>
                        <option value="Withdrawal">Withdrawals</option>
                        <option value="Transfer">Transfers</option>
                    </Form.Select>
                </Col>
                <Col md={2}>
                    <Form.Select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                    >
                        <option value="All">All Status</option>
                        <option value="Completed">Completed</option>
                        <option value="Pending">Pending</option>
                        <option value="Failed">Failed</option>
                    </Form.Select>
                </Col>
                <Col md={2}>
                    <Form.Select
                        value={dateFilter}
                        onChange={(e) => setDateFilter(e.target.value)}
                    >
                        <option value="Today">Today</option>
                        <option value="Week">This Week</option>
                        <option value="Month">This Month</option>
                        <option value="All">All Time</option>
                    </Form.Select>
                </Col>
                <Col md={2}>
                    <Button variant="outline-secondary" className="w-100">
                        <i className="fas fa-download me-2"></i>Export
                    </Button>
                </Col>
            </Row>

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-list me-2"></i>Transaction History ({filteredTransactions.length})
                    </h5>
                </Card.Header>
                <Card.Body>
                    {filteredTransactions.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Transactions Found</h5>
                            <p className="text-muted">Process your first transaction to get started</p>
                            <Button variant="primary" onClick={() => handleNewTransaction('Deposit')}>
                                Process First Transaction
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Type</th>
                                    <th>Customer</th>
                                    <th>Account</th>
                                    <th>Amount</th>
                                    <th>Date & Time</th>
                                    <th>Status</th>
                                    <th>Processed By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredTransactions.map((transaction) => (
                                    <tr key={transaction.id}>
                                        <td>
                                            <strong>{transaction.reference}</strong>
                                        </td>
                                        <td>
                                            <Badge bg={getTypeBadge(transaction.type)}>
                                                {transaction.type}
                                            </Badge>
                                        </td>
                                        <td>{transaction.customer}</td>
                                        <td>{transaction.accountNumber}</td>
                                        <td>
                                            <strong className={transaction.type === 'Deposit' ? 'text-success' : 'text-danger'}>
                                                ${transaction.amount.toLocaleString()}
                                            </strong>
                                        </td>
                                        <td>{transaction.date}</td>
                                        <td>
                                            <Badge bg={getStatusBadge(transaction.status)}>
                                                {transaction.status}
                                            </Badge>
                                        </td>
                                        <td>{transaction.processedBy}</td>
                                        <td>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="outline-secondary" size="sm">
                                                    <i className="fas fa-ellipsis-v"></i>
                                                </Dropdown.Toggle>
                                                <Dropdown.Menu>
                                                    <Dropdown.Item onClick={() => handleViewTransaction(transaction)}>
                                                        <i className="fas fa-eye me-2"></i>View Details
                                                    </Dropdown.Item>
                                                    <Dropdown.Item>
                                                        <i className="fas fa-print me-2"></i>Print Receipt
                                                    </Dropdown.Item>
                                                    {transaction.status === 'Pending' && (
                                                        <>
                                                            <Dropdown.Divider />
                                                            <Dropdown.Item className="text-success">
                                                                <i className="fas fa-check me-2"></i>Approve
                                                            </Dropdown.Item>
                                                            <Dropdown.Item className="text-danger">
                                                                <i className="fas fa-times me-2"></i>Reject
                                                            </Dropdown.Item>
                                                        </>
                                                    )}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Transaction Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        {modalType === 'view' ? 'Transaction Details' : 
                         modalType === 'Deposit' ? 'New Deposit' :
                         modalType === 'Withdrawal' ? 'New Withdrawal' : 'New Transfer'}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {modalType === 'view' ? (
                        <div>
                            <Row>
                                <Col md={6}>
                                    <h6>Transaction Information</h6>
                                    <p><strong>Reference:</strong> {selectedTransaction?.reference}</p>
                                    <p><strong>Type:</strong> 
                                        <Badge bg={getTypeBadge(selectedTransaction?.type)} className="ms-2">
                                            {selectedTransaction?.type}
                                        </Badge>
                                    </p>
                                    <p><strong>Amount:</strong> ${selectedTransaction?.amount?.toLocaleString()}</p>
                                    <p><strong>Status:</strong> 
                                        <Badge bg={getStatusBadge(selectedTransaction?.status)} className="ms-2">
                                            {selectedTransaction?.status}
                                        </Badge>
                                    </p>
                                </Col>
                                <Col md={6}>
                                    <h6>Customer Information</h6>
                                    <p><strong>Customer:</strong> {selectedTransaction?.customer}</p>
                                    <p><strong>Account Number:</strong> {selectedTransaction?.accountNumber}</p>
                                    <p><strong>Date & Time:</strong> {selectedTransaction?.date}</p>
                                    <p><strong>Processed By:</strong> {selectedTransaction?.processedBy}</p>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={12}>
                                    <h6>Description</h6>
                                    <p>{selectedTransaction?.description}</p>
                                </Col>
                            </Row>
                        </div>
                    ) : (
                        <Form onSubmit={handleSubmitTransaction}>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Transaction Type</Form.Label>
                                        <Form.Control
                                            type="text"
                                            value={formData.type}
                                            readOnly
                                            style={{ backgroundColor: '#f8f9fa' }}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Reference Number</Form.Label>
                                        <Form.Control
                                            type="text"
                                            value={formData.reference}
                                            readOnly
                                            style={{ backgroundColor: '#f8f9fa' }}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Account Number *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="accountNumber"
                                            value={formData.accountNumber}
                                            onChange={handleInputChange}
                                            placeholder="Enter account number"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Customer Name *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="customerName"
                                            value={formData.customerName}
                                            onChange={handleInputChange}
                                            placeholder="Enter customer name"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Amount *</Form.Label>
                                        <InputGroup>
                                            <InputGroup.Text>$</InputGroup.Text>
                                            <Form.Control
                                                type="number"
                                                name="amount"
                                                value={formData.amount}
                                                onChange={handleInputChange}
                                                placeholder="0.00"
                                                min="0"
                                                step="0.01"
                                                required
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Description</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="description"
                                            value={formData.description}
                                            onChange={handleInputChange}
                                            placeholder="Enter transaction description"
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Form>
                    )}
                </Modal.Body>
                {modalType !== 'view' && (
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" onClick={handleSubmitTransaction} disabled={loading}>
                            {loading ? 'Processing...' : `Process ${formData.type}`}
                        </Button>
                    </Modal.Footer>
                )}
                {modalType === 'view' && (
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Close
                        </Button>
                        <Button variant="primary">
                            <i className="fas fa-print me-2"></i>Print Receipt
                        </Button>
                    </Modal.Footer>
                )}
            </Modal>
        </Container>
    );
};

export default TransactionManagement;
