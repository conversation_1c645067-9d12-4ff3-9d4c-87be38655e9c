/* =====================================================
   HEAD OFFICE DASHBOARD STYLES
   ===================================================== */

.head-office-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
}

/* =====================================================
   DASHBOARD CONTAINER
   ===================================================== */

.dashboard-container {
  display: flex;
  min-height: calc(100vh - 60px); /* Account for navbar height */
  margin-top: 60px; /* Navbar height */
}

/* =====================================================
   SIDEBAR STYLES
   ===================================================== */

.head-office-dashboard .sidebar {
  width: 280px;
  min-height: calc(100vh - 60px);
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 60px;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.head-office-dashboard .sidebar-header {
  padding: 25px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
}

.head-office-dashboard .sidebar-header h5 {
  margin: 0;
  font-weight: 700;
  color: #ecf0f1;
  font-size: 1.3rem;
}

.head-office-dashboard .sidebar-header small {
  color: #bdc3c7;
  font-size: 0.85rem;
}

.head-office-dashboard .sidebar-menu {
  padding: 20px 0;
}

.head-office-dashboard .menu-item {
  margin-bottom: 5px;
}

.head-office-dashboard .menu-link {
  padding: 15px 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  color: #ecf0f1;
  text-decoration: none;
}

.head-office-dashboard .menu-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #3498db;
  color: white;
  text-decoration: none;
}

.head-office-dashboard .menu-link.active {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: #e74c3c;
  color: white;
  font-weight: 600;
}

.head-office-dashboard .menu-group .menu-header {
  padding: 15px 25px;
  font-weight: 600;
  color: #bdc3c7;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 5px;
}

.head-office-dashboard .submenu {
  background: rgba(0, 0, 0, 0.1);
}

.head-office-dashboard .submenu-item {
  padding: 12px 25px 12px 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bdc3c7;
  font-size: 0.9rem;
  border-left: 3px solid transparent;
}

.head-office-dashboard .submenu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: #3498db;
}

.head-office-dashboard .submenu-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 600;
  border-left-color: #e74c3c;
}

/* =====================================================
   MAIN CONTENT STYLES
   ===================================================== */

.head-office-dashboard .main-content {
  flex: 1;
  margin-left: 280px;
  padding: 30px;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
}

/* =====================================================
   DASHBOARD CARDS
   ===================================================== */

.head-office-dashboard .dashboard-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 20px;
  overflow: hidden;
}

.head-office-dashboard .dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.head-office-dashboard .dashboard-card .card-body {
  padding: 25px;
}

.head-office-dashboard .dashboard-card h3 {
  font-weight: 700;
  margin-bottom: 5px;
  font-size: 2rem;
}

.head-office-dashboard .dashboard-card p {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
}

/* =====================================================
   PERFORMANCE METRICS
   ===================================================== */

.head-office-dashboard .performance-metrics {
  padding: 10px 0;
}

.head-office-dashboard .metric-item {
  padding: 10px 0;
}

.head-office-dashboard .metric-item span {
  font-size: 0.9rem;
  font-weight: 500;
}

.head-office-dashboard .progress {
  height: 8px;
  border-radius: 10px;
  background-color: #e9ecef;
}

.head-office-dashboard .progress-bar {
  border-radius: 10px;
  transition: width 0.6s ease;
}

/* =====================================================
   ACTIVITY ITEMS
   ===================================================== */

.head-office-dashboard .activity-item {
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.head-office-dashboard .activity-item:last-child {
  border-bottom: none;
}

.head-office-dashboard .activity-item small {
  font-size: 0.8rem;
  color: #6c757d;
}

.head-office-dashboard .activity-item div {
  font-size: 0.9rem;
  color: #495057;
  margin-top: 2px;
}

/* =====================================================
   CARD HEADERS
   ===================================================== */

.head-office-dashboard .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 20px;
  font-weight: 600;
}

.head-office-dashboard .card-header h5 {
  margin: 0;
  font-size: 1.1rem;
}

/* =====================================================
   BUTTONS
   ===================================================== */

.head-office-dashboard .btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.head-office-dashboard .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.head-office-dashboard .btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.head-office-dashboard .btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.head-office-dashboard .btn-outline-primary:hover {
  background: #667eea;
  border-color: #667eea;
  transform: translateY(-1px);
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
  .head-office-dashboard .sidebar {
    width: 100%;
    position: relative;
    min-height: auto;
  }
  
  .head-office-dashboard .main-content {
    margin-left: 0;
    padding: 20px 15px;
  }
  
  .dashboard-container {
    flex-direction: column;
  }
  
  .head-office-dashboard .dashboard-card h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .head-office-dashboard .sidebar-header {
    padding: 20px 15px;
  }
  
  .head-office-dashboard .menu-link,
  .head-office-dashboard .submenu-item {
    padding-left: 20px;
    font-size: 0.9rem;
  }
  
  .head-office-dashboard .submenu-item {
    padding-left: 40px;
  }
  
  .head-office-dashboard .main-content {
    padding: 15px 10px;
  }
}

/* =====================================================
   ANIMATIONS
   ===================================================== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.head-office-dashboard .dashboard-card {
  animation: fadeInUp 0.6s ease-out;
}

.head-office-dashboard .dashboard-card:nth-child(2) {
  animation-delay: 0.1s;
}

.head-office-dashboard .dashboard-card:nth-child(3) {
  animation-delay: 0.2s;
}

.head-office-dashboard .dashboard-card:nth-child(4) {
  animation-delay: 0.3s;
}
