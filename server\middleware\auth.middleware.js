import jwt from 'jsonwebtoken';
import { pool } from '../config/db.js';

// JWT secret (in production, this should be in environment variables)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Generate JWT token
export const generateToken = (user) => {
  return jwt.sign(
    {
      userId: user.id,
      username: user.username,
      role: user.role
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// Verify JWT token
export const verifyToken = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access denied. No token provided.'
    });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({
      success: false,
      error: 'Invalid token.'
    });
  }
};

// Role-based authorization middleware
export const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `Access denied. Required roles: ${allowedRoles.join(', ')}`
      });
    }

    next();
  };
};

// Dashboard redirect middleware
export const dashboardRedirect = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  const { role } = req.user;
  let redirectUrl;

  switch (role) {
    case 'admin':
      redirectUrl = '/admin/dashboard';
      break;
    case 'corporate':
      redirectUrl = '/corporate/dashboard';
      break;
    case 'headoffice':
      redirectUrl = '/headoffice/dashboard';
      break;
    case 'regional':
      redirectUrl = '/regional/dashboard';
      break;
    case 'branch':
      redirectUrl = '/branch/dashboard';
      break;
    case 'users':
      redirectUrl = '/user/dashboard';
      break;
    case 'partners':
      redirectUrl = '/partner/dashboard';
      break;
    default:
      redirectUrl = '/dashboard';
  }

  req.dashboardUrl = redirectUrl;
  next();
};

// Check if user can manage another user based on role hierarchy
export const canManageUser = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const managerRole = req.user.role;

    // Get the target user's role
    const userQuery = 'SELECT role FROM my_group_user WHERE id = $1';
    const result = await pool.query(userQuery, [userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const targetUserRole = result.rows[0].role;

    // Define role hierarchy
    const roleHierarchy = {
      admin: ['corporate', 'headoffice', 'regional', 'branch', 'users', 'partners'],
      corporate: ['headoffice', 'regional', 'branch', 'users'],
      headoffice: ['regional', 'branch', 'users'],
      regional: ['branch', 'users'],
      branch: ['users'],
      users: [],
      partners: []
    };

    // Check if manager can manage target user
    const canManage = roleHierarchy[managerRole]?.includes(targetUserRole) || managerRole === 'admin';

    if (!canManage) {
      return res.status(403).json({
        success: false,
        error: `${managerRole} cannot manage ${targetUserRole} users`
      });
    }

    next();
  } catch (error) {
    console.error('Can manage user middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};
