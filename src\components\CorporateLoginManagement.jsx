import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
// import AdvancedSearchFilter from './AdvancedSearchFilter';
// import BulkOperations from './BulkOperations';

const CorporateLoginManagement = () => {
    const { createUser, updateUser, deleteUser, resetUserPassword, databaseService } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState(''); // 'create', 'edit', 'resetPassword'
    const [editingUser, setEditingUser] = useState(null);

    // Form data
    const [formData, setFormData] = useState({
        name: '',
        mobileNumber: '',
        email: '',
        username: '',
        password: '',
        confirmPassword: ''
    });

    // Corporate users data
    const [corporateUsers, setCorporateUsers] = useState([]);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [selectedUsers, setSelectedUsers] = useState([]);

    useEffect(() => {
        fetchCorporateUsers();
    }, []);

    useEffect(() => {
        setFilteredUsers(corporateUsers);
    }, [corporateUsers]);

    const fetchCorporateUsers = async () => {
        setLoading(true);
        try {
            const users = await databaseService.getCorporateUsers();
            setCorporateUsers(users);
        } catch (error) {
            console.error('Error fetching corporate users:', error);
            setError('Failed to fetch corporate users');
        } finally {
            setLoading(false);
        }
    };

    const handleCreate = () => {
        setModalType('create');
        setEditingUser(null);
        setFormData({
            name: '',
            mobileNumber: '',
            email: '',
            username: '',
            password: '',
            confirmPassword: ''
        });
        setShowModal(true);
    };

    const handleEdit = (user) => {
        setModalType('edit');
        setEditingUser(user);
        setFormData({
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: ''
        });
        setShowModal(true);
    };

    const handleResetPassword = (user) => {
        setModalType('resetPassword');
        setEditingUser(user);
        setFormData({
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: ''
        });
        setShowModal(true);
    };

    const toggleUserStatus = async (userId) => {
        try {
            const user = corporateUsers.find(u => u.id === userId);
            const newStatus = user.status === 'Active' ? 'Inactive' : 'Active';

            await updateUser(userId, { status: newStatus }, 'CORPORATE');

            setCorporateUsers(corporateUsers.map(user =>
                user.id === userId
                    ? { ...user, status: newStatus }
                    : user
            ));
            setSuccess('User status updated successfully');
        } catch (error) {
            setError('Failed to update user status');
        }
    };

    const handleDelete = async (userId) => {
        if (window.confirm('Are you sure you want to delete this corporate user?')) {
            try {
                await deleteUser(userId, 'CORPORATE');
                setCorporateUsers(corporateUsers.filter(user => user.id !== userId));
                setSuccess('Corporate user deleted successfully');
            } catch (error) {
                setError('Failed to delete user');
            }
        }
    };

    const validateForm = () => {
        if (modalType === 'create' || modalType === 'resetPassword') {
            if (formData.password !== formData.confirmPassword) {
                setError('Passwords do not match');
                return false;
            }
            if (formData.password.length < 6) {
                setError('Password must be at least 6 characters long');
                return false;
            }
        }

        if (!formData.email.includes('@')) {
            setError('Please enter a valid email address');
            return false;
        }

        if (formData.mobileNumber.length < 10) {
            setError('Please enter a valid mobile number');
            return false;
        }

        return true;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            if (modalType === 'create') {
                const newUser = await createUser({
                    name: formData.name,
                    mobileNumber: formData.mobileNumber,
                    email: formData.email,
                    username: formData.username,
                    password: formData.password,
                    role: 'CORPORATE'
                });

                setCorporateUsers([...corporateUsers, newUser]);
                setSuccess('Corporate user created successfully and credentials sent via email');

            } else if (modalType === 'edit') {
                const updatedUser = await updateUser(editingUser.id, {
                    name: formData.name,
                    mobileNumber: formData.mobileNumber,
                    email: formData.email,
                    username: formData.username
                }, 'CORPORATE');

                setCorporateUsers(corporateUsers.map(user =>
                    user.id === editingUser.id ? updatedUser : user
                ));
                setSuccess('Corporate user updated successfully');

            } else if (modalType === 'resetPassword') {
                await resetUserPassword(editingUser.id, formData.password, 'CORPORATE');
                setSuccess('Password reset successfully. New credentials sent to email.');
            }

            setShowModal(false);
            setFormData({
                name: '',
                mobileNumber: '',
                email: '',
                username: '',
                password: '',
                confirmPassword: ''
            });
        } catch (error) {
            setError('Failed to save corporate user');
        } finally {
            setLoading(false);
        }
    };

    // Bulk operations handlers
    const handleBulkUpdate = async (userIds, updateData) => {
        try {
            const promises = userIds.map(userId =>
                updateUser(userId, updateData, 'CORPORATE')
            );
            await Promise.all(promises);

            setCorporateUsers(corporateUsers.map(user =>
                userIds.includes(user.id) ? { ...user, ...updateData } : user
            ));
            setSelectedUsers([]);
            setSuccess(`Successfully updated ${userIds.length} users`);
        } catch (error) {
            setError('Failed to update users: ' + error.message);
        }
    };

    const handleBulkDelete = async (userIds) => {
        try {
            const promises = userIds.map(userId =>
                deleteUser(userId, 'CORPORATE')
            );
            await Promise.all(promises);

            setCorporateUsers(corporateUsers.filter(user => !userIds.includes(user.id)));
            setSelectedUsers([]);
            setSuccess(`Successfully deleted ${userIds.length} users`);
        } catch (error) {
            setError('Failed to delete users: ' + error.message);
        }
    };

    const handleBulkExport = (data) => {
        const usersToExport = data === 'all' ? corporateUsers :
                             corporateUsers.filter(user => data.includes(user.id));

        const csvContent = [
            ['Name', 'Email', 'Username', 'Mobile', 'Status', 'Created At', 'Last Login'],
            ...usersToExport.map(user => [
                user.name,
                user.email,
                user.username,
                user.mobileNumber,
                user.status,
                user.createdAt,
                user.lastLogin
            ])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `corporate_users_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        setSuccess(`Exported ${usersToExport.length} users to CSV`);
    };

    const handleBulkImport = async (importData) => {
        try {
            const promises = importData.map(userData =>
                createUser({
                    ...userData,
                    password: 'TempPass123!', // Temporary password
                    role: 'CORPORATE'
                })
            );
            const newUsers = await Promise.all(promises);

            setCorporateUsers([...corporateUsers, ...newUsers]);
            setSuccess(`Successfully imported ${newUsers.length} users`);
        } catch (error) {
            setError('Failed to import users: ' + error.message);
        }
    };

    const handleSelectUser = (userId) => {
        setSelectedUsers(prev =>
            prev.includes(userId)
                ? prev.filter(id => id !== userId)
                : [...prev, userId]
        );
    };

    const handleSelectAll = () => {
        setSelectedUsers(
            selectedUsers.length === filteredUsers.length
                ? []
                : filteredUsers.map(user => user.id)
        );
    };

    const getModalTitle = () => {
        switch (modalType) {
            case 'create': return 'Create Corporate User';
            case 'edit': return 'Edit Corporate User';
            case 'resetPassword': return 'Reset Password';
            default: return 'Corporate User';
        }
    };

    // Advanced search configuration
    const searchFields = ['name', 'email', 'username', 'mobileNumber'];
    const filterFields = [
        {
            key: 'role',
            label: 'Role',
            options: [
                { value: 'CORPORATE', label: 'Corporate' },
                { value: 'ADMIN', label: 'Admin' }
            ]
        }
    ];
    const sortFields = [
        { key: 'name', label: 'Name' },
        { key: 'email', label: 'Email' },
        { key: 'createdAt', label: 'Created Date' },
        { key: 'lastLogin', label: 'Last Login' },
        { key: 'status', label: 'Status' }
    ];

    return (
        <Container fluid className="corporate-login-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Corporate Login Management</h4>
                <Button variant="primary" onClick={handleCreate}>
                    <i className="fas fa-plus me-2"></i>Create Corporate User
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{corporateUsers.length}</h3>
                            <p className="mb-0">Total Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {corporateUsers.filter(user => user.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                {selectedUsers.length}
                            </h3>
                            <p className="mb-0">Selected Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {filteredUsers.length}
                            </h3>
                            <p className="mb-0">Filtered Results</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Advanced Search & Filter */}
            <AdvancedSearchFilter
                data={corporateUsers}
                onFilteredData={setFilteredUsers}
                searchFields={searchFields}
                filterFields={filterFields}
                sortFields={sortFields}
                onExport={handleBulkExport}
            />

            {/* Bulk Operations */}
            <BulkOperations
                selectedItems={selectedUsers}
                onBulkUpdate={handleBulkUpdate}
                onBulkDelete={handleBulkDelete}
                onBulkExport={handleBulkExport}
                onBulkImport={handleBulkImport}
                itemType="corporate users"
            />

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-users me-2"></i>Corporate Users
                    </h5>
                </Card.Header>
                <Card.Body>
                    {corporateUsers.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Corporate Users</h5>
                            <p className="text-muted">Create your first corporate user to get started</p>
                            <Button variant="primary" onClick={handleCreate}>
                                Create First User
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>
                                        <Form.Check
                                            type="checkbox"
                                            checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                                            onChange={handleSelectAll}
                                        />
                                    </th>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Mobile Number</th>
                                    <th>Email</th>
                                    <th>Username</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Created Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredUsers.map((user, index) => (
                                    <tr key={user.id}>
                                        <td>
                                            <Form.Check
                                                type="checkbox"
                                                checked={selectedUsers.includes(user.id)}
                                                onChange={() => handleSelectUser(user.id)}
                                            />
                                        </td>
                                        <td>{index + 1}</td>
                                        <td>
                                            <strong>{user.name}</strong>
                                        </td>
                                        <td>{user.mobileNumber}</td>
                                        <td>{user.email}</td>
                                        <td>
                                            <Badge bg="secondary">{user.username}</Badge>
                                        </td>
                                        <td>
                                            <Badge 
                                                bg={user.status === 'Active' ? 'success' : 'danger'}
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => toggleUserStatus(user.id)}
                                            >
                                                {user.status}
                                            </Badge>
                                        </td>
                                        <td>
                                            <small className="text-muted">{user.lastLogin}</small>
                                        </td>
                                        <td>{user.createdAt}</td>
                                        <td>
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleEdit(user)}
                                                title="Edit User"
                                            >
                                                <i className="fas fa-edit"></i>
                                            </Button>
                                            <Button
                                                variant="outline-warning"
                                                size="sm"
                                                className="me-1"
                                                onClick={() => handleResetPassword(user)}
                                                title="Reset Password"
                                            >
                                                <i className="fas fa-key"></i>
                                            </Button>
                                            <Button
                                                variant="outline-danger"
                                                size="sm"
                                                onClick={() => handleDelete(user.id)}
                                                title="Delete User"
                                            >
                                                <i className="fas fa-trash"></i>
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Create/Edit/Reset Password Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-user me-2"></i>
                        {getModalTitle()}
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Full Name *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter full name"
                                        value={formData.name}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            name: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Mobile Number *</Form.Label>
                                    <Form.Control
                                        type="tel"
                                        placeholder="Enter mobile number"
                                        value={formData.mobileNumber}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            mobileNumber: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Email Address *</Form.Label>
                                    <Form.Control
                                        type="email"
                                        placeholder="Enter email address"
                                        value={formData.email}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            email: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Username *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter username"
                                        value={formData.username}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            username: e.target.value
                                        })}
                                        required
                                        disabled={modalType === 'resetPassword'}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        {(modalType === 'create' || modalType === 'resetPassword') && (
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>
                                            {modalType === 'resetPassword' ? 'New Password' : 'Password'} *
                                        </Form.Label>
                                        <Form.Control
                                            type="password"
                                            placeholder="Enter password"
                                            value={formData.password}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                password: e.target.value
                                            })}
                                            required
                                            minLength={6}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Confirm Password *</Form.Label>
                                        <Form.Control
                                            type="password"
                                            placeholder="Confirm password"
                                            value={formData.confirmPassword}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                confirmPassword: e.target.value
                                            })}
                                            required
                                            minLength={6}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        )}

                        <div className="bg-light p-3 rounded">
                            <small className="text-muted">
                                <i className="fas fa-info-circle me-1"></i>
                                <strong>Note:</strong> 
                                {modalType === 'create' && ' Login credentials will be sent to the provided email address.'}
                                {modalType === 'edit' && ' User information will be updated. Password remains unchanged.'}
                                {modalType === 'resetPassword' && ' New password will be sent to the user\'s email address.'}
                            </small>
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <i className={`fas ${modalType === 'create' ? 'fa-plus' : modalType === 'edit' ? 'fa-save' : 'fa-key'} me-2`}></i>
                                    {modalType === 'create' && 'Create User'}
                                    {modalType === 'edit' && 'Update User'}
                                    {modalType === 'resetPassword' && 'Reset Password'}
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default CorporateLoginManagement;
