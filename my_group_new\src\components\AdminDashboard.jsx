import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Nav, But<PERSON>, Table, Badge, Modal } from 'react-bootstrap';
import { 
  FaTachometerAlt, 
  FaUsers, 
  FaCog, 
  FaSignOutAlt,
  FaEdit,
  FaTrash,
  FaPlus,
  FaEye,
  FaBars,
  FaTimes
} from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import './AdminDashboard.css';

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalGroups: 0,
    totalCategories: 0
  });
  const [loading, setLoading] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  const API_BASE_URL = 'http://localhost:5000/api';

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch users
      const usersResponse = await fetch(`${API_BASE_URL}/users`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        setUsers(usersData.users || []);
        
        // Calculate stats
        const totalUsers = usersData.users?.length || 0;
        const activeUsers = usersData.users?.filter(u => u.status === 1).length || 0;
        
        setStats(prev => ({
          ...prev,
          totalUsers,
          activeUsers
        }));
      }

      // Fetch groups
      const groupsResponse = await fetch(`${API_BASE_URL}/groups/with-categories`);
      if (groupsResponse.ok) {
        const groupsData = await groupsResponse.json();
        const totalGroups = groupsData.groups?.length || 0;
        const totalCategories = groupsData.groups?.reduce((acc, group) => 
          acc + (group.categories?.length || 0), 0) || 0;
        
        setStats(prev => ({
          ...prev,
          totalGroups,
          totalCategories
        }));
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserStatusToggle = async (userId, currentStatus) => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${userId}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: currentStatus === 1 ? 0 : 1 })
      });

      if (response.ok) {
        fetchDashboardData(); // Refresh data
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  const handleViewUser = (user) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  const renderDashboard = () => (
    <div className="dashboard-content">
      <h2 className="mb-4">Dashboard Overview</h2>
      
      <Row className="mb-4">
        <Col md={3} sm={6} className="mb-3">
          <Card className="stat-card">
            <Card.Body className="text-center">
              <FaUsers className="stat-icon text-primary" />
              <h3 className="stat-number">{stats.totalUsers}</h3>
              <p className="stat-label">Total Users</p>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={3} sm={6} className="mb-3">
          <Card className="stat-card">
            <Card.Body className="text-center">
              <FaUsers className="stat-icon text-success" />
              <h3 className="stat-number">{stats.activeUsers}</h3>
              <p className="stat-label">Active Users</p>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={3} sm={6} className="mb-3">
          <Card className="stat-card">
            <Card.Body className="text-center">
              <FaTachometerAlt className="stat-icon text-info" />
              <h3 className="stat-number">{stats.totalGroups}</h3>
              <p className="stat-label">Total Groups</p>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={3} sm={6} className="mb-3">
          <Card className="stat-card">
            <Card.Body className="text-center">
              <FaCog className="stat-icon text-warning" />
              <h3 className="stat-number">{stats.totalCategories}</h3>
              <p className="stat-label">Categories</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Card>
        <Card.Header>
          <h5 className="mb-0">Recent Activity</h5>
        </Card.Header>
        <Card.Body>
          <p className="text-muted">Dashboard analytics and recent activity will be displayed here.</p>
        </Card.Body>
      </Card>
    </div>
  );

  const renderUserManagement = () => (
    <div className="user-management">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>User Management</h2>
        <Button variant="primary">
          <FaPlus className="me-2" />
          Add User
        </Button>
      </div>

      <Card>
        <Card.Body>
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Last Login</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map(user => (
                  <tr key={user.id}>
                    <td>{user.id}</td>
                    <td>{user.user_name}</td>
                    <td>{user.email}</td>
                    <td>{user.role || 'Customer'}</td>
                    <td>
                      <Badge bg={user.status === 1 ? 'success' : 'danger'}>
                        {user.status === 1 ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td>
                      {user.last_logged_in ? 
                        new Date(user.last_logged_in).toLocaleDateString() : 
                        'Never'
                      }
                    </td>
                    <td>
                      <Button 
                        variant="outline-primary" 
                        size="sm" 
                        className="me-2"
                        onClick={() => handleViewUser(user)}
                      >
                        <FaEye />
                      </Button>
                      <Button 
                        variant="outline-secondary" 
                        size="sm" 
                        className="me-2"
                      >
                        <FaEdit />
                      </Button>
                      <Button 
                        variant={user.status === 1 ? 'outline-warning' : 'outline-success'} 
                        size="sm"
                        onClick={() => handleUserStatusToggle(user.id, user.status)}
                      >
                        {user.status === 1 ? 'Deactivate' : 'Activate'}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>
    </div>
  );

  const renderSettings = () => (
    <div className="settings-content">
      <h2 className="mb-4">Settings</h2>
      
      <Card>
        <Card.Header>
          <h5 className="mb-0">Application Settings</h5>
        </Card.Header>
        <Card.Body>
          <p className="text-muted">Application configuration and settings will be available here.</p>
        </Card.Body>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'users':
        return renderUserManagement();
      case 'settings':
        return renderSettings();
      default:
        return renderDashboard();
    }
  };

  return (
    <div className="admin-dashboard">
      {/* Mobile Header */}
      <div className="mobile-header d-md-none">
        <Button 
          variant="link" 
          className="sidebar-toggle"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          {sidebarOpen ? <FaTimes /> : <FaBars />}
        </Button>
        <h4 className="mb-0">Admin Dashboard</h4>
        <div></div>
      </div>

      {/* Sidebar */}
      <div className={`sidebar ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <div className="sidebar-header">
          <h4>Admin Panel</h4>
          <p className="text-muted">Welcome, {user?.user_name}</p>
        </div>
        
        <Nav className="flex-column sidebar-nav">
          <Nav.Link 
            className={activeTab === 'dashboard' ? 'active' : ''}
            onClick={() => {
              setActiveTab('dashboard');
              setSidebarOpen(false);
            }}
          >
            <FaTachometerAlt className="me-2" />
            Dashboard
          </Nav.Link>
          
          <Nav.Link 
            className={activeTab === 'users' ? 'active' : ''}
            onClick={() => {
              setActiveTab('users');
              setSidebarOpen(false);
            }}
          >
            <FaUsers className="me-2" />
            User Management
          </Nav.Link>
          
          <Nav.Link 
            className={activeTab === 'settings' ? 'active' : ''}
            onClick={() => {
              setActiveTab('settings');
              setSidebarOpen(false);
            }}
          >
            <FaCog className="me-2" />
            Settings
          </Nav.Link>
        </Nav>
        
        <div className="sidebar-footer">
          <Button 
            variant="outline-danger" 
            className="w-100"
            onClick={logout}
          >
            <FaSignOutAlt className="me-2" />
            Logout
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content">
        <Container fluid>
          {renderContent()}
        </Container>
      </div>

      {/* User Details Modal */}
      <Modal show={showUserModal} onHide={() => setShowUserModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>User Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedUser && (
            <Row>
              <Col md={6}>
                <p><strong>ID:</strong> {selectedUser.id}</p>
                <p><strong>Username:</strong> {selectedUser.user_name}</p>
                <p><strong>Email:</strong> {selectedUser.email}</p>
                <p><strong>Mobile:</strong> {selectedUser.mobile_number}</p>
              </Col>
              <Col md={6}>
                <p><strong>Role:</strong> {selectedUser.role || 'Customer'}</p>
                <p><strong>Status:</strong> 
                  <Badge bg={selectedUser.status === 1 ? 'success' : 'danger'} className="ms-2">
                    {selectedUser.status === 1 ? 'Active' : 'Inactive'}
                  </Badge>
                </p>
                <p><strong>Created:</strong> {new Date(selectedUser.created_on).toLocaleDateString()}</p>
                <p><strong>Last Login:</strong> {selectedUser.last_logged_in ? 
                  new Date(selectedUser.last_logged_in).toLocaleDateString() : 'Never'}</p>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowUserModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AdminDashboard;
