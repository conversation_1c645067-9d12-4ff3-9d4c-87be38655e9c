const express = require('express');
const router = express.Router();
const groupController = require('../controllers/groupController');
const { verifyToken, requireRole, ROLES } = require('../middleware/auth');

// Public routes
router.get('/', groupController.getAllGroups);
router.get('/with-categories', groupController.getAllGroupsWithCategories);
router.get('/:id', groupController.getGroupById);
router.get('/:id/categories', groupController.getGroupCategories);

// Protected routes - require authentication and admin role
router.use(verifyToken);
router.use(requireRole([ROLES.ADMIN, ROLES.APP_ADMIN]));

router.post('/', groupController.createGroup);
router.put('/:id', groupController.updateGroup);
router.delete('/:id', groupController.deleteGroup);

module.exports = router;
