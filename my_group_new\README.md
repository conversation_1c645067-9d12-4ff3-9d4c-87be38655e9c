# My Group New - Mobile-First React Application

A comprehensive multi-role dashboard system with mobile-first design, featuring user authentication, profile management, and group data integration.

## Features

### 🏠 Home Screen
- **Mobile-First Design**: Optimized for mobile devices with responsive layout
- **Desktop Restriction**: Shows "not available" message on desktop browsers
- **Dynamic Header**: Two-row header with group data integration
- **Theme Toggle**: Light/dark theme support

### 🔐 Authentication System
- **Login Modal**: Popup login with validation
- **Registration**: Complete user registration with profile details
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access**: Support for multiple user roles

### 👤 Profile Management
- **View Profile**: Complete user profile display
- **Edit Profile**: Update personal information
- **Settings**: Account management options
- **Logout**: Secure session termination

### 🏢 Group Data Integration
- **Dynamic Groups**: Fetch and display group data from database
- **Categories**: Group categories with icons and navigation
- **Horizontal Display**: Scrollable group data in header
- **Interactive Cards**: Clickable category cards

## Tech Stack

### Frontend
- **React 18**: Modern React with hooks
- **React Bootstrap**: UI components and responsive grid
- **React Router**: Client-side routing
- **React Icons**: Icon library
- **Vite**: Fast build tool and dev server

### Backend
- **Node.js**: JavaScript runtime
- **Express.js**: Web framework
- **PostgreSQL**: Database
- **JWT**: Authentication tokens
- **bcryptjs**: Password hashing
- **CORS**: Cross-origin resource sharing

## Project Structure

```
my_group_new/
├── backend/                 # Backend server
│   ├── config/             # Database configuration
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Authentication middleware
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── .env               # Environment variables
│   ├── package.json       # Backend dependencies
│   └── server.js          # Main server file
├── src/                   # Frontend source
│   ├── components/        # React components
│   │   ├── Header.jsx     # Main header component
│   │   ├── HomeScreen.jsx # Home screen
│   │   ├── LoginModal.jsx # Login modal
│   │   ├── Login.jsx      # Login form
│   │   ├── Register.jsx   # Registration form
│   │   ├── ProfileModal.jsx # Profile modal
│   │   └── GroupDataDisplay.jsx # Group data display
│   ├── contexts/          # React contexts
│   │   └── AuthContext.jsx # Authentication context
│   ├── App.jsx           # Main app component
│   ├── main.jsx          # App entry point
│   └── index.css         # Global styles
├── public/               # Static assets
├── package.json          # Frontend dependencies
└── vite.config.js        # Vite configuration
```

## Database Schema

The application uses PostgreSQL with the following main tables:

- **my_users**: User authentication data
- **my_user_details**: Extended user profile information
- **my_group_data**: Group information and branding
- **my_group_category**: Group categories and navigation
- **my_role**: User roles and permissions

## Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL database
- npm or yarn package manager

### 1. Clone and Install Dependencies

```bash
# Install frontend dependencies
cd my_group_new
npm install

# Install backend dependencies
cd backend
npm install
```

### 2. Database Setup

1. Create PostgreSQL database named `my_group_db`
2. Run the schema from `../database/schema_new.sql`
3. Update database credentials in `backend/.env`

### 3. Environment Configuration

Update `backend/.env` with your settings:

```env
# Server Configuration
PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=my_group_db
DB_USER=postgres
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
```

### 4. Start the Application

```bash
# Start backend server (from backend directory)
npm run dev

# Start frontend development server (from root directory)
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/profile` - Get user profile
- `GET /api/auth/verify` - Verify JWT token

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/profile` - Update current user profile
- `PATCH /api/users/:id/status` - Update user status (admin only)

### Groups
- `GET /api/groups` - Get all groups
- `GET /api/groups/with-categories` - Get groups with categories
- `GET /api/groups/:id` - Get group by ID
- `GET /api/groups/:id/categories` - Get group categories

## Mobile-First Design

The application is specifically designed for mobile devices:

- **Responsive Layout**: Optimized for mobile screens
- **Touch-Friendly**: Large touch targets and intuitive gestures
- **Mobile Navigation**: Hamburger menu and mobile-optimized header
- **Desktop Restriction**: Displays message on desktop browsers
- **Progressive Enhancement**: Works on various mobile devices

## User Roles

The system supports multiple user roles:

1. **Admin** (role_id: 1) - Full system access
2. **Corporate** (role_id: 2) - Corporate level access
3. **Head Office** (role_id: 3) - Head office management
4. **Regional** (role_id: 4) - Regional management
5. **Branch** (role_id: 5) - Branch level access
6. **Customer** (role_id: 6) - End user access
7. **Partner** (role_id: 7) - Partner access
8. **App Admin** (role_id: 8) - Application administration

## Development

### Available Scripts

Frontend:
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

Backend:
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon

### Code Style

- Use functional components with hooks
- Follow React best practices
- Use Bootstrap classes for styling
- Implement proper error handling
- Add loading states for async operations

## Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include responsive design considerations
4. Test on mobile devices
5. Update documentation as needed

## License

This project is licensed under the ISC License.
