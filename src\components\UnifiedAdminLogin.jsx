import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import authAPI from '../api/authAPI';
import '../styles/UnifiedAdminLogin.css';

const UnifiedAdminLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  
  const navigate = useNavigate();
  const location = useLocation();
  const { login, currentUser } = useAuth();

  // Available roles for login
  const availableRoles = [
    { id: 'admin', name: 'System Administrator', icon: '👨‍💼', color: '#dc2626' },
    { id: 'app_admin', name: 'Application Administrator', icon: '⚙️', color: '#7c3aed' },
    { id: 'corporate', name: 'Corporate User', icon: '🏢', color: '#1e40af' },
    { id: 'headoffice', name: 'Head Office', icon: '🏛️', color: '#059669' },
    { id: 'regional', name: 'Regional Office', icon: '🌍', color: '#ea580c' },
    { id: 'branch', name: 'Branch Office', icon: '🏪', color: '#7c2d12' },
    { id: 'partner', name: 'Partner', icon: '🤝', color: '#0891b2' },
    { id: 'customer', name: 'Customer', icon: '👤', color: '#6b7280' }
  ];

  // Redirect if already logged in
  useEffect(() => {
    if (currentUser) {
      redirectToDashboard(currentUser.role);
    }
  }, [currentUser]);

  const redirectToDashboard = (role) => {
    const dashboardRoutes = {
      'admin': '/admin/dashboard',
      'app_admin': '/admin/dashboard',
      'corporate': '/corporate/dashboard',
      'headoffice': '/headoffice/dashboard',
      'regional': '/regional/dashboard',
      'branch': '/branch/dashboard',
      'partner': '/partner/dashboard',
      'customer': '/customer/dashboard'
    };

    const redirectTo = dashboardRoutes[role] || '/dashboard';
    navigate(redirectTo, { replace: true });
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setError(''); // Clear error when user types
  };

  const handleRoleSelect = (roleId) => {
    setSelectedRole(roleId);
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.username.trim()) {
        throw new Error('Username is required');
      }
      if (!formData.password) {
        throw new Error('Password is required');
      }

      // Attempt login
      const result = await authAPI.login(
        formData.username.trim(),
        formData.password,
        navigator.userAgent,
        '127.0.0.1' // In real app, get actual IP
      );

      if (result.success) {
        // Check if selected role matches user's actual role (if role was pre-selected)
        if (selectedRole && selectedRole !== result.user.role) {
          throw new Error(`Access denied. This account is not authorized for ${selectedRole} role.`);
        }

        // Save remember me preference
        if (formData.rememberMe) {
          localStorage.setItem('rememberMe', 'true');
          localStorage.setItem('savedUsername', formData.username);
        } else {
          localStorage.removeItem('rememberMe');
          localStorage.removeItem('savedUsername');
        }

        // Use auth context login
        await login(formData.username, formData.password);
        
        // Redirect to appropriate dashboard
        redirectToDashboard(result.user.role);
      } else {
        throw new Error(result.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'An error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  // Load saved username if remember me was checked
  useEffect(() => {
    const rememberMe = localStorage.getItem('rememberMe');
    const savedUsername = localStorage.getItem('savedUsername');
    
    if (rememberMe === 'true' && savedUsername) {
      setFormData(prev => ({
        ...prev,
        username: savedUsername,
        rememberMe: true
      }));
    }
  }, []);

  return (
    <div className="unified-login-container">
      <div className="login-background">
        <div className="background-pattern"></div>
      </div>
      
      <div className="login-content">
        <div className="login-card">
          <div className="login-header">
            <div className="logo-section">
              <div className="logo">
                <span className="logo-icon">🏢</span>
                <span className="logo-text">MyGroup</span>
              </div>
              <h1>Admin Portal</h1>
              <p>Unified login for all user roles</p>
            </div>
          </div>

          {/* Role Selection (Optional) */}
          {!selectedRole && (
            <div className="role-selection">
              <h3>Select Your Role (Optional)</h3>
              <div className="role-grid">
                {availableRoles.map(role => (
                  <button
                    key={role.id}
                    type="button"
                    className="role-card"
                    onClick={() => handleRoleSelect(role.id)}
                    style={{ borderColor: role.color }}
                  >
                    <span className="role-icon">{role.icon}</span>
                    <span className="role-name">{role.name}</span>
                  </button>
                ))}
              </div>
              <p className="role-note">
                You can also login directly without selecting a role. 
                The system will automatically detect your role.
              </p>
            </div>
          )}

          {/* Selected Role Display */}
          {selectedRole && (
            <div className="selected-role">
              <div className="selected-role-info">
                <span className="selected-role-icon">
                  {availableRoles.find(r => r.id === selectedRole)?.icon}
                </span>
                <span className="selected-role-name">
                  {availableRoles.find(r => r.id === selectedRole)?.name}
                </span>
                <button 
                  type="button" 
                  className="change-role-btn"
                  onClick={() => setSelectedRole('')}
                >
                  Change
                </button>
              </div>
            </div>
          )}

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="login-form">
            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="username">Username or Email</label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter your username or email"
                required
                disabled={loading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                required
                disabled={loading}
              />
            </div>

            <div className="form-options">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  disabled={loading}
                />
                <span className="checkbox-custom"></span>
                Remember me
              </label>
              
              <button type="button" className="forgot-password-link">
                Forgot Password?
              </button>
            </div>

            <button 
              type="submit" 
              className="login-button"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="loading-spinner"></span>
                  Signing in...
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          <div className="login-footer">
            <p>
              Need help? <a href="/support">Contact Support</a>
            </p>
            <div className="system-info">
              <small>MyGroup Admin Portal v2.0</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedAdminLogin;
