import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Bad<PERSON>, Button, Dropdown, ListGroup } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const NotificationSystem = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const { currentUser } = useAuth();

  // Sample notifications - in a real app, these would come from an API
  useEffect(() => {
    const sampleNotifications = [
      {
        id: 1,
        title: 'Welcome to MyGroup',
        message: 'Thank you for joining our platform!',
        type: 'info',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        read: false
      },
      {
        id: 2,
        title: 'System Update',
        message: 'New features have been added to your dashboard.',
        type: 'success',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        read: false
      },
      {
        id: 3,
        title: 'Security Alert',
        message: 'Please review your account security settings.',
        type: 'warning',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        read: true
      }
    ];

    setNotifications(sampleNotifications);
    setUnreadCount(sampleNotifications.filter(n => !n.read).length);
  }, []);

  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  const deleteNotification = (notificationId) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${days}d ago`;
    }
  };

  const getVariantByType = (type) => {
    switch (type) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'danger';
      default: return 'info';
    }
  };

  return (
    <div className="notification-system">
      <Dropdown 
        show={showDropdown} 
        onToggle={setShowDropdown}
        align="end"
      >
        <Dropdown.Toggle 
          variant="outline-secondary" 
          id="notification-dropdown"
          className="position-relative"
        >
          🔔
          {unreadCount > 0 && (
            <Badge 
              bg="danger" 
              className="position-absolute top-0 start-100 translate-middle"
              style={{ fontSize: '0.7rem' }}
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Dropdown.Toggle>

        <Dropdown.Menu style={{ width: '350px', maxHeight: '400px', overflowY: 'auto' }}>
          <div className="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
            <h6 className="mb-0">Notifications</h6>
            {unreadCount > 0 && (
              <Button 
                variant="link" 
                size="sm" 
                className="p-0"
                onClick={markAllAsRead}
              >
                Mark all as read
              </Button>
            )}
          </div>

          {notifications.length === 0 ? (
            <div className="text-center py-4 text-muted">
              No notifications
            </div>
          ) : (
            <ListGroup variant="flush">
              {notifications.map(notification => (
                <ListGroup.Item 
                  key={notification.id}
                  className={`border-0 ${!notification.read ? 'bg-light' : ''}`}
                >
                  <div className="d-flex justify-content-between align-items-start">
                    <div className="flex-grow-1">
                      <div className="d-flex align-items-center mb-1">
                        <strong className="me-2">{notification.title}</strong>
                        {!notification.read && (
                          <Badge bg="primary" className="ms-auto">New</Badge>
                        )}
                      </div>
                      <p className="mb-1 small text-muted">
                        {notification.message}
                      </p>
                      <small className="text-muted">
                        {formatTimestamp(notification.timestamp)}
                      </small>
                    </div>
                    <Dropdown align="end">
                      <Dropdown.Toggle 
                        variant="link" 
                        size="sm" 
                        className="p-0 text-muted"
                      >
                        ⋮
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        {!notification.read && (
                          <Dropdown.Item 
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </Dropdown.Item>
                        )}
                        <Dropdown.Item 
                          onClick={() => deleteNotification(notification.id)}
                          className="text-danger"
                        >
                          Delete
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    </Dropdown>
                  </div>
                </ListGroup.Item>
              ))}
            </ListGroup>
          )}

          {notifications.length > 0 && (
            <div className="text-center py-2 border-top">
              <Button variant="link" size="sm">
                View all notifications
              </Button>
            </div>
          )}
        </Dropdown.Menu>
      </Dropdown>
    </div>
  );
};

export default NotificationSystem;
