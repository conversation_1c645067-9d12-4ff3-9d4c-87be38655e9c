-- Migration: Create my_regional_offices table
-- Description: Stores regional office information under head offices
-- Created: 2024-01-18

CREATE TABLE my_regional_offices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    head_office_id INT NOT NULL,
    region_name VARCHAR(255) NOT NULL,
    region_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (head_office_id) REFERENCES my_head_offices(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_regional_offices_code ON my_regional_offices(region_code);
CREATE INDEX idx_regional_offices_head_office ON my_regional_offices(head_office_id);
CREATE INDEX idx_regional_offices_active ON my_regional_offices(is_active);
CREATE INDEX idx_regional_offices_name ON my_regional_offices(region_name);
