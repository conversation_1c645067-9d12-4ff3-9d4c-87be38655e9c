# Admin Dashboard API Endpoints Documentation

## Base URL
```
http://localhost:3001/api
```

## Authentication
All endpoints require authentication. Include the user token in the Authorization header:
```
Authorization: Bearer <token>
```

---

## 1. Profile Groups API

### Get All Profile Groups
```http
GET /api/profile-groups
```

### Create Profile Group
```http
POST /api/profile-groups
Content-Type: application/json

{
  "groupType": "My Apps",
  "groupName": "Mobile Applications",
  "iconUrl": "/images/mobile-icon.png",
  "logoUrl": "/images/mobile-logo.png",
  "nameImageUrl": "/images/mobile-name.png",
  "backgroundColor": "#007bff"
}
```

### Update Profile Group
```http
PUT /api/profile-groups/:id
Content-Type: application/json

{
  "groupType": "My Apps",
  "groupName": "Updated Mobile Applications",
  "backgroundColor": "#28a745"
}
```

### Delete Profile Group
```http
DELETE /api/profile-groups/:id
```

---

## 2. Languages API

### Get All Languages
```http
GET /api/languages
```

### Get Languages by Country
```http
GET /api/languages/country/:countryId
```

### Create Language Entry
```http
POST /api/languages
Content-Type: application/json

{
  "countryId": 1,
  "language1": "Hindi",
  "language2": "English",
  "description": "Primary languages spoken in India"
}
```

### Update Language Entry
```http
PUT /api/languages/:id
Content-Type: application/json

{
  "language1": "Hindi",
  "language2": "English",
  "description": "Updated description"
}
```

### Delete Language Entry
```http
DELETE /api/languages/:id
```

---

## 3. Education Levels API

### Get All Education Levels
```http
GET /api/education-levels
```

### Get Education Levels by Category
```http
GET /api/education-levels/category/:category
```

### Create Education Level
```http
POST /api/education-levels
Content-Type: application/json

{
  "educationLevel": "Bachelor's Degree",
  "description": "Four-year undergraduate degree",
  "category": "Undergraduate",
  "displayOrder": 7
}
```

### Update Education Level
```http
PUT /api/education-levels/:id
Content-Type: application/json

{
  "educationLevel": "Updated Bachelor's Degree",
  "description": "Updated description",
  "status": "Active"
}
```

### Toggle Education Level Status
```http
PATCH /api/education-levels/:id/status
Content-Type: application/json

{
  "status": "Inactive"
}
```

### Delete Education Level
```http
DELETE /api/education-levels/:id
```

---

## 4. Professions API

### Get All Professions
```http
GET /api/professions
```

### Get Professions by Category
```http
GET /api/professions/category/:category
```

### Get Professions by Industry
```http
GET /api/professions/industry/:industry
```

### Create Profession
```http
POST /api/professions
Content-Type: application/json

{
  "professionName": "Software Engineer",
  "description": "Develops and maintains software applications",
  "category": "Technology",
  "industry": "Information Technology",
  "displayOrder": 1
}
```

### Update Profession
```http
PUT /api/professions/:id
Content-Type: application/json

{
  "professionName": "Senior Software Engineer",
  "description": "Updated description",
  "status": "Active"
}
```

### Toggle Profession Status
```http
PATCH /api/professions/:id/status
Content-Type: application/json

{
  "status": "Inactive"
}
```

### Delete Profession
```http
DELETE /api/professions/:id
```

---

## 5. Categories API (Hierarchical)

### Get Category Hierarchy by Group
```http
GET /api/categories/group/:groupId
```

### Get All Main Categories
```http
GET /api/categories/main
```

### Create Main Category
```http
POST /api/categories/main
Content-Type: application/json

{
  "groupId": 1,
  "name": "Mobile Development",
  "description": "Mobile app development services",
  "iconClass": "fas fa-mobile-alt",
  "displayOrder": 1
}
```

### Create Level 1 Category
```http
POST /api/categories/level1
Content-Type: application/json

{
  "mainCategoryId": 1,
  "name": "iOS Development",
  "description": "iPhone and iPad app development",
  "iconClass": "fab fa-apple",
  "displayOrder": 1
}
```

### Create Level 2 Category
```http
POST /api/categories/level2
Content-Type: application/json

{
  "level1CategoryId": 1,
  "name": "Native iOS",
  "description": "Swift and Objective-C development",
  "iconClass": "fas fa-code",
  "displayOrder": 1
}
```

### Create Level 3 Category
```http
POST /api/categories/level3
Content-Type: application/json

{
  "level2CategoryId": 1,
  "name": "Swift Development",
  "description": "Modern Swift programming",
  "displayOrder": 1
}
```

### Update Category (any level)
```http
PUT /api/categories/:level/:id
Content-Type: application/json

{
  "name": "Updated Category Name",
  "description": "Updated description"
}
```

### Delete Category (any level)
```http
DELETE /api/categories/:level/:id
```

---

## 6. Corporate Users API

### Get All Corporate Users
```http
GET /api/corporate-users
```

### Get Corporate User by ID
```http
GET /api/corporate-users/:id
```

### Create Corporate User
```http
POST /api/corporate-users
Content-Type: application/json

{
  "fullName": "John Corporate",
  "mobileNumber": "******-0101",
  "emailId": "<EMAIL>",
  "username": "johncorp",
  "password": "securePassword123",
  "role": "CORPORATE"
}
```

### Update Corporate User
```http
PUT /api/corporate-users/:id
Content-Type: application/json

{
  "fullName": "John Updated Corporate",
  "mobileNumber": "******-0101",
  "emailId": "<EMAIL>"
}
```

### Reset Password
```http
POST /api/corporate-users/:id/reset-password
Content-Type: application/json

{
  "newPassword": "newSecurePassword123"
}
```

### Toggle User Status
```http
PATCH /api/corporate-users/:id/status
Content-Type: application/json

{
  "status": "Inactive"
}
```

### Delete Corporate User
```http
DELETE /api/corporate-users/:id
```

---

## 7. Dashboard Statistics API

### Get Dashboard Stats
```http
GET /api/dashboard/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalGroups": 4,
    "totalCountries": 195,
    "totalLanguages": 12,
    "totalCorporateUsers": 8,
    "totalEducationLevels": 15,
    "totalProfessions": 25,
    "totalCategories": 18,
    "activeUsers": 156,
    "recentActivities": [
      {
        "action": "New corporate user created",
        "timestamp": "2024-01-20T10:30:00Z"
      }
    ]
  }
}
```

---

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `422` - Validation Error
- `500` - Internal Server Error

---

## File Upload Endpoints

### Upload Profile Group Images
```http
POST /api/upload/profile-group-images
Content-Type: multipart/form-data

Form Data:
- icon: File
- logo: File
- nameImage: File
```

**Response:**
```json
{
  "success": true,
  "data": {
    "iconUrl": "/uploads/icons/filename.png",
    "logoUrl": "/uploads/logos/filename.png",
    "nameImageUrl": "/uploads/names/filename.png"
  }
}
```
