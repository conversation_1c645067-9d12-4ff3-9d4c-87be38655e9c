import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, InputGroup } from 'react-bootstrap';
import { FaEye, FaEyeSlash, FaEnvelope, FaLock } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import './Login.css';

const Login = ({ onSwitchToRegister, onClose }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [validated, setValidated] = useState(false);
  
  const { login } = useAuth();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const validateForm = () => {
    const { email, password } = formData;
    
    if (!email || !password) {
      setError('Please fill in all fields');
      return false;
    }
    
    if (!email.includes('@')) {
      setError('Please enter a valid email address');
      return false;
    }
    
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    setValidated(true);
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await login(formData.email, formData.password);
      onClose && onClose();
    } catch (err) {
      setError(err.message || 'Failed to login. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-form">
      <div className="text-center mb-4">
        <h5 className="mb-2">Sign In to Your Account</h5>
        <p className="text-muted small">Enter your credentials to continue</p>
      </div>
      
      {error && (
        <Alert variant="danger" className="py-2">
          <small>{error}</small>
        </Alert>
      )}
      
      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Form.Group className="mb-3">
          <Form.Label className="small fw-semibold">Email Address</Form.Label>
          <InputGroup>
            <InputGroup.Text>
              <FaEnvelope className="text-muted" />
            </InputGroup.Text>
            <Form.Control
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              required
              isInvalid={validated && !formData.email}
            />
            <Form.Control.Feedback type="invalid">
              Please provide a valid email.
            </Form.Control.Feedback>
          </InputGroup>
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label className="small fw-semibold">Password</Form.Label>
          <InputGroup>
            <InputGroup.Text>
              <FaLock className="text-muted" />
            </InputGroup.Text>
            <Form.Control
              type={showPassword ? 'text' : 'password'}
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter your password"
              required
              isInvalid={validated && !formData.password}
            />
            <Button
              variant="outline-secondary"
              onClick={() => setShowPassword(!showPassword)}
              className="password-toggle"
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </Button>
            <Form.Control.Feedback type="invalid">
              Please provide a password.
            </Form.Control.Feedback>
          </InputGroup>
        </Form.Group>

        <div className="d-flex justify-content-between align-items-center mb-3">
          <Form.Check 
            type="checkbox" 
            id="remember-me"
            label="Remember me"
            className="small"
          />
          <Button variant="link" className="p-0 small text-decoration-none">
            Forgot password?
          </Button>
        </div>

        <Button 
          variant="primary" 
          type="submit" 
          className="w-100 mb-3 py-2"
          disabled={loading}
        >
          {loading ? (
            <>
              <Spinner animation="border" size="sm" className="me-2" />
              Signing in...
            </>
          ) : (
            'Sign In'
          )}
        </Button>
      </Form>

      <div className="text-center">
        <p className="mb-0 small text-muted">
          Don't have an account?{' '}
          <Button 
            variant="link" 
            className="p-0 small text-decoration-none fw-semibold"
            onClick={onSwitchToRegister}
          >
            Create one here
          </Button>
        </p>
      </div>
    </div>
  );
};

export default Login;
