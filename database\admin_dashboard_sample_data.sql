-- =====================================================
-- ADMIN DASHBOARD SAMPLE DATA
-- =====================================================

-- Clear existing data
TRUNCATE TABLE category_level3, category_level2, category_level1, main_categories, 
               professions, education_levels, languages, profile_groups, corporate_users RESTART IDENTITY CASCADE;

-- =====================================================
-- 1. PROFILE GROUPS SAMPLE DATA
-- =====================================================
INSERT INTO profile_groups (group_type, group_name, icon_url, logo_url, name_image_url, background_color, display_order) VALUES
('My Apps', 'Mobile Applications', '/images/mobile-icon.png', '/images/mobile-logo.png', '/images/mobile-name.png', '#007bff', 1),
('My Company', 'Corporate Solutions', '/images/corporate-icon.png', '/images/corporate-logo.png', '/images/corporate-name.png', '#28a745', 2),
('My Online Apps', 'Web Services', '/images/web-icon.png', '/images/web-logo.png', '/images/web-name.png', '#17a2b8', 3),
('My Offline Apps', 'Desktop Applications', '/images/desktop-icon.png', '/images/desktop-logo.png', '/images/desktop-name.png', '#ffc107', 4);

-- =====================================================
-- 2. LANGUAGES SAMPLE DATA
-- =====================================================
INSERT INTO languages (country_id, language_1, language_2, description) VALUES
(1, 'Hindi', 'English', 'Primary languages spoken in India'),
(2, 'English', 'Spanish', 'Primary languages in United States'),
(3, 'English', 'Welsh', 'Primary languages in United Kingdom'),
(4, 'English', 'French', 'Primary languages in Canada'),
(5, 'English', 'Aboriginal Languages', 'Primary languages in Australia'),
(6, 'Mandarin', 'Cantonese', 'Primary languages in China'),
(7, 'Japanese', 'English', 'Primary languages in Japan'),
(8, 'German', 'English', 'Primary languages in Germany'),
(9, 'French', 'English', 'Primary languages in France'),
(10, 'Spanish', 'Catalan', 'Primary languages in Spain');

-- =====================================================
-- 3. EDUCATION LEVELS SAMPLE DATA
-- =====================================================
INSERT INTO education_levels (education_level, description, category, display_order) VALUES
('Primary School', 'Elementary education (Grades 1-5)', 'Primary', 1),
('Middle School', 'Middle level education (Grades 6-8)', 'Secondary', 2),
('High School', 'Secondary education completion (Grades 9-12)', 'Secondary', 3),
('Higher Secondary', '12th grade or equivalent', 'Higher Secondary', 4),
('Diploma', 'Technical or vocational diploma', 'Diploma', 5),
('Associate Degree', 'Two-year college degree', 'Undergraduate', 6),
('Bachelor''s Degree', 'Four-year undergraduate degree', 'Undergraduate', 7),
('Master''s Degree', 'Postgraduate degree', 'Postgraduate', 8),
('PhD/Doctorate', 'Doctoral degree with research', 'Doctoral', 9),
('Professional Degree', 'Professional certification (MD, JD, etc.)', 'Professional', 10),
('Trade Certificate', 'Skilled trade certification', 'Vocational', 11),
('Industry Certification', 'Industry-specific certification', 'Certification', 12);

-- =====================================================
-- 4. PROFESSIONS SAMPLE DATA
-- =====================================================
INSERT INTO professions (profession_name, description, category, industry, display_order) VALUES
('Software Engineer', 'Develops and maintains software applications', 'Technology', 'Information Technology', 1),
('Data Scientist', 'Analyzes complex data to derive insights', 'Technology', 'Information Technology', 2),
('Web Developer', 'Creates and maintains websites', 'Technology', 'Information Technology', 3),
('Mobile App Developer', 'Develops mobile applications', 'Technology', 'Information Technology', 4),
('Doctor', 'Medical practitioner providing healthcare', 'Healthcare', 'Healthcare & Medicine', 5),
('Nurse', 'Healthcare professional providing patient care', 'Healthcare', 'Healthcare & Medicine', 6),
('Teacher', 'Educates students in various subjects', 'Education', 'Education', 7),
('Professor', 'Higher education instructor and researcher', 'Education', 'Education', 8),
('Business Analyst', 'Analyzes business processes and requirements', 'Business', 'Business Services', 9),
('Project Manager', 'Manages projects and teams', 'Business', 'Business Services', 10),
('Mechanical Engineer', 'Designs and develops mechanical systems', 'Engineering', 'Engineering', 11),
('Civil Engineer', 'Designs infrastructure and construction projects', 'Engineering', 'Engineering', 12),
('Financial Advisor', 'Provides financial planning services', 'Finance', 'Financial Services', 13),
('Accountant', 'Manages financial records and transactions', 'Finance', 'Financial Services', 14),
('Lawyer', 'Provides legal counsel and representation', 'Legal', 'Legal Services', 15),
('Graphic Designer', 'Creates visual designs and artwork', 'Arts & Design', 'Creative & Design', 16),
('Marketing Manager', 'Develops and executes marketing strategies', 'Sales & Marketing', 'Sales & Marketing', 17),
('Sales Representative', 'Sells products and services to customers', 'Sales & Marketing', 'Sales & Marketing', 18);

-- =====================================================
-- 5. MAIN CATEGORIES SAMPLE DATA
-- =====================================================
INSERT INTO main_categories (group_id, name, description, icon_class, display_order) VALUES
-- Mobile Applications Group
(1, 'Mobile Development', 'Mobile app development services', 'fas fa-mobile-alt', 1),
(1, 'App Design', 'Mobile app UI/UX design', 'fas fa-paint-brush', 2),
(1, 'App Testing', 'Mobile app testing and QA', 'fas fa-bug', 3),
(1, 'App Store Optimization', 'ASO and app marketing', 'fas fa-chart-line', 4),

-- Corporate Solutions Group
(2, 'Enterprise Software', 'Business software solutions', 'fas fa-building', 1),
(2, 'Consulting Services', 'Business consulting and strategy', 'fas fa-handshake', 2),
(2, 'Training Programs', 'Corporate training and development', 'fas fa-chalkboard-teacher', 3),
(2, 'Support Services', 'Technical and business support', 'fas fa-headset', 4),

-- Web Services Group
(3, 'Web Development', 'Website and web app development', 'fas fa-globe', 1),
(3, 'E-commerce', 'Online store and marketplace solutions', 'fas fa-shopping-cart', 2),
(3, 'Digital Marketing', 'Online marketing and SEO services', 'fas fa-bullhorn', 3),
(3, 'Cloud Services', 'Cloud hosting and infrastructure', 'fas fa-cloud', 4);

-- =====================================================
-- 6. CATEGORY LEVEL 1 SAMPLE DATA
-- =====================================================
INSERT INTO category_level1 (main_category_id, name, description, icon_class, display_order) VALUES
-- Mobile Development subcategories
(1, 'iOS Development', 'iPhone and iPad app development', 'fab fa-apple', 1),
(1, 'Android Development', 'Android app development', 'fab fa-android', 2),
(1, 'Cross-Platform', 'React Native, Flutter development', 'fas fa-code', 3),

-- App Design subcategories
(2, 'UI Design', 'User interface design', 'fas fa-desktop', 1),
(2, 'UX Research', 'User experience research', 'fas fa-search', 2),
(2, 'Prototyping', 'App prototyping and wireframing', 'fas fa-drafting-compass', 3),

-- Web Development subcategories
(9, 'Frontend Development', 'Client-side web development', 'fab fa-html5', 1),
(9, 'Backend Development', 'Server-side development', 'fas fa-server', 2),
(9, 'Full Stack Development', 'Complete web development', 'fas fa-layer-group', 3);

-- =====================================================
-- 7. CATEGORY LEVEL 2 SAMPLE DATA
-- =====================================================
INSERT INTO category_level2 (level1_category_id, name, description, icon_class, display_order) VALUES
-- iOS Development subcategories
(1, 'Native iOS', 'Swift and Objective-C development', 'fas fa-code', 1),
(1, 'iOS UI/UX', 'iOS-specific design patterns', 'fas fa-mobile', 2),

-- Android Development subcategories
(2, 'Native Android', 'Java and Kotlin development', 'fas fa-code', 1),
(2, 'Android UI/UX', 'Material Design implementation', 'fas fa-mobile', 2),

-- Frontend Development subcategories
(10, 'React Development', 'React.js applications', 'fab fa-react', 1),
(10, 'Vue.js Development', 'Vue.js applications', 'fab fa-vuejs', 2),
(10, 'Angular Development', 'Angular applications', 'fab fa-angular', 3);

-- =====================================================
-- 8. CATEGORY LEVEL 3 SAMPLE DATA
-- =====================================================
INSERT INTO category_level3 (level2_category_id, name, description, display_order) VALUES
-- Native iOS subcategories
(1, 'Swift Development', 'Modern Swift programming', 1),
(1, 'Objective-C Development', 'Legacy Objective-C support', 2),
(1, 'SwiftUI', 'Declarative UI framework', 3),

-- Native Android subcategories
(2, 'Java Development', 'Traditional Android Java', 1),
(2, 'Kotlin Development', 'Modern Kotlin programming', 2),
(2, 'Jetpack Compose', 'Modern Android UI toolkit', 3),

-- React Development subcategories
(3, 'React Hooks', 'Modern React with hooks', 1),
(3, 'Redux Integration', 'State management with Redux', 2),
(3, 'Next.js', 'React framework for production', 3);

-- =====================================================
-- 9. CORPORATE USERS SAMPLE DATA
-- =====================================================
INSERT INTO corporate_users (full_name, mobile_number, email_id, username, password_hash, role, status, email_verified) VALUES
('John Corporate', '******-0101', '<EMAIL>', 'johncorp', '$2b$10$example_hash_1', 'CORPORATE', 'Active', true),
('Sarah Manager', '******-0102', '<EMAIL>', 'sarahman', '$2b$10$example_hash_2', 'CORPORATE', 'Active', true),
('Mike Director', '******-0103', '<EMAIL>', 'mikedir', '$2b$10$example_hash_3', 'CORPORATE', 'Inactive', true),
('Lisa Executive', '******-0104', '<EMAIL>', 'lisaexec', '$2b$10$example_hash_4', 'CORPORATE', 'Active', false),
('David Admin', '******-0105', '<EMAIL>', 'davidadm', '$2b$10$example_hash_5', 'CORPORATE', 'Active', true);

-- =====================================================
-- UPDATE SEQUENCES
-- =====================================================
SELECT setval('profile_groups_id_seq', (SELECT MAX(id) FROM profile_groups));
SELECT setval('languages_id_seq', (SELECT MAX(id) FROM languages));
SELECT setval('education_levels_id_seq', (SELECT MAX(id) FROM education_levels));
SELECT setval('professions_id_seq', (SELECT MAX(id) FROM professions));
SELECT setval('main_categories_id_seq', (SELECT MAX(id) FROM main_categories));
SELECT setval('category_level1_id_seq', (SELECT MAX(id) FROM category_level1));
SELECT setval('category_level2_id_seq', (SELECT MAX(id) FROM category_level2));
SELECT setval('category_level3_id_seq', (SELECT MAX(id) FROM category_level3));
