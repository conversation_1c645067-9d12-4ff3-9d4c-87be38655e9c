const pool = require('../config/database');

class UserDetails {
  // Create user details
  static async create(userDetailsData) {
    const {
      first_name,
      middle_name,
      last_name,
      user_id,
      display_name,
      gender,
      martial_status,
      coutry_id,
      state_id,
      district_id,
      nationality,
      education,
      profession,
      photo,
      date_of_birth,
      language_id
    } = userDetailsData;

    const query = `
      INSERT INTO my_user_details (
        first_name, middle_name, last_name, user_id, display_name, gender,
        martial_status, coutry_id, state_id, district_id, nationality,
        education, profession, photo, date_of_birth, language_id,
        created_at, update_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW())
      RETURNING *
    `;
    
    const values = [
      first_name, middle_name, last_name, user_id, display_name, gender,
      martial_status, coutry_id, state_id, district_id, nationality,
      education, profession, photo, date_of_birth, language_id
    ];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find user details by user ID
  static async findByUserId(userId) {
    const query = `
      SELECT ud.*, c.name as country_name, s.name as state_name, 
             d.name as district_name, e.name as education_name,
             p.name as profession_name, l.primary_language
      FROM my_user_details ud
      LEFT JOIN my_country c ON ud.coutry_id = c.id
      LEFT JOIN my_state s ON ud.state_id = s.id
      LEFT JOIN my_district d ON ud.district_id = d.id
      LEFT JOIN my_education e ON ud.education = e.id
      LEFT JOIN my_profession p ON ud.profession = p.id
      LEFT JOIN my_language l ON ud.language_id = l.id
      WHERE ud.user_id = $1
    `;
    
    try {
      const result = await pool.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Update user details
  static async update(userId, updateData) {
    const fields = [];
    const values = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = $${paramCount}`);
        values.push(updateData[key]);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push(`update_at = NOW()`);
    values.push(userId);

    const query = `
      UPDATE my_user_details 
      SET ${fields.join(', ')}
      WHERE user_id = $${paramCount}
      RETURNING *
    `;
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete user details
  static async delete(userId) {
    const query = `DELETE FROM my_user_details WHERE user_id = $1 RETURNING *`;
    
    try {
      const result = await pool.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Get complete user profile (user + details)
  static async getCompleteProfile(userId) {
    const query = `
      SELECT u.id, u.user_name, u.email, u.mobile_number, u.status,
             u.created_on, u.last_logged_in, r.role,
             ud.first_name, ud.middle_name, ud.last_name, ud.display_name,
             ud.gender, ud.martial_status, ud.nationality, ud.photo,
             ud.date_of_birth, c.name as country_name, s.name as state_name,
             d.name as district_name, e.name as education_name,
             p.name as profession_name, l.primary_language
      FROM my_users u
      LEFT JOIN my_role r ON u.role_id = r.id
      LEFT JOIN my_user_details ud ON u.id = ud.user_id
      LEFT JOIN my_country c ON ud.coutry_id = c.id
      LEFT JOIN my_state s ON ud.state_id = s.id
      LEFT JOIN my_district d ON ud.district_id = d.id
      LEFT JOIN my_education e ON ud.education = e.id
      LEFT JOIN my_profession p ON ud.profession = p.id
      LEFT JOIN my_language l ON ud.language_id = l.id
      WHERE u.id = $1 AND u.status = 1
    `;
    
    try {
      const result = await pool.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserDetails;
