import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge, Pagination } from 'react-bootstrap';

const PublicDatabaseManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);

    // Filter and pagination state
    const [filters, setFilters] = useState({
        search: '',
        role: '',
        status: '',
        country: '',
        state: '',
        dateFrom: '',
        dateTo: ''
    });
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [sortField, setSortField] = useState('createdAt');
    const [sortDirection, setSortDirection] = useState('desc');

    // Public database users (aggregated from all user types)
    const [publicUsers, setPublicUsers] = useState([
        {
            id: 1,
            name: '<PERSON>',
            email: '<EMAIL>',
            username: 'johnadmin',
            role: 'ADMIN',
            status: 'Active',
            country: 'United States',
            state: 'California',
            district: 'Los Angeles',
            mobileNumber: '******-0101',
            lastLogin: '2024-01-20 09:30 AM',
            createdAt: '2024-01-15',
            loginCount: 45,
            userType: 'Admin'
        },
        {
            id: 2,
            name: 'Sarah Corporate',
            email: '<EMAIL>',
            username: 'sarahcorp',
            role: 'CORPORATE',
            status: 'Active',
            country: 'United States',
            state: 'New York',
            district: 'Manhattan',
            mobileNumber: '******-0102',
            lastLogin: '2024-01-19 02:15 PM',
            createdAt: '2024-01-12',
            loginCount: 32,
            userType: 'Corporate'
        },
        {
            id: 3,
            name: 'Rajesh Kumar',
            email: '<EMAIL>',
            username: 'rajeshho',
            role: 'HEAD_OFFICE',
            status: 'Active',
            country: 'India',
            state: 'Maharashtra',
            district: 'Mumbai',
            mobileNumber: '+91-9876543210',
            lastLogin: '2024-01-20 10:15 AM',
            createdAt: '2024-01-10',
            loginCount: 28,
            userType: 'Head Office'
        },
        {
            id: 4,
            name: 'Priya Sharma',
            email: '<EMAIL>',
            username: 'priyareg',
            role: 'REGIONAL',
            status: 'Active',
            country: 'India',
            state: 'Karnataka',
            district: 'Bangalore',
            mobileNumber: '+91-9876543211',
            lastLogin: '2024-01-19 04:20 PM',
            createdAt: '2024-01-08',
            loginCount: 22,
            userType: 'Regional Office'
        },
        {
            id: 5,
            name: 'Mike Branch',
            email: '<EMAIL>',
            username: 'mikebranch',
            role: 'BRANCH',
            status: 'Inactive',
            country: 'Canada',
            state: 'Ontario',
            district: 'Toronto',
            mobileNumber: '******-555-0103',
            lastLogin: '2024-01-17 01:15 PM',
            createdAt: '2024-01-05',
            loginCount: 15,
            userType: 'Branch Office'
        },
        {
            id: 6,
            name: 'Emma Wilson',
            email: '<EMAIL>',
            username: 'emmawilson',
            role: 'USER',
            status: 'Active',
            country: 'United Kingdom',
            state: 'England',
            district: 'London',
            mobileNumber: '+44-20-7946-0958',
            lastLogin: '2024-01-20 11:45 AM',
            createdAt: '2024-01-03',
            loginCount: 67,
            userType: 'Regular User'
        }
    ]);

    const [filteredUsers, setFilteredUsers] = useState(publicUsers);

    const roles = ['ADMIN', 'CORPORATE', 'HEAD_OFFICE', 'REGIONAL', 'BRANCH', 'USER'];
    const countries = ['United States', 'India', 'Canada', 'United Kingdom', 'Australia'];

    useEffect(() => {
        fetchPublicUsers();
    }, []);

    useEffect(() => {
        applyFilters();
    }, [filters, publicUsers, sortField, sortDirection]);

    const fetchPublicUsers = async () => {
        setLoading(true);
        try {
            // API call to fetch public database users
            // const response = await fetch('/api/public-database');
            // const data = await response.json();
            // setPublicUsers(data);
        } catch (error) {
            console.error('Error fetching public users:', error);
            setError('Failed to fetch public database');
        } finally {
            setLoading(false);
        }
    };

    const applyFilters = () => {
        let filtered = [...publicUsers];

        // Apply search filter
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filtered = filtered.filter(user => 
                user.name.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.username.toLowerCase().includes(searchTerm) ||
                user.mobileNumber.includes(searchTerm)
            );
        }

        // Apply role filter
        if (filters.role) {
            filtered = filtered.filter(user => user.role === filters.role);
        }

        // Apply status filter
        if (filters.status) {
            filtered = filtered.filter(user => user.status === filters.status);
        }

        // Apply country filter
        if (filters.country) {
            filtered = filtered.filter(user => user.country === filters.country);
        }

        // Apply state filter
        if (filters.state) {
            filtered = filtered.filter(user => user.state === filters.state);
        }

        // Apply date range filter
        if (filters.dateFrom) {
            filtered = filtered.filter(user => user.createdAt >= filters.dateFrom);
        }
        if (filters.dateTo) {
            filtered = filtered.filter(user => user.createdAt <= filters.dateTo);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            let aValue = a[sortField];
            let bValue = b[sortField];

            if (sortField === 'createdAt' || sortField === 'lastLogin') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            if (sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        setFilteredUsers(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    };

    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const handleViewDetails = (user) => {
        setSelectedUser(user);
        setShowModal(true);
    };

    const exportToCSV = () => {
        const headers = ['Name', 'Email', 'Username', 'Role', 'Status', 'Country', 'State', 'District', 'Mobile', 'Last Login', 'Created At', 'Login Count'];
        const csvContent = [
            headers.join(','),
            ...filteredUsers.map(user => [
                user.name,
                user.email,
                user.username,
                user.role,
                user.status,
                user.country,
                user.state,
                user.district,
                user.mobileNumber,
                user.lastLogin,
                user.createdAt,
                user.loginCount
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `public_database_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        setSuccess('Database exported to CSV successfully');
    };

    const exportToJSON = () => {
        const jsonContent = JSON.stringify(filteredUsers, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `public_database_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        setSuccess('Database exported to JSON successfully');
    };

    const clearFilters = () => {
        setFilters({
            search: '',
            role: '',
            status: '',
            country: '',
            state: '',
            dateFrom: '',
            dateTo: ''
        });
    };

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

    const getRoleBadgeColor = (role) => {
        switch (role) {
            case 'ADMIN': return 'danger';
            case 'CORPORATE': return 'primary';
            case 'HEAD_OFFICE': return 'success';
            case 'REGIONAL': return 'info';
            case 'BRANCH': return 'warning';
            case 'USER': return 'secondary';
            default: return 'secondary';
        }
    };

    const getSortIcon = (field) => {
        if (sortField !== field) return 'fas fa-sort';
        return sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    };

    return (
        <Container fluid className="public-database-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Public Database Management</h4>
                <div className="export-buttons">
                    <Button variant="outline-success" className="me-2" onClick={exportToCSV}>
                        <i className="fas fa-file-csv me-1"></i>Export CSV
                    </Button>
                    <Button variant="outline-info" onClick={exportToJSON}>
                        <i className="fas fa-file-code me-1"></i>Export JSON
                    </Button>
                </div>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{publicUsers.length}</h3>
                            <p className="mb-0">Total Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {publicUsers.filter(user => user.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {new Set(publicUsers.map(user => user.country)).size}
                            </h3>
                            <p className="mb-0">Countries</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">{filteredUsers.length}</h3>
                            <p className="mb-0">Filtered Results</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Filters */}
            <Card className="filter-section mb-4">
                <Card.Header>
                    <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                            <i className="fas fa-filter me-2"></i>Filters
                        </h6>
                        <Button variant="outline-secondary" size="sm" onClick={clearFilters}>
                            <i className="fas fa-times me-1"></i>Clear Filters
                        </Button>
                    </div>
                </Card.Header>
                <Card.Body>
                    <Row>
                        <Col md={3}>
                            <Form.Group className="mb-3">
                                <Form.Label>Search</Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder="Name, email, username, mobile..."
                                    value={filters.search}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        search: e.target.value
                                    })}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group className="mb-3">
                                <Form.Label>Role</Form.Label>
                                <Form.Select
                                    value={filters.role}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        role: e.target.value
                                    })}
                                >
                                    <option value="">All Roles</option>
                                    {roles.map(role => (
                                        <option key={role} value={role}>{role}</option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group className="mb-3">
                                <Form.Label>Status</Form.Label>
                                <Form.Select
                                    value={filters.status}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        status: e.target.value
                                    })}
                                >
                                    <option value="">All Status</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={2}>
                            <Form.Group className="mb-3">
                                <Form.Label>Country</Form.Label>
                                <Form.Select
                                    value={filters.country}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        country: e.target.value
                                    })}
                                >
                                    <option value="">All Countries</option>
                                    {countries.map(country => (
                                        <option key={country} value={country}>{country}</option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                        </Col>
                        <Col md={1.5}>
                            <Form.Group className="mb-3">
                                <Form.Label>From Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={filters.dateFrom}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        dateFrom: e.target.value
                                    })}
                                />
                            </Form.Group>
                        </Col>
                        <Col md={1.5}>
                            <Form.Group className="mb-3">
                                <Form.Label>To Date</Form.Label>
                                <Form.Control
                                    type="date"
                                    value={filters.dateTo}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        dateTo: e.target.value
                                    })}
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            {/* Users Table */}
            <Card>
                <Card.Header>
                    <div className="d-flex justify-content-between align-items-center">
                        <h5 className="mb-0">
                            <i className="fas fa-database me-2"></i>
                            Public Database ({filteredUsers.length} users)
                        </h5>
                        <small className="text-muted">
                            Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredUsers.length)} of {filteredUsers.length}
                        </small>
                    </div>
                </Card.Header>
                <Card.Body>
                    {currentUsers.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-database fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Users Found</h5>
                            <p className="text-muted">Try adjusting your filters</p>
                        </div>
                    ) : (
                        <>
                            <Table responsive hover>
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th 
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => handleSort('name')}
                                        >
                                            Name <i className={getSortIcon('name')}></i>
                                        </th>
                                        <th 
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => handleSort('email')}
                                        >
                                            Email <i className={getSortIcon('email')}></i>
                                        </th>
                                        <th 
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => handleSort('role')}
                                        >
                                            Role <i className={getSortIcon('role')}></i>
                                        </th>
                                        <th>Status</th>
                                        <th>Location</th>
                                        <th>Mobile</th>
                                        <th 
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => handleSort('lastLogin')}
                                        >
                                            Last Login <i className={getSortIcon('lastLogin')}></i>
                                        </th>
                                        <th 
                                            style={{ cursor: 'pointer' }}
                                            onClick={() => handleSort('createdAt')}
                                        >
                                            Created <i className={getSortIcon('createdAt')}></i>
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {currentUsers.map((user, index) => (
                                        <tr key={user.id}>
                                            <td>{indexOfFirstItem + index + 1}</td>
                                            <td>
                                                <div>
                                                    <strong>{user.name}</strong>
                                                    <br />
                                                    <small className="text-muted">@{user.username}</small>
                                                </div>
                                            </td>
                                            <td>{user.email}</td>
                                            <td>
                                                <Badge bg={getRoleBadgeColor(user.role)}>
                                                    {user.userType}
                                                </Badge>
                                            </td>
                                            <td>
                                                <Badge bg={user.status === 'Active' ? 'success' : 'danger'}>
                                                    {user.status}
                                                </Badge>
                                            </td>
                                            <td>
                                                <small>
                                                    {user.country}<br />
                                                    {user.state}, {user.district}
                                                </small>
                                            </td>
                                            <td>{user.mobileNumber}</td>
                                            <td>
                                                <small>{user.lastLogin}</small>
                                            </td>
                                            <td>
                                                <small>{user.createdAt}</small>
                                            </td>
                                            <td>
                                                <Button
                                                    variant="outline-info"
                                                    size="sm"
                                                    onClick={() => handleViewDetails(user)}
                                                    title="View Details"
                                                >
                                                    <i className="fas fa-eye"></i>
                                                </Button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center mt-4">
                                    <Pagination>
                                        <Pagination.First 
                                            onClick={() => setCurrentPage(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev 
                                            onClick={() => setCurrentPage(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />
                                        
                                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                            const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                                            return (
                                                <Pagination.Item
                                                    key={pageNumber}
                                                    active={pageNumber === currentPage}
                                                    onClick={() => setCurrentPage(pageNumber)}
                                                >
                                                    {pageNumber}
                                                </Pagination.Item>
                                            );
                                        })}
                                        
                                        <Pagination.Next 
                                            onClick={() => setCurrentPage(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last 
                                            onClick={() => setCurrentPage(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </>
                    )}
                </Card.Body>
            </Card>

            {/* User Details Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-user me-2"></i>
                        User Details: {selectedUser?.name}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {selectedUser && (
                        <Row>
                            <Col md={6}>
                                <div className="user-detail-section mb-3">
                                    <h6>Personal Information</h6>
                                    <div className="detail-item">
                                        <strong>Name:</strong> {selectedUser.name}
                                    </div>
                                    <div className="detail-item">
                                        <strong>Email:</strong> {selectedUser.email}
                                    </div>
                                    <div className="detail-item">
                                        <strong>Username:</strong> {selectedUser.username}
                                    </div>
                                    <div className="detail-item">
                                        <strong>Mobile:</strong> {selectedUser.mobileNumber}
                                    </div>
                                </div>
                            </Col>
                            <Col md={6}>
                                <div className="user-detail-section mb-3">
                                    <h6>Account Information</h6>
                                    <div className="detail-item">
                                        <strong>Role:</strong> 
                                        <Badge bg={getRoleBadgeColor(selectedUser.role)} className="ms-2">
                                            {selectedUser.userType}
                                        </Badge>
                                    </div>
                                    <div className="detail-item">
                                        <strong>Status:</strong> 
                                        <Badge bg={selectedUser.status === 'Active' ? 'success' : 'danger'} className="ms-2">
                                            {selectedUser.status}
                                        </Badge>
                                    </div>
                                    <div className="detail-item">
                                        <strong>Created:</strong> {selectedUser.createdAt}
                                    </div>
                                    <div className="detail-item">
                                        <strong>Last Login:</strong> {selectedUser.lastLogin}
                                    </div>
                                    <div className="detail-item">
                                        <strong>Login Count:</strong> {selectedUser.loginCount}
                                    </div>
                                </div>
                            </Col>
                            <Col md={12}>
                                <div className="user-detail-section">
                                    <h6>Location Information</h6>
                                    <div className="detail-item">
                                        <strong>Country:</strong> {selectedUser.country}
                                    </div>
                                    <div className="detail-item">
                                        <strong>State:</strong> {selectedUser.state}
                                    </div>
                                    <div className="detail-item">
                                        <strong>District:</strong> {selectedUser.district}
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowModal(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default PublicDatabaseManagement;
