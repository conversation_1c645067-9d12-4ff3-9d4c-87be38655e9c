-- Migration: Create my_roles table
-- Description: Defines all available roles in the system with hierarchy levels
-- Created: 2024-01-18

CREATE TABLE my_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    hierarchy_level INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default roles
INSERT INTO my_roles (role_name, role_code, description, hierarchy_level) VALUES
('Super Admin', 'ADMIN', 'System administrator with full access', 1),
('Corporate Manager', 'CORPORATE', 'Corporate level manager', 2),
('Head Office Manager', 'HEAD_OFFICE', 'Head office level manager', 3),
('Regional Manager', 'REGIONAL', 'Regional office level manager', 4),
('Branch Manager', 'BRANCH', 'Branch office level manager', 5);

-- <PERSON>reate indexes
CREATE INDEX idx_roles_code ON my_roles(role_code);
CREATE INDEX idx_roles_hierarchy ON my_roles(hierarchy_level);
CREATE INDEX idx_roles_active ON my_roles(is_active);
