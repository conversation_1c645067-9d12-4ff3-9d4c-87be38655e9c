import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Container } from 'react-bootstrap';
import HomeScreen from './components/HomeScreen';
import LoginModal from './components/LoginModal';
import ProfileModal from './components/ProfileModal';
import AdminDashboard from './components/AdminDashboard';
import ProtectedRoute from './components/ProtectedRoute';
import AuthProvider, { useAuth } from './contexts/AuthContext';
import './App.css';

// Root component that handles authentication check
const RootComponent = () => {
  const { isAuthenticated, user, loading, canAccessDashboard } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);

  if (loading) {
    return (
      <div className="loading-screen">
        <Container className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading...</p>
        </Container>
      </div>
    );
  }

  // If not authenticated, show login modal
  if (!isAuthenticated) {
    return (
      <>
        <div className="auth-required-screen">
          <Container className="text-center py-5">
            <h2>Welcome to My Group</h2>
            <p>Please login to access the application</p>
            <div className="d-flex flex-column align-items-center gap-3">
              <button
                className="btn btn-primary btn-lg"
                onClick={() => setShowLoginModal(true)}
              >
                Login
              </button>
              <p className="text-muted small">
                Mobile-first application designed for smartphones
              </p>
            </div>
          </Container>
        </div>

        <LoginModal
          show={showLoginModal}
          onHide={() => setShowLoginModal(false)}
        />
      </>
    );
  }

  // If authenticated, show home screen with admin access info
  return (
    <>
      <HomeScreen
        onShowLogin={() => setShowLoginModal(true)}
        onShowProfile={() => setShowProfileModal(true)}
      />

      <LoginModal
        show={showLoginModal}
        onHide={() => setShowLoginModal(false)}
      />

      <ProfileModal
        show={showProfileModal}
        onHide={() => setShowProfileModal(false)}
      />
    </>
  );
};

function App() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);

    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  if (!isMobile) {
    return (
      <div className="desktop-not-available">
        <Container className="text-center py-5">
          <h1>Desktop Version Not Available</h1>
          <p>This application is designed for mobile devices only.</p>
          <p>Please access this application from a mobile device or resize your browser window.</p>
        </Container>
      </div>
    );
  }

  return (
    <AuthProvider>
      <Router>
        <div className="App mobile-app">
          <Routes>
            {/* Root route - requires authentication */}
            <Route path="/" element={<RootComponent />} />

            {/* Admin Dashboard - requires admin role */}
            <Route
              path="/admin"
              element={
                <ProtectedRoute allowedRoles={[1, 8]}> {/* ADMIN, APP_ADMIN */}
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            {/* Redirect any unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
