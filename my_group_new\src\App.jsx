import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Container } from 'react-bootstrap';
import HomeScreen from './components/HomeScreen';
import LoginModal from './components/LoginModal';
import ProfileModal from './components/ProfileModal';
import AuthProvider from './contexts/AuthContext';
import './App.css';

function App() {
  const [isMobile, setIsMobile] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);

    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  if (!isMobile) {
    return (
      <div className="desktop-not-available">
        <Container className="text-center py-5">
          <h1>Desktop Version Not Available</h1>
          <p>This application is designed for mobile devices only.</p>
          <p>Please access this application from a mobile device or resize your browser window.</p>
        </Container>
      </div>
    );
  }

  return (
    <AuthProvider>
      <Router>
        <div className="App mobile-app">
          <Routes>
            <Route 
              path="/" 
              element={
                <HomeScreen 
                  onShowLogin={() => setShowLoginModal(true)}
                  onShowProfile={() => setShowProfileModal(true)}
                />
              } 
            />
          </Routes>

          <LoginModal 
            show={showLoginModal}
            onHide={() => setShowLoginModal(false)}
          />

          <ProfileModal 
            show={showProfileModal}
            onHide={() => setShowProfileModal(false)}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
