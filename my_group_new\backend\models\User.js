const pool = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  // Create a new user
  static async create(userData) {
    const {
      user_name,
      password,
      role_id,
      group_id,
      mobile_number,
      email,
      ip_address
    } = userData;

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const query = `
      INSERT INTO my_users (
        user_name, password, role_id, group_id, mobile_number, email, 
        ip_address, created_on, modified_on, last_logged_in, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW(), NOW(), 1)
      RETURNING id, user_name, email, mobile_number, role_id, group_id, status
    `;
    
    const values = [user_name, hashedPassword, role_id, group_id, mobile_number, email, ip_address];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    const query = `
      SELECT u.*, r.role, r.role_description 
      FROM my_users u 
      LEFT JOIN my_role r ON u.role_id = r.id 
      WHERE u.email = $1 AND u.status = 1
    `;
    
    try {
      const result = await pool.query(query, [email]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    const query = `
      SELECT u.*, r.role, r.role_description 
      FROM my_users u 
      LEFT JOIN my_role r ON u.role_id = r.id 
      WHERE u.id = $1 AND u.status = 1
    `;
    
    try {
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Verify password
  static async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // Update last login
  static async updateLastLogin(userId, ipAddress) {
    const query = `
      UPDATE my_users 
      SET last_logged_in = NOW(), ip_address = $2, modified_on = NOW()
      WHERE id = $1
    `;
    
    try {
      await pool.query(query, [userId, ipAddress]);
    } catch (error) {
      throw error;
    }
  }

  // Get all users with pagination
  static async getAll(page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    const query = `
      SELECT u.id, u.user_name, u.email, u.mobile_number, u.status, 
             u.created_on, u.last_logged_in, r.role
      FROM my_users u 
      LEFT JOIN my_role r ON u.role_id = r.id 
      ORDER BY u.created_on DESC
      LIMIT $1 OFFSET $2
    `;
    
    try {
      const result = await pool.query(query, [limit, offset]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Update user status
  static async updateStatus(userId, status) {
    const query = `
      UPDATE my_users 
      SET status = $2, modified_on = NOW()
      WHERE id = $1
      RETURNING id, user_name, email, status
    `;
    
    try {
      const result = await pool.query(query, [userId, status]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }
}

module.exports = User;
