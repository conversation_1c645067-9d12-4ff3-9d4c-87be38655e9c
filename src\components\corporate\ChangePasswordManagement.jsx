import React, { useState } from 'react';
import { Container, Row, Col, Card, Button, Form, Alert, ProgressBar } from 'react-bootstrap';

const ChangePasswordManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    // Form data
    const [formData, setFormData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });

    // Password strength state
    const [passwordStrength, setPasswordStrength] = useState({
        score: 0,
        feedback: [],
        color: 'danger'
    });

    // Password requirements
    const passwordRequirements = [
        { id: 'length', text: 'At least 8 characters', regex: /.{8,}/ },
        { id: 'uppercase', text: 'At least one uppercase letter', regex: /[A-Z]/ },
        { id: 'lowercase', text: 'At least one lowercase letter', regex: /[a-z]/ },
        { id: 'number', text: 'At least one number', regex: /\d/ },
        { id: 'special', text: 'At least one special character', regex: /[!@#$%^&*(),.?":{}|<>]/ }
    ];

    // Current user info (mock data)
    const currentUser = {
        name: 'Corporate Admin',
        email: '<EMAIL>',
        username: 'corporateadmin',
        role: 'CORPORATE',
        lastPasswordChange: '2024-01-01',
        passwordExpiryDays: 90
    };

    const calculatePasswordStrength = (password) => {
        let score = 0;
        const feedback = [];

        // Check each requirement
        passwordRequirements.forEach(req => {
            if (req.regex.test(password)) {
                score += 20;
            } else {
                feedback.push(req.text);
            }
        });

        // Additional checks
        if (password.length >= 12) score += 10;
        if (/[!@#$%^&*(),.?":{}|<>].*[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 10;

        let color = 'danger';
        if (score >= 80) color = 'success';
        else if (score >= 60) color = 'warning';
        else if (score >= 40) color = 'info';

        setPasswordStrength({ score: Math.min(score, 100), feedback, color });
    };

    const handlePasswordChange = (e) => {
        const newPassword = e.target.value;
        setFormData({
            ...formData,
            newPassword: newPassword
        });
        calculatePasswordStrength(newPassword);
    };

    const validateForm = () => {
        if (!formData.currentPassword) {
            setError('Current password is required');
            return false;
        }

        if (!formData.newPassword) {
            setError('New password is required');
            return false;
        }

        if (formData.newPassword.length < 8) {
            setError('New password must be at least 8 characters long');
            return false;
        }

        if (formData.newPassword !== formData.confirmPassword) {
            setError('New password and confirm password do not match');
            return false;
        }

        if (formData.currentPassword === formData.newPassword) {
            setError('New password must be different from current password');
            return false;
        }

        // Check if password meets minimum requirements
        const requiredChecks = passwordRequirements.slice(0, 4); // First 4 are mandatory
        const failedChecks = requiredChecks.filter(req => !req.regex.test(formData.newPassword));
        
        if (failedChecks.length > 0) {
            setError('Password does not meet minimum requirements');
            return false;
        }

        return true;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccess('');

        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Mock API call to change password
            // const response = await fetch('/api/change-password', {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //         'Authorization': `Bearer ${localStorage.getItem('token')}`
            //     },
            //     body: JSON.stringify({
            //         currentPassword: formData.currentPassword,
            //         newPassword: formData.newPassword
            //     })
            // });

            // if (!response.ok) {
            //     throw new Error('Failed to change password');
            // }

            setSuccess('Password changed successfully! You will be logged out in 5 seconds for security.');
            
            // Clear form
            setFormData({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            });
            setPasswordStrength({ score: 0, feedback: [], color: 'danger' });

            // Auto logout after 5 seconds (in real app)
            // setTimeout(() => {
            //     localStorage.removeItem('token');
            //     window.location.href = '/login';
            // }, 5000);

        } catch (error) {
            setError('Failed to change password. Please check your current password and try again.');
        } finally {
            setLoading(false);
        }
    };

    const generateStrongPassword = () => {
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const numbers = '0123456789';
        const special = '!@#$%^&*(),.?":{}|<>';
        
        let password = '';
        
        // Ensure at least one character from each category
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        password += special[Math.floor(Math.random() * special.length)];
        
        // Fill the rest randomly
        const allChars = uppercase + lowercase + numbers + special;
        for (let i = 4; i < 12; i++) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
        }
        
        // Shuffle the password
        password = password.split('').sort(() => Math.random() - 0.5).join('');
        
        setFormData({
            ...formData,
            newPassword: password,
            confirmPassword: password
        });
        calculatePasswordStrength(password);
    };

    const getPasswordExpiryStatus = () => {
        const lastChange = new Date(currentUser.lastPasswordChange);
        const today = new Date();
        const daysSinceChange = Math.floor((today - lastChange) / (1000 * 60 * 60 * 24));
        const daysUntilExpiry = currentUser.passwordExpiryDays - daysSinceChange;

        if (daysUntilExpiry <= 0) {
            return { status: 'expired', message: 'Password has expired', color: 'danger' };
        } else if (daysUntilExpiry <= 7) {
            return { status: 'expiring', message: `Password expires in ${daysUntilExpiry} days`, color: 'warning' };
        } else {
            return { status: 'valid', message: `Password expires in ${daysUntilExpiry} days`, color: 'success' };
        }
    };

    const expiryStatus = getPasswordExpiryStatus();

    return (
        <Container fluid className="change-password-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    <i className="fas fa-check-circle me-2"></i>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    <i className="fas fa-exclamation-triangle me-2"></i>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Change Password</h4>
                <Alert variant={expiryStatus.color} className="mb-0 py-2">
                    <i className="fas fa-clock me-1"></i>
                    {expiryStatus.message}
                </Alert>
            </div>

            <Row className="justify-content-center">
                <Col lg={8}>
                    {/* Current User Info */}
                    <Card className="mb-4">
                        <Card.Header>
                            <h5 className="mb-0">
                                <i className="fas fa-user me-2"></i>Account Information
                            </h5>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <div className="mb-2">
                                        <strong>Name:</strong> {currentUser.name}
                                    </div>
                                    <div className="mb-2">
                                        <strong>Email:</strong> {currentUser.email}
                                    </div>
                                </Col>
                                <Col md={6}>
                                    <div className="mb-2">
                                        <strong>Username:</strong> {currentUser.username}
                                    </div>
                                    <div className="mb-2">
                                        <strong>Last Password Change:</strong> {currentUser.lastPasswordChange}
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    {/* Password Change Form */}
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">
                                <i className="fas fa-key me-2"></i>Change Password
                            </h5>
                        </Card.Header>
                        <Card.Body>
                            <Form onSubmit={handleSubmit}>
                                {/* Current Password */}
                                <Form.Group className="mb-4">
                                    <Form.Label>Current Password *</Form.Label>
                                    <div className="input-group">
                                        <Form.Control
                                            type={showCurrentPassword ? 'text' : 'password'}
                                            placeholder="Enter your current password"
                                            value={formData.currentPassword}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                currentPassword: e.target.value
                                            })}
                                            required
                                        />
                                        <Button
                                            variant="outline-secondary"
                                            type="button"
                                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                        >
                                            <i className={`fas ${showCurrentPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                                        </Button>
                                    </div>
                                </Form.Group>

                                {/* New Password */}
                                <Form.Group className="mb-3">
                                    <div className="d-flex justify-content-between align-items-center">
                                        <Form.Label>New Password *</Form.Label>
                                        <Button
                                            variant="outline-info"
                                            size="sm"
                                            type="button"
                                            onClick={generateStrongPassword}
                                        >
                                            <i className="fas fa-magic me-1"></i>Generate Strong Password
                                        </Button>
                                    </div>
                                    <div className="input-group">
                                        <Form.Control
                                            type={showNewPassword ? 'text' : 'password'}
                                            placeholder="Enter your new password"
                                            value={formData.newPassword}
                                            onChange={handlePasswordChange}
                                            required
                                        />
                                        <Button
                                            variant="outline-secondary"
                                            type="button"
                                            onClick={() => setShowNewPassword(!showNewPassword)}
                                        >
                                            <i className={`fas ${showNewPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                                        </Button>
                                    </div>
                                    
                                    {/* Password Strength Indicator */}
                                    {formData.newPassword && (
                                        <div className="mt-2">
                                            <div className="d-flex justify-content-between align-items-center mb-1">
                                                <small>Password Strength:</small>
                                                <small className={`text-${passwordStrength.color}`}>
                                                    {passwordStrength.score >= 80 ? 'Strong' :
                                                     passwordStrength.score >= 60 ? 'Good' :
                                                     passwordStrength.score >= 40 ? 'Fair' : 'Weak'}
                                                </small>
                                            </div>
                                            <ProgressBar 
                                                variant={passwordStrength.color}
                                                now={passwordStrength.score}
                                                style={{ height: '6px' }}
                                            />
                                        </div>
                                    )}
                                </Form.Group>

                                {/* Confirm Password */}
                                <Form.Group className="mb-4">
                                    <Form.Label>Confirm New Password *</Form.Label>
                                    <div className="input-group">
                                        <Form.Control
                                            type={showConfirmPassword ? 'text' : 'password'}
                                            placeholder="Confirm your new password"
                                            value={formData.confirmPassword}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                confirmPassword: e.target.value
                                            })}
                                            required
                                        />
                                        <Button
                                            variant="outline-secondary"
                                            type="button"
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        >
                                            <i className={`fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                                        </Button>
                                    </div>
                                    {formData.confirmPassword && formData.newPassword !== formData.confirmPassword && (
                                        <Form.Text className="text-danger">
                                            Passwords do not match
                                        </Form.Text>
                                    )}
                                </Form.Group>

                                {/* Submit Button */}
                                <div className="d-grid">
                                    <Button 
                                        variant="primary" 
                                        type="submit" 
                                        size="lg"
                                        disabled={loading || passwordStrength.score < 40}
                                    >
                                        {loading ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm me-2" />
                                                Changing Password...
                                            </>
                                        ) : (
                                            <>
                                                <i className="fas fa-key me-2"></i>
                                                Change Password
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </Form>
                        </Card.Body>
                    </Card>

                    {/* Password Requirements */}
                    <Card className="mt-4">
                        <Card.Header>
                            <h6 className="mb-0">
                                <i className="fas fa-shield-alt me-2"></i>Password Requirements
                            </h6>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <h6 className="text-danger">Required:</h6>
                                    <ul className="list-unstyled">
                                        {passwordRequirements.slice(0, 4).map(req => (
                                            <li key={req.id} className="mb-1">
                                                <i className={`fas ${
                                                    formData.newPassword && req.regex.test(formData.newPassword) 
                                                        ? 'fa-check text-success' 
                                                        : 'fa-times text-danger'
                                                } me-2`}></i>
                                                {req.text}
                                            </li>
                                        ))}
                                    </ul>
                                </Col>
                                <Col md={6}>
                                    <h6 className="text-info">Recommended:</h6>
                                    <ul className="list-unstyled">
                                        <li className="mb-1">
                                            <i className={`fas ${
                                                formData.newPassword && formData.newPassword.length >= 12
                                                    ? 'fa-check text-success' 
                                                    : 'fa-times text-muted'
                                            } me-2`}></i>
                                            At least 12 characters
                                        </li>
                                        <li className="mb-1">
                                            <i className={`fas ${
                                                formData.newPassword && /[!@#$%^&*(),.?":{}|<>].*[!@#$%^&*(),.?":{}|<>]/.test(formData.newPassword)
                                                    ? 'fa-check text-success' 
                                                    : 'fa-times text-muted'
                                            } me-2`}></i>
                                            Multiple special characters
                                        </li>
                                        <li className="mb-1">
                                            <i className="fas fa-info-circle text-info me-2"></i>
                                            Avoid common words or patterns
                                        </li>
                                        <li className="mb-1">
                                            <i className="fas fa-info-circle text-info me-2"></i>
                                            Don't reuse recent passwords
                                        </li>
                                    </ul>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    {/* Security Tips */}
                    <Card className="mt-4">
                        <Card.Header>
                            <h6 className="mb-0">
                                <i className="fas fa-lightbulb me-2"></i>Security Tips
                            </h6>
                        </Card.Header>
                        <Card.Body>
                            <Row>
                                <Col md={6}>
                                    <ul className="list-unstyled">
                                        <li className="mb-2">
                                            <i className="fas fa-check text-success me-2"></i>
                                            Use a unique password for each account
                                        </li>
                                        <li className="mb-2">
                                            <i className="fas fa-check text-success me-2"></i>
                                            Consider using a password manager
                                        </li>
                                        <li className="mb-2">
                                            <i className="fas fa-check text-success me-2"></i>
                                            Enable two-factor authentication when available
                                        </li>
                                    </ul>
                                </Col>
                                <Col md={6}>
                                    <ul className="list-unstyled">
                                        <li className="mb-2">
                                            <i className="fas fa-times text-danger me-2"></i>
                                            Don't share your password with anyone
                                        </li>
                                        <li className="mb-2">
                                            <i className="fas fa-times text-danger me-2"></i>
                                            Don't use personal information in passwords
                                        </li>
                                        <li className="mb-2">
                                            <i className="fas fa-times text-danger me-2"></i>
                                            Don't save passwords in browsers on shared computers
                                        </li>
                                    </ul>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default ChangePasswordManagement;
