/* Customer Frontend Styles */

.customer-frontend {
  min-height: 100vh;
  background: #f8fafc;
}

/* Top Navigation */
.top-nav {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 1.8rem;
}

.logo-text {
  font-size: 1.4rem;
  font-weight: bold;
  color: #1e40af;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f1f5f9;
  border-radius: 25px;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.9rem;
  color: #374151;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-btn {
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  color: #6b7280;
}

.auth-section {
  display: flex;
  justify-content: flex-end;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.welcome-text {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

.logout-btn {
  font-size: 0.8rem;
}

.auth-buttons {
  display: flex;
  gap: 8px;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.hero-cta {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

/* Apps Section */
.apps-section {
  padding: 80px 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  text-align: center;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 60px;
}

.app-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.app-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: currentColor;
}

.app-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.app-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.app-description {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 16px;
  flex-grow: 1;
}

.app-status {
  margin-top: auto;
}

.status-available {
  background: #dcfce7;
  color: #166534;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-login {
  background: #fef3c7;
  color: #92400e;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Footer Section */
.footer-section {
  background: #1f2937;
  color: white;
  padding: 60px 0 20px 0;
}

.footer-section h5,
.footer-section h6 {
  color: white;
  margin-bottom: 20px;
}

.footer-section p {
  color: #d1d5db;
  margin-bottom: 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-links a {
  color: #d1d5db;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: white;
}

.footer-section hr {
  border-color: #374151;
  margin: 40px 0 20px 0;
}

/* Modal Customizations */
.modal-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px;
}

.modal-title {
  color: #1f2937;
  font-weight: 600;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  border-top: 1px solid #e5e7eb;
  padding: 16px 24px;
  justify-content: center;
}

.auth-switch {
  text-align: center;
  color: #6b7280;
  font-size: 0.9rem;
}

.auth-switch .btn-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.auth-switch .btn-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .app-card {
    padding: 20px 15px;
  }
  
  .app-icon {
    font-size: 2.5rem;
  }
  
  .search-bar {
    margin: 0 10px;
  }
  
  .auth-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .user-menu {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }
  
  .welcome-text {
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 60px 0;
  }
  
  .apps-section {
    padding: 60px 0;
  }
  
  .footer-section {
    padding: 40px 0 20px 0;
  }
  
  .logo-text {
    display: none;
  }
  
  .search-input {
    font-size: 0.8rem;
  }
}

/* Animation for app cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-card {
  animation: fadeInUp 0.6s ease-out;
}

.app-card:nth-child(1) { animation-delay: 0.1s; }
.app-card:nth-child(2) { animation-delay: 0.2s; }
.app-card:nth-child(3) { animation-delay: 0.3s; }
.app-card:nth-child(4) { animation-delay: 0.4s; }
.app-card:nth-child(5) { animation-delay: 0.5s; }
.app-card:nth-child(6) { animation-delay: 0.6s; }
.app-card:nth-child(7) { animation-delay: 0.7s; }
.app-card:nth-child(8) { animation-delay: 0.8s; }
