import React, { useState } from 'react';
import { Modal, Tab, Tabs } from 'react-bootstrap';
import Login from './Login';
import Register from './Register';
import './LoginModal.css';

const LoginModal = ({ show, onHide }) => {
  const [activeTab, setActiveTab] = useState('login');

  const handleSwitchToRegister = () => {
    setActiveTab('register');
  };

  const handleSwitchToLogin = () => {
    setActiveTab('login');
  };

  const handleClose = () => {
    setActiveTab('login'); // Reset to login tab when closing
    onHide();
  };

  return (
    <Modal 
      show={show} 
      onHide={handleClose}
      centered
      className="login-modal"
    >
      <Modal.Header closeButton className="border-0">
        <Modal.Title className="w-100 text-center">
          Welcome to My Group
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="px-4 pb-4">
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k)}
          className="mb-3 custom-tabs"
          justify
        >
          <Tab eventKey="login" title="Login">
            <Login 
              onSwitchToRegister={handleSwitchToRegister}
              onClose={handleClose}
            />
          </Tab>
          
          <Tab eventKey="register" title="Register">
            <Register 
              onSwitchToLogin={handleSwitchToLogin}
              onClose={handleClose}
            />
          </Tab>
        </Tabs>
      </Modal.Body>
    </Modal>
  );
};

export default LoginModal;
