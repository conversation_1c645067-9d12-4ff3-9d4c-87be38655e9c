const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { verifyToken, requireRole, ROLES } = require('../middleware/auth');

// Protected routes - require authentication
router.use(verifyToken);

// Get all users (admin only)
router.get('/', requireRole([ROLES.ADMIN, ROLES.APP_ADMIN]), userController.getAllUsers);

// Get user by ID
router.get('/:id', userController.getUserById);

// Update user status (admin only)
router.patch('/:id/status', requireRole([ROLES.ADMIN, ROLES.APP_ADMIN]), userController.updateUserStatus);

// Get user details
router.get('/:id/details', userController.getUserDetails);

// Update user details
router.put('/:id/details', userController.updateUserDetails);

// Get complete profile
router.get('/:id/profile', userController.getCompleteProfile);

// Update current user profile
router.put('/profile', userController.updateProfile);

module.exports = router;
