import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge, Dropdown } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';

const RegionalOfficeManagement = () => {
    const { currentUser } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState(''); // 'create', 'edit', 'resetPassword'
    const [editingUser, setEditingUser] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('All');

    // Sample data - replace with actual API calls
    const [regionalOffices, setRegionalOffices] = useState([
        {
            id: 1,
            region: 'North Region',
            state: 'California',
            city: 'San Francisco',
            name: '<PERSON>',
            mobileNumber: '******-0123',
            email: '<EMAIL>',
            username: 'john.smith',
            status: 'Active',
            branchCount: 12,
            userCount: 145,
            revenue: '$125,000',
            lastLogin: '2024-01-18 09:30 AM',
            createdAt: '2023-06-15'
        },
        {
            id: 2,
            region: 'South Region',
            state: 'Texas',
            city: 'Houston',
            name: 'Sarah Johnson',
            mobileNumber: '******-0124',
            email: '<EMAIL>',
            username: 'sarah.johnson',
            status: 'Active',
            branchCount: 8,
            userCount: 98,
            revenue: '$89,500',
            lastLogin: '2024-01-17 14:20 PM',
            createdAt: '2023-07-20'
        },
        {
            id: 3,
            region: 'East Region',
            state: 'New York',
            city: 'New York City',
            name: 'Michael Brown',
            mobileNumber: '******-0125',
            email: '<EMAIL>',
            username: 'michael.brown',
            status: 'Inactive',
            branchCount: 15,
            userCount: 203,
            revenue: '$178,200',
            lastLogin: '2024-01-10 11:45 AM',
            createdAt: '2023-05-10'
        }
    ]);

    // Sample countries/states data
    const [countries] = useState([
        { id: 1, name: 'United States' },
        { id: 2, name: 'Canada' },
        { id: 3, name: 'United Kingdom' }
    ]);

    const [states] = useState([
        { id: 1, name: 'California', countryId: 1 },
        { id: 2, name: 'Texas', countryId: 1 },
        { id: 3, name: 'New York', countryId: 1 },
        { id: 4, name: 'Ontario', countryId: 2 },
        { id: 5, name: 'London', countryId: 3 }
    ]);

    // Form data
    const [formData, setFormData] = useState({
        country: '',
        state: '',
        city: '',
        region: '',
        name: '',
        mobileNumber: '',
        email: '',
        username: '',
        password: '',
        confirmPassword: '',
        status: 'Active'
    });

    const handleInputChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleCreate = () => {
        setModalType('create');
        setEditingUser(null);
        setFormData({
            country: '',
            state: '',
            city: '',
            region: '',
            name: '',
            mobileNumber: '',
            email: '',
            username: '',
            password: '',
            confirmPassword: '',
            status: 'Active'
        });
        setShowModal(true);
    };

    const handleEdit = (user) => {
        setModalType('edit');
        setEditingUser(user);
        setFormData({
            country: '1', // Default to first country
            state: '1', // Default to first state
            city: user.city,
            region: user.region,
            name: user.name,
            mobileNumber: user.mobileNumber,
            email: user.email,
            username: user.username,
            password: '',
            confirmPassword: '',
            status: user.status
        });
        setShowModal(true);
    };

    const handleResetPassword = (user) => {
        setModalType('resetPassword');
        setEditingUser(user);
        setShowModal(true);
    };

    const handleDelete = async (userId) => {
        if (window.confirm('Are you sure you want to delete this regional office?')) {
            try {
                setLoading(true);
                // API call would go here
                setRegionalOffices(regionalOffices.filter(office => office.id !== userId));
                setSuccess('Regional office deleted successfully');
            } catch (error) {
                setError('Failed to delete regional office');
            } finally {
                setLoading(false);
            }
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            if (modalType === 'create') {
                const newOffice = {
                    id: Date.now(),
                    region: formData.region,
                    state: states.find(s => s.id === parseInt(formData.state))?.name || '',
                    city: formData.city,
                    name: formData.name,
                    mobileNumber: formData.mobileNumber,
                    email: formData.email,
                    username: formData.username,
                    status: formData.status,
                    branchCount: 0,
                    userCount: 0,
                    revenue: '$0',
                    createdAt: new Date().toISOString().split('T')[0],
                    lastLogin: 'Never'
                };

                setRegionalOffices([...regionalOffices, newOffice]);
                setSuccess('Regional office created successfully');
            } else if (modalType === 'edit') {
                const updatedOffices = regionalOffices.map(office =>
                    office.id === editingUser.id
                        ? {
                            ...office,
                            region: formData.region,
                            state: states.find(s => s.id === parseInt(formData.state))?.name || office.state,
                            city: formData.city,
                            name: formData.name,
                            mobileNumber: formData.mobileNumber,
                            email: formData.email,
                            username: formData.username,
                            status: formData.status
                        }
                        : office
                );
                setRegionalOffices(updatedOffices);
                setSuccess('Regional office updated successfully');
            }

            setShowModal(false);
        } catch (error) {
            setError('Operation failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Filter regional offices based on search and status
    const filteredOffices = regionalOffices.filter(office => {
        const matchesSearch = office.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            office.region.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            office.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = filterStatus === 'All' || office.status === filterStatus;
        return matchesSearch && matchesStatus;
    });

    return (
        <Container fluid className="regional-office-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Regional Office Management</h4>
                <Button variant="primary" onClick={handleCreate}>
                    <i className="fas fa-plus me-2"></i>Create Regional Office
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                            <h3 className="text-primary">{regionalOffices.length}</h3>
                            <p className="mb-0">Total Regional Offices</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h3 className="text-success">{regionalOffices.filter(o => o.status === 'Active').length}</h3>
                            <p className="mb-0">Active Offices</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-store fa-2x text-info mb-2"></i>
                            <h3 className="text-info">{regionalOffices.reduce((sum, office) => sum + office.branchCount, 0)}</h3>
                            <p className="mb-0">Total Branches</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-users fa-2x text-warning mb-2"></i>
                            <h3 className="text-warning">{regionalOffices.reduce((sum, office) => sum + office.userCount, 0)}</h3>
                            <p className="mb-0">Total Users</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Search and Filter */}
            <Row className="mb-3">
                <Col md={6}>
                    <Form.Control
                        type="text"
                        placeholder="Search by name, region, or email..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </Col>
                <Col md={3}>
                    <Form.Select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                    >
                        <option value="All">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                    </Form.Select>
                </Col>
                <Col md={3}>
                    <Button variant="outline-secondary" className="w-100">
                        <i className="fas fa-download me-2"></i>Export Data
                    </Button>
                </Col>
            </Row>

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-map-marker-alt me-2"></i>Regional Offices ({filteredOffices.length})
                    </h5>
                </Card.Header>
                <Card.Body>
                    {filteredOffices.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Regional Offices Found</h5>
                            <p className="text-muted">Create your first regional office to get started</p>
                            <Button variant="primary" onClick={handleCreate}>
                                Create First Regional Office
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Region</th>
                                    <th>Location</th>
                                    <th>Manager</th>
                                    <th>Contact</th>
                                    <th>Branches</th>
                                    <th>Users</th>
                                    <th>Revenue</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredOffices.map((office, index) => (
                                    <tr key={office.id}>
                                        <td>{index + 1}</td>
                                        <td>
                                            <strong>{office.region}</strong>
                                        </td>
                                        <td>
                                            {office.city}, {office.state}
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{office.name}</strong>
                                                <br />
                                                <small className="text-muted">{office.username}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <small>{office.mobileNumber}</small>
                                                <br />
                                                <small>{office.email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <Badge bg="info">{office.branchCount}</Badge>
                                        </td>
                                        <td>
                                            <Badge bg="secondary">{office.userCount}</Badge>
                                        </td>
                                        <td>
                                            <strong className="text-success">{office.revenue}</strong>
                                        </td>
                                        <td>
                                            <Badge bg={office.status === 'Active' ? 'success' : 'danger'}>
                                                {office.status}
                                            </Badge>
                                        </td>
                                        <td>
                                            <small>{office.lastLogin}</small>
                                        </td>
                                        <td>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="outline-secondary" size="sm">
                                                    <i className="fas fa-ellipsis-v"></i>
                                                </Dropdown.Toggle>
                                                <Dropdown.Menu>
                                                    <Dropdown.Item onClick={() => handleEdit(office)}>
                                                        <i className="fas fa-edit me-2"></i>Edit
                                                    </Dropdown.Item>
                                                    <Dropdown.Item onClick={() => handleResetPassword(office)}>
                                                        <i className="fas fa-key me-2"></i>Reset Password
                                                    </Dropdown.Item>
                                                    <Dropdown.Divider />
                                                    <Dropdown.Item 
                                                        onClick={() => handleDelete(office.id)}
                                                        className="text-danger"
                                                    >
                                                        <i className="fas fa-trash me-2"></i>Delete
                                                    </Dropdown.Item>
                                                </Dropdown.Menu>
                                            </Dropdown>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Modal for Create/Edit */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        {modalType === 'create' ? 'Create Regional Office' : 
                         modalType === 'edit' ? 'Edit Regional Office' : 'Reset Password'}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {modalType === 'resetPassword' ? (
                        <div className="text-center py-4">
                            <i className="fas fa-key fa-3x text-warning mb-3"></i>
                            <h5>Reset Password for {editingUser?.name}</h5>
                            <p className="text-muted">A new password will be generated and sent to the user's email.</p>
                            <div className="d-flex justify-content-center gap-2">
                                <Button variant="secondary" onClick={() => setShowModal(false)}>
                                    Cancel
                                </Button>
                                <Button variant="warning">
                                    Reset Password
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <Form onSubmit={handleSubmit}>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Country *</Form.Label>
                                        <Form.Select
                                            name="country"
                                            value={formData.country}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select Country</option>
                                            {countries.map(country => (
                                                <option key={country.id} value={country.id}>
                                                    {country.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>State *</Form.Label>
                                        <Form.Select
                                            name="state"
                                            value={formData.state}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select State</option>
                                            {states.map(state => (
                                                <option key={state.id} value={state.id}>
                                                    {state.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>City *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="city"
                                            value={formData.city}
                                            onChange={handleInputChange}
                                            placeholder="Enter city"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Region Name *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="region"
                                            value={formData.region}
                                            onChange={handleInputChange}
                                            placeholder="Enter region name"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Manager Name *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="name"
                                            value={formData.name}
                                            onChange={handleInputChange}
                                            placeholder="Enter manager name"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Mobile Number *</Form.Label>
                                        <Form.Control
                                            type="tel"
                                            name="mobileNumber"
                                            value={formData.mobileNumber}
                                            onChange={handleInputChange}
                                            placeholder="Enter mobile number"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Email *</Form.Label>
                                        <Form.Control
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            placeholder="Enter email"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Username *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="username"
                                            value={formData.username}
                                            onChange={handleInputChange}
                                            placeholder="Enter username"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            {modalType === 'create' && (
                                <Row>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Password *</Form.Label>
                                            <Form.Control
                                                type="password"
                                                name="password"
                                                value={formData.password}
                                                onChange={handleInputChange}
                                                placeholder="Enter password"
                                                required
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Confirm Password *</Form.Label>
                                            <Form.Control
                                                type="password"
                                                name="confirmPassword"
                                                value={formData.confirmPassword}
                                                onChange={handleInputChange}
                                                placeholder="Confirm password"
                                                required
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                            )}
                            <Form.Group className="mb-3">
                                <Form.Label>Status</Form.Label>
                                <Form.Select
                                    name="status"
                                    value={formData.status}
                                    onChange={handleInputChange}
                                >
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </Form.Select>
                            </Form.Group>
                        </Form>
                    )}
                </Modal.Body>
                {modalType !== 'resetPassword' && (
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" onClick={handleSubmit} disabled={loading}>
                            {loading ? 'Processing...' : (modalType === 'create' ? 'Create' : 'Update')}
                        </Button>
                    </Modal.Footer>
                )}
            </Modal>
        </Container>
    );
};

export default RegionalOfficeManagement;
