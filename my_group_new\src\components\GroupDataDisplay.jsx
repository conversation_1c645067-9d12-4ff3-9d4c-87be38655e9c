import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON> } from 'react-bootstrap';
import './GroupDataDisplay.css';

const GroupDataDisplay = ({ groupData, loading, error, onLoginRequired }) => {
  if (loading) {
    return (
      <div className="loading-container">
        <Spinner animation="border" variant="primary" />
        <p className="mt-2">Loading groups...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger" className="m-3">
        <Alert.Heading>Error Loading Data</Alert.Heading>
        <p>{error}</p>
      </Alert>
    );
  }

  const handleCategoryClick = (category) => {
    if (onLoginRequired()) {
      console.log('Navigating to:', category.end_point);
      // Implement navigation logic here
    }
  };

  return (
    <Container fluid className="group-data-display">
      {groupData.map((group) => (
        <div key={group.id} className="group-section mb-4">
          <div className="group-header">
            <div className="group-info">
              <img 
                src={group.logo} 
                alt={group.name}
                className="group-main-logo"
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
              <div className="group-details">
                <h4 className="group-title">{group.group_name}</h4>
                <p className="group-subtitle">{group.name}</p>
              </div>
            </div>
            <div 
              className="group-badge"
              style={{ 
                backgroundColor: group.background_color,
                color: group.color 
              }}
            >
              <span className="group-badge-icon">{group.icon}</span>
            </div>
          </div>

          {group.categories && group.categories.length > 0 && (
            <Row className="categories-grid">
              {group.categories.map((category) => (
                <Col xs={6} sm={4} md={3} key={category.id} className="mb-3">
                  <Card 
                    className="category-card h-100"
                    onClick={() => handleCategoryClick(category)}
                  >
                    <Card.Body className="text-center">
                      <div className="category-icon">
                        {category.icon}
                      </div>
                      <Card.Title className="category-title">
                        {category.display_name}
                      </Card.Title>
                      <Card.Text className="category-description">
                        {category.name}
                      </Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          )}

          {(!group.categories || group.categories.length === 0) && (
            <div className="no-categories">
              <p className="text-muted">No categories available for this group.</p>
            </div>
          )}
        </div>
      ))}

      {groupData.length === 0 && (
        <div className="no-groups">
          <div className="text-center py-5">
            <h5>No Groups Available</h5>
            <p className="text-muted">Please check back later for updates.</p>
          </div>
        </div>
      )}
    </Container>
  );
};

export default GroupDataDisplay;
