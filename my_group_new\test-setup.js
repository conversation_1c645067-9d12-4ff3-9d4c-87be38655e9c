// Simple test script to verify the setup
import fs from 'fs';
import path from 'path';

console.log('Testing My Group New Application Setup...');

// Check if we're in the right directory

console.log('\n1. Checking project structure...');

const requiredFiles = [
  'package.json',
  'vite.config.js',
  'src/App.jsx',
  'src/main.jsx',
  'src/components/HomeScreen.jsx',
  'src/components/Header.jsx',
  'src/components/LoginModal.jsx',
  'src/contexts/AuthContext.jsx',
  'backend/package.json',
  'backend/server.js',
  'backend/models/User.js',
  'backend/controllers/authController.js'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✓ ${file} exists`);
  } else {
    console.log(`✗ ${file} missing`);
    allFilesExist = false;
  }
});

console.log('\n2. Checking package.json dependencies...');

try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = ['react', 'react-dom', 'react-bootstrap', 'react-router-dom', 'react-icons', 'bootstrap'];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✓ ${dep} is listed in dependencies`);
    } else {
      console.log(`✗ ${dep} missing from dependencies`);
      allFilesExist = false;
    }
  });
} catch (error) {
  console.log('✗ Error reading package.json:', error.message);
  allFilesExist = false;
}

console.log('\n3. Checking backend dependencies...');

try {
  const backendPackageJson = JSON.parse(fs.readFileSync('backend/package.json', 'utf8'));
  const requiredBackendDeps = ['express', 'cors', 'pg', 'bcryptjs', 'jsonwebtoken', 'dotenv'];
  
  requiredBackendDeps.forEach(dep => {
    if (backendPackageJson.dependencies && backendPackageJson.dependencies[dep]) {
      console.log(`✓ ${dep} is listed in backend dependencies`);
    } else {
      console.log(`✗ ${dep} missing from backend dependencies`);
      allFilesExist = false;
    }
  });
} catch (error) {
  console.log('✗ Error reading backend/package.json:', error.message);
  allFilesExist = false;
}

console.log('\n4. Summary:');
if (allFilesExist) {
  console.log('✓ All required files and dependencies are present!');
  console.log('\nNext steps:');
  console.log('1. Set up PostgreSQL database with schema from ../database/schema_new.sql');
  console.log('2. Update backend/.env with your database credentials');
  console.log('3. Run "npm run dev" to start the frontend');
  console.log('4. Run "npm run dev" in the backend directory to start the API server');
  console.log('5. Access the application at http://localhost:3000');
} else {
  console.log('✗ Some files or dependencies are missing. Please check the setup.');
}

console.log('\nTest completed.');
