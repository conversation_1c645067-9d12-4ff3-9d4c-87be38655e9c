import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import authAPI from '../api/authAPI';
import '../styles/BackendDashboardLogin.css';

const BackendDashboardLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const navigate = useNavigate();
  const location = useLocation();
  const { login, currentUser } = useAuth();

  // Backend roles that can access this login
  const backendRoles = ['admin', 'corporate', 'headoffice', 'regional', 'branch'];

  // Redirect if already logged in with backend role
  useEffect(() => {
    if (currentUser && backendRoles.includes(currentUser.role)) {
      redirectToDashboard(currentUser.role);
    }
  }, [currentUser]);

  const redirectToDashboard = (role) => {
    const dashboardRoutes = {
      'admin': '/admin/dashboard',
      'corporate': '/corporate/dashboard',
      'headoffice': '/headoffice/dashboard',
      'regional': '/regional/dashboard',
      'branch': '/branch/dashboard'
    };

    const redirectTo = dashboardRoutes[role] || '/admin/dashboard';
    navigate(redirectTo, { replace: true });
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.username.trim()) {
        throw new Error('Username is required');
      }
      if (!formData.password) {
        throw new Error('Password is required');
      }

      // Attempt login
      const result = await authAPI.login(
        formData.username.trim(),
        formData.password,
        navigator.userAgent,
        '127.0.0.1'
      );

      if (result.success) {
        // Check if user has backend role access
        if (!backendRoles.includes(result.user.role)) {
          throw new Error(`Access denied. This login is for backend dashboard users only. Your role: ${result.user.role}`);
        }

        // Save remember me preference
        if (formData.rememberMe) {
          localStorage.setItem('rememberMe', 'true');
          localStorage.setItem('savedUsername', formData.username);
        } else {
          localStorage.removeItem('rememberMe');
          localStorage.removeItem('savedUsername');
        }

        // Use auth context login
        await login(formData.username, formData.password);
        
        // Redirect to appropriate dashboard
        redirectToDashboard(result.user.role);
      } else {
        throw new Error(result.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'An error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  // Load saved username if remember me was checked
  useEffect(() => {
    const rememberMe = localStorage.getItem('rememberMe');
    const savedUsername = localStorage.getItem('savedUsername');
    
    if (rememberMe === 'true' && savedUsername) {
      setFormData(prev => ({
        ...prev,
        username: savedUsername,
        rememberMe: true
      }));
    }
  }, []);

  return (
    <div className="backend-login-container">
      <div className="login-background">
        <div className="background-pattern"></div>
      </div>
      
      <div className="login-content">
        <div className="login-card">
          <div className="login-header">
            <div className="logo-section">
              <div className="logo">
                <span className="logo-icon">🏢</span>
                <span className="logo-text">MyGroup</span>
              </div>
              <h1>Backend Dashboard</h1>
              <p>Login for Admin, Corporate, Head Office, Regional & Branch</p>
            </div>
          </div>

          {/* Role Information */}
          <div className="role-info">
            <h3>Authorized Roles:</h3>
            <div className="role-list">
              <span className="role-item admin">👨‍💼 Admin</span>
              <span className="role-item corporate">🏢 Corporate</span>
              <span className="role-item headoffice">🏛️ Head Office</span>
              <span className="role-item regional">🌍 Regional</span>
              <span className="role-item branch">🏪 Branch</span>
            </div>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="login-form">
            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="username">Username</label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter your username"
                required
                disabled={loading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                required
                disabled={loading}
              />
            </div>

            <div className="form-options">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  disabled={loading}
                />
                <span className="checkbox-custom"></span>
                Remember me
              </label>
              
              <button type="button" className="forgot-password-link">
                Forgot Password?
              </button>
            </div>

            <button 
              type="submit" 
              className="login-button"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="loading-spinner"></span>
                  Signing in...
                </>
              ) : (
                'Sign In to Dashboard'
              )}
            </button>
          </form>

          {/* Default Credentials Info */}
          <div className="default-credentials">
            <h4>Default Admin Credentials:</h4>
            <div className="credentials-info">
              <p><strong>Username:</strong> admin</p>
              <p><strong>Password:</strong> admin123</p>
            </div>
            <small>Use these credentials for initial admin access</small>
          </div>

          <div className="login-footer">
            <div className="navigation-links">
              <a href="/">← Back to Customer Site</a>
              <a href="/partner">Partner Login →</a>
            </div>
            <div className="system-info">
              <small>MyGroup Backend Dashboard v2.0</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackendDashboardLogin;
