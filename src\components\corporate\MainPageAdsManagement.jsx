import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Modal, Alert, Badge, ProgressBar } from 'react-bootstrap';

const MainPageAdsManagement = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [editingAd, setEditingAd] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);

    // Form data
    const [formData, setFormData] = useState({
        adPosition: '',
        adTitle: '',
        adDescription: '',
        adFile: null,
        adType: 'image',
        linkUrl: '',
        startDate: '',
        endDate: '',
        status: 'Active'
    });

    // Main page ads data (Ads-1, Ads-2, Ads-3)
    const [mainPageAds, setMainPageAds] = useState([
        {
            id: 1,
            adPosition: 'Ads-1',
            adTitle: 'Premium Product Showcase',
            adDescription: 'Featured product advertisement in prime position',
            adFile: '/images/ads/main-ad-1.jpg',
            adType: 'image',
            linkUrl: 'https://example.com/product1',
            startDate: '2024-01-15',
            endDate: '2024-02-15',
            status: 'Active',
            views: 5420,
            clicks: 234,
            position: 1,
            createdAt: '2024-01-15'
        },
        {
            id: 2,
            adPosition: 'Ads-2',
            adTitle: 'Service Promotion',
            adDescription: 'Special offer on professional services',
            adFile: '/images/ads/main-ad-2.jpg',
            adType: 'image',
            linkUrl: 'https://example.com/services',
            startDate: '2024-01-10',
            endDate: '2024-02-10',
            status: 'Active',
            views: 3890,
            clicks: 167,
            position: 2,
            createdAt: '2024-01-10'
        },
        {
            id: 3,
            adPosition: 'Ads-3',
            adTitle: 'Brand Partnership',
            adDescription: 'Collaborative brand advertisement',
            adFile: '/images/ads/main-ad-3.mp4',
            adType: 'video',
            linkUrl: 'https://example.com/partnership',
            startDate: '2024-01-08',
            endDate: '2024-01-25',
            status: 'Expired',
            views: 2156,
            clicks: 89,
            position: 3,
            createdAt: '2024-01-08'
        }
    ]);

    const adPositions = ['Ads-1', 'Ads-2', 'Ads-3'];
    const adTypes = ['image', 'video', 'gif', 'banner'];

    useEffect(() => {
        fetchMainPageAds();
        updateAdStatuses();
    }, []);

    const fetchMainPageAds = async () => {
        setLoading(true);
        try {
            // API call to fetch main page ads
            // const response = await fetch('/api/main-page-ads');
            // const data = await response.json();
            // setMainPageAds(data);
        } catch (error) {
            console.error('Error fetching main page ads:', error);
            setError('Failed to fetch main page ads');
        } finally {
            setLoading(false);
        }
    };

    const updateAdStatuses = () => {
        const today = new Date().toISOString().split('T')[0];
        setMainPageAds(prevAds => 
            prevAds.map(ad => {
                if (ad.endDate < today && ad.status === 'Active') {
                    return { ...ad, status: 'Expired' };
                }
                return ad;
            })
        );
    };

    const handleCreate = (position = '') => {
        setEditingAd(null);
        setFormData({
            adPosition: position,
            adTitle: '',
            adDescription: '',
            adFile: null,
            adType: 'image',
            linkUrl: '',
            startDate: '',
            endDate: '',
            status: 'Active'
        });
        setShowModal(true);
    };

    const handleEdit = (ad) => {
        setEditingAd(ad);
        setFormData({
            adPosition: ad.adPosition,
            adTitle: ad.adTitle,
            adDescription: ad.adDescription,
            adFile: null, // File will be handled separately
            adType: ad.adType,
            linkUrl: ad.linkUrl,
            startDate: ad.startDate,
            endDate: ad.endDate,
            status: ad.status
        });
        setShowModal(true);
    };

    const handleDelete = (adId) => {
        if (window.confirm('Are you sure you want to delete this main page ad?')) {
            setMainPageAds(mainPageAds.filter(ad => ad.id !== adId));
            setSuccess('Main page ad deleted successfully');
        }
    };

    const toggleAdStatus = (adId) => {
        setMainPageAds(mainPageAds.map(ad => 
            ad.id === adId 
                ? { ...ad, status: ad.status === 'Active' ? 'Inactive' : 'Active' }
                : ad
        ));
        setSuccess('Ad status updated successfully');
    };

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = {
                image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                video: ['video/mp4', 'video/webm', 'video/ogg'],
                gif: ['image/gif'],
                banner: ['image/jpeg', 'image/png', 'image/webp']
            };

            const isValidType = allowedTypes[formData.adType]?.includes(file.type);
            if (!isValidType) {
                setError(`Invalid file type for ${formData.adType}. Please select a valid file.`);
                return;
            }

            // Validate file size (max 15MB for main page ads)
            if (file.size > 15 * 1024 * 1024) {
                setError('File size must be less than 15MB');
                return;
            }

            setFormData({
                ...formData,
                adFile: file
            });
        }
    };

    const simulateFileUpload = () => {
        return new Promise((resolve) => {
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                setUploadProgress(progress);
                if (progress >= 100) {
                    clearInterval(interval);
                    resolve('/uploads/main-ads/uploaded-file.jpg');
                }
            }, 200);
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setUploadProgress(0);

        try {
            let adFileUrl = editingAd?.adFile || '';

            // Upload file if new file is selected
            if (formData.adFile) {
                adFileUrl = await simulateFileUpload();
            }

            const positionNumber = parseInt(formData.adPosition.split('-')[1]);
            
            const adData = {
                id: editingAd ? editingAd.id : Date.now(),
                adPosition: formData.adPosition,
                adTitle: formData.adTitle,
                adDescription: formData.adDescription,
                adFile: adFileUrl,
                adType: formData.adType,
                linkUrl: formData.linkUrl,
                startDate: formData.startDate,
                endDate: formData.endDate,
                status: formData.status,
                position: positionNumber,
                views: editingAd ? editingAd.views : 0,
                clicks: editingAd ? editingAd.clicks : 0,
                createdAt: editingAd ? editingAd.createdAt : new Date().toISOString().split('T')[0]
            };

            if (editingAd) {
                setMainPageAds(mainPageAds.map(ad => 
                    ad.id === editingAd.id ? adData : ad
                ));
                setSuccess('Main page ad updated successfully');
            } else {
                setMainPageAds([...mainPageAds, adData]);
                setSuccess('Main page ad created successfully');
            }

            setShowModal(false);
            setFormData({
                adPosition: '',
                adTitle: '',
                adDescription: '',
                adFile: null,
                adType: 'image',
                linkUrl: '',
                startDate: '',
                endDate: '',
                status: 'Active'
            });
            setUploadProgress(0);
        } catch (error) {
            setError('Failed to save main page ad');
        } finally {
            setLoading(false);
        }
    };

    const getStatusBadgeColor = (status) => {
        switch (status) {
            case 'Active': return 'success';
            case 'Inactive': return 'secondary';
            case 'Expired': return 'danger';
            default: return 'secondary';
        }
    };

    const getAdTypeIcon = (type) => {
        switch (type) {
            case 'image': return 'fas fa-image';
            case 'video': return 'fas fa-video';
            case 'gif': return 'fas fa-file-image';
            case 'banner': return 'fas fa-rectangle-ad';
            default: return 'fas fa-file';
        }
    };

    const getPositionColor = (position) => {
        switch (position) {
            case 'Ads-1': return 'primary';
            case 'Ads-2': return 'success';
            case 'Ads-3': return 'warning';
            default: return 'secondary';
        }
    };

    const renderAdCard = (position) => {
        const ad = mainPageAds.find(a => a.adPosition === position);
        
        return (
            <Card className="h-100 ad-position-card">
                <Card.Header className={`bg-${getPositionColor(position)} text-white`}>
                    <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">{position}</h6>
                        <Button 
                            variant="light" 
                            size="sm"
                            onClick={() => ad ? handleEdit(ad) : handleCreate(position)}
                        >
                            <i className={`fas ${ad ? 'fa-edit' : 'fa-plus'}`}></i>
                        </Button>
                    </div>
                </Card.Header>
                <Card.Body>
                    {ad ? (
                        <div>
                            <div className="ad-preview mb-3">
                                {ad.adType === 'video' ? (
                                    <div className="video-placeholder bg-light p-3 text-center">
                                        <i className="fas fa-video fa-2x text-muted"></i>
                                        <div>Video Ad</div>
                                    </div>
                                ) : (
                                    <div className="image-placeholder bg-light p-3 text-center">
                                        <i className="fas fa-image fa-2x text-muted"></i>
                                        <div>Image Ad</div>
                                    </div>
                                )}
                            </div>
                            <h6>{ad.adTitle}</h6>
                            <p className="text-muted small">{ad.adDescription}</p>
                            <div className="ad-stats">
                                <small>
                                    <div>Views: {ad.views.toLocaleString()}</div>
                                    <div>Clicks: {ad.clicks}</div>
                                    <div>CTR: {ad.views > 0 ? ((ad.clicks / ad.views) * 100).toFixed(2) : 0}%</div>
                                </small>
                            </div>
                            <div className="mt-2">
                                <Badge bg={getStatusBadgeColor(ad.status)}>{ad.status}</Badge>
                            </div>
                            <div className="mt-2">
                                <Button
                                    variant="outline-primary"
                                    size="sm"
                                    className="me-1"
                                    onClick={() => handleEdit(ad)}
                                >
                                    <i className="fas fa-edit"></i>
                                </Button>
                                <Button
                                    variant="outline-danger"
                                    size="sm"
                                    onClick={() => handleDelete(ad.id)}
                                >
                                    <i className="fas fa-trash"></i>
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-4">
                            <i className="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                            <h6 className="text-muted">No Ad Assigned</h6>
                            <Button 
                                variant="primary" 
                                size="sm"
                                onClick={() => handleCreate(position)}
                            >
                                Create Ad
                            </Button>
                        </div>
                    )}
                </Card.Body>
            </Card>
        );
    };

    return (
        <Container fluid className="main-page-ads-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Main Page Ads Management</h4>
                <Button variant="primary" onClick={() => handleCreate()}>
                    <i className="fas fa-plus me-2"></i>Create Main Page Ad
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-primary">{mainPageAds.length}</h3>
                            <p className="mb-0">Total Ads</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-success">
                                {mainPageAds.filter(ad => ad.status === 'Active').length}
                            </h3>
                            <p className="mb-0">Active Ads</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-info">
                                {mainPageAds.reduce((sum, ad) => sum + ad.views, 0).toLocaleString()}
                            </h3>
                            <p className="mb-0">Total Views</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center">
                        <Card.Body>
                            <h3 className="text-warning">
                                {mainPageAds.reduce((sum, ad) => sum + ad.clicks, 0)}
                            </h3>
                            <p className="mb-0">Total Clicks</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Ad Positions Layout */}
            <Row className="mb-4">
                <Col md={4}>
                    {renderAdCard('Ads-1')}
                </Col>
                <Col md={4}>
                    {renderAdCard('Ads-2')}
                </Col>
                <Col md={4}>
                    {renderAdCard('Ads-3')}
                </Col>
            </Row>

            {/* Main Page Layout Preview */}
            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-desktop me-2"></i>Main Page Layout Preview
                    </h5>
                </Card.Header>
                <Card.Body>
                    <div className="layout-preview">
                        <div className="preview-container" style={{ border: '2px dashed #ddd', padding: '20px', borderRadius: '8px' }}>
                            <div className="header-section mb-3 p-2 bg-light">
                                <small className="text-muted">Header Section</small>
                            </div>
                            
                            <Row>
                                <Col md={8}>
                                    <div className="content-section p-3 bg-light mb-3">
                                        <small className="text-muted">Main Content Area</small>
                                    </div>
                                </Col>
                                <Col md={4}>
                                    <div className="ads-section">
                                        {adPositions.map(position => {
                                            const ad = mainPageAds.find(a => a.adPosition === position);
                                            return (
                                                <div key={position} className="ad-slot mb-2 p-2 border">
                                                    <small className="text-muted">{position}</small>
                                                    {ad && (
                                                        <div className="mt-1">
                                                            <Badge bg={getPositionColor(position)} className="me-1">
                                                                {ad.adTitle}
                                                            </Badge>
                                                            <Badge bg={getStatusBadgeColor(ad.status)}>
                                                                {ad.status}
                                                            </Badge>
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </Col>
                            </Row>
                            
                            <div className="footer-section mt-3 p-2 bg-light">
                                <small className="text-muted">Footer Section</small>
                            </div>
                        </div>
                    </div>
                </Card.Body>
            </Card>

            {/* Create/Edit Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="fas fa-desktop me-2"></i>
                        {editingAd ? 'Edit Main Page Ad' : 'Create Main Page Ad'}
                    </Modal.Title>
                </Modal.Header>
                <Form onSubmit={handleSubmit}>
                    <Modal.Body>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Position *</Form.Label>
                                    <Form.Select
                                        value={formData.adPosition}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adPosition: e.target.value
                                        })}
                                        required
                                    >
                                        <option value="">Select Position</option>
                                        {adPositions.map(position => (
                                            <option key={position} value={position}>
                                                {position}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Type *</Form.Label>
                                    <Form.Select
                                        value={formData.adType}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adType: e.target.value
                                        })}
                                        required
                                    >
                                        {adTypes.map(type => (
                                            <option key={type} value={type}>
                                                {type.charAt(0).toUpperCase() + type.slice(1)}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Title *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter ad title"
                                        value={formData.adTitle}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adTitle: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad Description</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={3}
                                        placeholder="Enter ad description"
                                        value={formData.adDescription}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            adDescription: e.target.value
                                        })}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Link URL</Form.Label>
                                    <Form.Control
                                        type="url"
                                        placeholder="https://example.com"
                                        value={formData.linkUrl}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            linkUrl: e.target.value
                                        })}
                                    />
                                    <Form.Text className="text-muted">
                                        URL to redirect when ad is clicked
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Ad File *</Form.Label>
                                    <Form.Control
                                        type="file"
                                        accept={
                                            formData.adType === 'video' ? 'video/*' :
                                            formData.adType === 'gif' ? 'image/gif' : 'image/*'
                                        }
                                        onChange={handleFileChange}
                                        required={!editingAd}
                                    />
                                    <Form.Text className="text-muted">
                                        Max file size: 15MB. Supported formats: {
                                            formData.adType === 'video' ? 'MP4, WebM, OGG' :
                                            formData.adType === 'gif' ? 'GIF' : 'JPEG, PNG, WebP'
                                        }
                                    </Form.Text>
                                    {uploadProgress > 0 && uploadProgress < 100 && (
                                        <ProgressBar 
                                            now={uploadProgress} 
                                            label={`${uploadProgress}%`}
                                            className="mt-2"
                                        />
                                    )}
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Start Date *</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={formData.startDate}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            startDate: e.target.value
                                        })}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>End Date *</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={formData.endDate}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            endDate: e.target.value
                                        })}
                                        required
                                        min={formData.startDate}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={4}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Status</Form.Label>
                                    <Form.Select
                                        value={formData.status}
                                        onChange={(e) => setFormData({
                                            ...formData,
                                            status: e.target.value
                                        })}
                                    >
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <div className="bg-light p-3 rounded">
                            <small className="text-muted">
                                <i className="fas fa-info-circle me-1"></i>
                                <strong>Note:</strong> Main page ads are displayed in prominent positions on the homepage. 
                                Ads-1 has the highest visibility, followed by Ads-2 and Ads-3.
                            </small>
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" type="submit" disabled={loading}>
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" />
                                    {uploadProgress > 0 ? `Uploading... ${uploadProgress}%` : 'Processing...'}
                                </>
                            ) : (
                                <>
                                    <i className={`fas ${editingAd ? 'fa-save' : 'fa-plus'} me-2`}></i>
                                    {editingAd ? 'Update Ad' : 'Create Ad'}
                                </>
                            )}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
        </Container>
    );
};

export default MainPageAdsManagement;
