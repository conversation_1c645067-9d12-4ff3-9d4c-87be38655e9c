-- Migration: Create my_permissions table
-- Description: Defines available permissions in the system
-- Created: 2024-01-18

CREATE TABLE my_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    permission_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default permissions
INSERT INTO my_permissions (permission_name, permission_code, description, module) VALUES
-- User Management
('Create Users', 'user.create', 'Create new users', 'user_management'),
('Read Users', 'user.read', 'View user information', 'user_management'),
('Update Users', 'user.update', 'Update user information', 'user_management'),
('Delete Users', 'user.delete', 'Delete users', 'user_management'),

-- Organization Management
('Manage Organizations', 'organization.manage', 'Manage organization settings', 'organization'),
('Manage Head Offices', 'head_office.manage', 'Manage head office settings', 'head_office'),
('Manage Regional Offices', 'regional.manage', 'Manage regional office settings', 'regional'),
('Manage Branch Offices', 'branch.manage', 'Manage branch office settings', 'branch'),

-- Transaction Management
('Process Transactions', 'transaction.process', 'Process financial transactions', 'transactions'),
('View Transactions', 'transaction.view', 'View transaction history', 'transactions'),
('Approve Transactions', 'transaction.approve', 'Approve pending transactions', 'transactions'),

-- Report Management
('Generate Reports', 'report.generate', 'Generate system reports', 'reports'),
('View Reports', 'report.view', 'View system reports', 'reports'),
('Export Data', 'data.export', 'Export system data', 'reports'),

-- System Administration
('System Settings', 'system.settings', 'Manage system settings', 'system'),
('View Audit Logs', 'audit.view', 'View system audit logs', 'system'),
('Manage Permissions', 'permission.manage', 'Manage user permissions', 'system');

-- Create indexes
CREATE INDEX idx_permissions_code ON my_permissions(permission_code);
CREATE INDEX idx_permissions_module ON my_permissions(module);
CREATE INDEX idx_permissions_active ON my_permissions(is_active);
