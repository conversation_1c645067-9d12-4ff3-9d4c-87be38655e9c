import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Modal, Alert, Badge, Dropdown } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';

const BranchOfficeManagement = () => {
    const { currentUser } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState(''); // 'create', 'edit', 'resetPassword'
    const [editingUser, setEditingUser] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('All');
    const [filterRegion, setFilterRegion] = useState('All');

    // Sample data - replace with actual API calls
    const [branchOffices, setBranchOffices] = useState([
        {
            id: 1,
            branchCode: 'BR001',
            branchName: 'Downtown Branch',
            region: 'North Region',
            state: 'California',
            city: 'San Francisco',
            address: '123 Main St, Downtown',
            managerName: 'Alice Johnson',
            mobileNumber: '******-0201',
            email: '<EMAIL>',
            username: 'alice.johnson',
            status: 'Active',
            userCount: 45,
            revenue: '$25,000',
            lastLogin: '2024-01-18 08:15 AM',
            createdAt: '2023-08-10',
            services: ['Banking', 'Loans', 'Insurance']
        },
        {
            id: 2,
            branchCode: 'BR002',
            branchName: 'Westside Branch',
            region: 'North Region',
            state: 'California',
            city: 'Los Angeles',
            address: '456 West Ave, Westside',
            managerName: 'Bob Smith',
            mobileNumber: '******-0202',
            email: '<EMAIL>',
            username: 'bob.smith',
            status: 'Active',
            userCount: 32,
            revenue: '$18,500',
            lastLogin: '2024-01-17 16:30 PM',
            createdAt: '2023-09-15',
            services: ['Banking', 'Investments']
        },
        {
            id: 3,
            branchCode: 'BR003',
            branchName: 'Central Branch',
            region: 'South Region',
            state: 'Texas',
            city: 'Houston',
            address: '789 Central Blvd, Central',
            managerName: 'Carol Davis',
            mobileNumber: '******-0203',
            email: '<EMAIL>',
            username: 'carol.davis',
            status: 'Inactive',
            userCount: 28,
            revenue: '$15,200',
            lastLogin: '2024-01-12 10:45 AM',
            createdAt: '2023-07-20',
            services: ['Banking', 'Loans']
        }
    ]);

    // Sample regions data
    const [regions] = useState([
        { id: 1, name: 'North Region' },
        { id: 2, name: 'South Region' },
        { id: 3, name: 'East Region' },
        { id: 4, name: 'West Region' }
    ]);

    // Sample countries/states data
    const [countries] = useState([
        { id: 1, name: 'United States' },
        { id: 2, name: 'Canada' },
        { id: 3, name: 'United Kingdom' }
    ]);

    const [states] = useState([
        { id: 1, name: 'California', countryId: 1 },
        { id: 2, name: 'Texas', countryId: 1 },
        { id: 3, name: 'New York', countryId: 1 },
        { id: 4, name: 'Ontario', countryId: 2 },
        { id: 5, name: 'London', countryId: 3 }
    ]);

    // Available services
    const [availableServices] = useState([
        'Banking', 'Loans', 'Insurance', 'Investments', 'Credit Cards', 'Mortgages'
    ]);

    // Form data
    const [formData, setFormData] = useState({
        branchCode: '',
        branchName: '',
        region: '',
        country: '',
        state: '',
        city: '',
        address: '',
        managerName: '',
        mobileNumber: '',
        email: '',
        username: '',
        password: '',
        confirmPassword: '',
        status: 'Active',
        services: []
    });

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        
        if (type === 'checkbox') {
            if (name === 'services') {
                setFormData(prev => ({
                    ...prev,
                    services: checked 
                        ? [...prev.services, value]
                        : prev.services.filter(service => service !== value)
                }));
            }
        } else {
            setFormData({
                ...formData,
                [name]: value
            });
        }
    };

    const generateBranchCode = () => {
        const lastBranch = branchOffices[branchOffices.length - 1];
        const lastNumber = lastBranch ? parseInt(lastBranch.branchCode.slice(2)) : 0;
        return `BR${String(lastNumber + 1).padStart(3, '0')}`;
    };

    const handleCreate = () => {
        setModalType('create');
        setEditingUser(null);
        setFormData({
            branchCode: generateBranchCode(),
            branchName: '',
            region: '',
            country: '',
            state: '',
            city: '',
            address: '',
            managerName: '',
            mobileNumber: '',
            email: '',
            username: '',
            password: '',
            confirmPassword: '',
            status: 'Active',
            services: []
        });
        setShowModal(true);
    };

    const handleEdit = (branch) => {
        setModalType('edit');
        setEditingUser(branch);
        setFormData({
            branchCode: branch.branchCode,
            branchName: branch.branchName,
            region: branch.region,
            country: '1', // Default to first country
            state: '1', // Default to first state
            city: branch.city,
            address: branch.address,
            managerName: branch.managerName,
            mobileNumber: branch.mobileNumber,
            email: branch.email,
            username: branch.username,
            password: '',
            confirmPassword: '',
            status: branch.status,
            services: branch.services || []
        });
        setShowModal(true);
    };

    const handleResetPassword = (branch) => {
        setModalType('resetPassword');
        setEditingUser(branch);
        setShowModal(true);
    };

    const handleDelete = async (branchId) => {
        if (window.confirm('Are you sure you want to delete this branch office?')) {
            try {
                setLoading(true);
                // API call would go here
                setBranchOffices(branchOffices.filter(branch => branch.id !== branchId));
                setSuccess('Branch office deleted successfully');
            } catch (error) {
                setError('Failed to delete branch office');
            } finally {
                setLoading(false);
            }
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            if (modalType === 'create') {
                const newBranch = {
                    id: Date.now(),
                    branchCode: formData.branchCode,
                    branchName: formData.branchName,
                    region: formData.region,
                    state: states.find(s => s.id === parseInt(formData.state))?.name || '',
                    city: formData.city,
                    address: formData.address,
                    managerName: formData.managerName,
                    mobileNumber: formData.mobileNumber,
                    email: formData.email,
                    username: formData.username,
                    status: formData.status,
                    userCount: 0,
                    revenue: '$0',
                    createdAt: new Date().toISOString().split('T')[0],
                    lastLogin: 'Never',
                    services: formData.services
                };

                setBranchOffices([...branchOffices, newBranch]);
                setSuccess('Branch office created successfully');
            } else if (modalType === 'edit') {
                const updatedBranches = branchOffices.map(branch =>
                    branch.id === editingUser.id
                        ? {
                            ...branch,
                            branchCode: formData.branchCode,
                            branchName: formData.branchName,
                            region: formData.region,
                            state: states.find(s => s.id === parseInt(formData.state))?.name || branch.state,
                            city: formData.city,
                            address: formData.address,
                            managerName: formData.managerName,
                            mobileNumber: formData.mobileNumber,
                            email: formData.email,
                            username: formData.username,
                            status: formData.status,
                            services: formData.services
                        }
                        : branch
                );
                setBranchOffices(updatedBranches);
                setSuccess('Branch office updated successfully');
            }

            setShowModal(false);
        } catch (error) {
            setError('Operation failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Filter branch offices based on search, status, and region
    const filteredBranches = branchOffices.filter(branch => {
        const matchesSearch = branch.branchName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            branch.managerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            branch.branchCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            branch.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = filterStatus === 'All' || branch.status === filterStatus;
        const matchesRegion = filterRegion === 'All' || branch.region === filterRegion;
        return matchesSearch && matchesStatus && matchesRegion;
    });

    return (
        <Container fluid className="branch-office-management">
            {success && (
                <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                    {success}
                </Alert>
            )}
            {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                    {error}
                </Alert>
            )}

            <div className="d-flex justify-content-between align-items-center mb-4">
                <h4>Branch Office Management</h4>
                <Button variant="primary" onClick={handleCreate}>
                    <i className="fas fa-plus me-2"></i>Create Branch Office
                </Button>
            </div>

            {/* Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-store fa-2x text-primary mb-2"></i>
                            <h3 className="text-primary">{branchOffices.length}</h3>
                            <p className="mb-0">Total Branches</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h3 className="text-success">{branchOffices.filter(b => b.status === 'Active').length}</h3>
                            <p className="mb-0">Active Branches</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-users fa-2x text-info mb-2"></i>
                            <h3 className="text-info">{branchOffices.reduce((sum, branch) => sum + branch.userCount, 0)}</h3>
                            <p className="mb-0">Total Users</p>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-center dashboard-card">
                        <Card.Body>
                            <i className="fas fa-dollar-sign fa-2x text-warning mb-2"></i>
                            <h3 className="text-warning">
                                ${branchOffices.reduce((sum, branch) => {
                                    const revenue = parseFloat(branch.revenue.replace('$', '').replace(',', ''));
                                    return sum + revenue;
                                }, 0).toLocaleString()}
                            </h3>
                            <p className="mb-0">Total Revenue</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Search and Filter */}
            <Row className="mb-3">
                <Col md={4}>
                    <Form.Control
                        type="text"
                        placeholder="Search by branch name, manager, code, or email..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </Col>
                <Col md={2}>
                    <Form.Select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                    >
                        <option value="All">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                    </Form.Select>
                </Col>
                <Col md={2}>
                    <Form.Select
                        value={filterRegion}
                        onChange={(e) => setFilterRegion(e.target.value)}
                    >
                        <option value="All">All Regions</option>
                        {regions.map(region => (
                            <option key={region.id} value={region.name}>
                                {region.name}
                            </option>
                        ))}
                    </Form.Select>
                </Col>
                <Col md={2}>
                    <Button variant="outline-secondary" className="w-100">
                        <i className="fas fa-download me-2"></i>Export
                    </Button>
                </Col>
                <Col md={2}>
                    <Button variant="outline-info" className="w-100">
                        <i className="fas fa-chart-bar me-2"></i>Analytics
                    </Button>
                </Col>
            </Row>

            <Card>
                <Card.Header>
                    <h5 className="mb-0">
                        <i className="fas fa-store me-2"></i>Branch Offices ({filteredBranches.length})
                    </h5>
                </Card.Header>
                <Card.Body>
                    {filteredBranches.length === 0 ? (
                        <div className="text-center py-5">
                            <i className="fas fa-store fa-3x text-muted mb-3"></i>
                            <h5 className="text-muted">No Branch Offices Found</h5>
                            <p className="text-muted">Create your first branch office to get started</p>
                            <Button variant="primary" onClick={handleCreate}>
                                Create First Branch Office
                            </Button>
                        </div>
                    ) : (
                        <Table responsive hover>
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Branch Code</th>
                                    <th>Branch Name</th>
                                    <th>Region</th>
                                    <th>Location</th>
                                    <th>Manager</th>
                                    <th>Contact</th>
                                    <th>Users</th>
                                    <th>Revenue</th>
                                    <th>Services</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredBranches.map((branch, index) => (
                                    <tr key={branch.id}>
                                        <td>{index + 1}</td>
                                        <td>
                                            <Badge bg="secondary">{branch.branchCode}</Badge>
                                        </td>
                                        <td>
                                            <strong>{branch.branchName}</strong>
                                        </td>
                                        <td>
                                            <Badge bg="info">{branch.region}</Badge>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{branch.city}, {branch.state}</strong>
                                                <br />
                                                <small className="text-muted">{branch.address}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{branch.managerName}</strong>
                                                <br />
                                                <small className="text-muted">{branch.username}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <small>{branch.mobileNumber}</small>
                                                <br />
                                                <small>{branch.email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <Badge bg="primary">{branch.userCount}</Badge>
                                        </td>
                                        <td>
                                            <strong className="text-success">{branch.revenue}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                {branch.services?.slice(0, 2).map((service, idx) => (
                                                    <Badge key={idx} bg="outline-secondary" className="me-1 mb-1">
                                                        {service}
                                                    </Badge>
                                                ))}
                                                {branch.services?.length > 2 && (
                                                    <Badge bg="outline-secondary">+{branch.services.length - 2}</Badge>
                                                )}
                                            </div>
                                        </td>
                                        <td>
                                            <Badge bg={branch.status === 'Active' ? 'success' : 'danger'}>
                                                {branch.status}
                                            </Badge>
                                        </td>
                                        <td>
                                            <small>{branch.lastLogin}</small>
                                        </td>
                                        <td>
                                            <Dropdown>
                                                <Dropdown.Toggle variant="outline-secondary" size="sm">
                                                    <i className="fas fa-ellipsis-v"></i>
                                                </Dropdown.Toggle>
                                                <Dropdown.Menu>
                                                    <Dropdown.Item onClick={() => handleEdit(branch)}>
                                                        <i className="fas fa-edit me-2"></i>Edit
                                                    </Dropdown.Item>
                                                    <Dropdown.Item onClick={() => handleResetPassword(branch)}>
                                                        <i className="fas fa-key me-2"></i>Reset Password
                                                    </Dropdown.Item>
                                                    <Dropdown.Divider />
                                                    <Dropdown.Item 
                                                        onClick={() => handleDelete(branch.id)}
                                                        className="text-danger"
                                                    >
                                                        <i className="fas fa-trash me-2"></i>Delete
                                                    </Dropdown.Item>
                                                </Dropdown.Menu>
                                            </Dropdown>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                </Card.Body>
            </Card>

            {/* Modal for Create/Edit */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>
                        {modalType === 'create' ? 'Create Branch Office' : 
                         modalType === 'edit' ? 'Edit Branch Office' : 'Reset Password'}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {modalType === 'resetPassword' ? (
                        <div className="text-center py-4">
                            <i className="fas fa-key fa-3x text-warning mb-3"></i>
                            <h5>Reset Password for {editingUser?.managerName}</h5>
                            <p className="text-muted">A new password will be generated and sent to the manager's email.</p>
                            <div className="d-flex justify-content-center gap-2">
                                <Button variant="secondary" onClick={() => setShowModal(false)}>
                                    Cancel
                                </Button>
                                <Button variant="warning">
                                    Reset Password
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <Form onSubmit={handleSubmit}>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Branch Code *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="branchCode"
                                            value={formData.branchCode}
                                            onChange={handleInputChange}
                                            placeholder="Enter branch code"
                                            required
                                            readOnly={modalType === 'create'}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Branch Name *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="branchName"
                                            value={formData.branchName}
                                            onChange={handleInputChange}
                                            placeholder="Enter branch name"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Region *</Form.Label>
                                        <Form.Select
                                            name="region"
                                            value={formData.region}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select Region</option>
                                            {regions.map(region => (
                                                <option key={region.id} value={region.name}>
                                                    {region.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Country *</Form.Label>
                                        <Form.Select
                                            name="country"
                                            value={formData.country}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select Country</option>
                                            {countries.map(country => (
                                                <option key={country.id} value={country.id}>
                                                    {country.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>State *</Form.Label>
                                        <Form.Select
                                            name="state"
                                            value={formData.state}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select State</option>
                                            {states.map(state => (
                                                <option key={state.id} value={state.id}>
                                                    {state.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>City *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="city"
                                            value={formData.city}
                                            onChange={handleInputChange}
                                            placeholder="Enter city"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Form.Group className="mb-3">
                                <Form.Label>Address *</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="address"
                                    value={formData.address}
                                    onChange={handleInputChange}
                                    placeholder="Enter complete address"
                                    required
                                />
                            </Form.Group>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Manager Name *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="managerName"
                                            value={formData.managerName}
                                            onChange={handleInputChange}
                                            placeholder="Enter manager name"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Mobile Number *</Form.Label>
                                        <Form.Control
                                            type="tel"
                                            name="mobileNumber"
                                            value={formData.mobileNumber}
                                            onChange={handleInputChange}
                                            placeholder="Enter mobile number"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Email *</Form.Label>
                                        <Form.Control
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            placeholder="Enter email"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Username *</Form.Label>
                                        <Form.Control
                                            type="text"
                                            name="username"
                                            value={formData.username}
                                            onChange={handleInputChange}
                                            placeholder="Enter username"
                                            required
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            {modalType === 'create' && (
                                <Row>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Password *</Form.Label>
                                            <Form.Control
                                                type="password"
                                                name="password"
                                                value={formData.password}
                                                onChange={handleInputChange}
                                                placeholder="Enter password"
                                                required
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Confirm Password *</Form.Label>
                                            <Form.Control
                                                type="password"
                                                name="confirmPassword"
                                                value={formData.confirmPassword}
                                                onChange={handleInputChange}
                                                placeholder="Confirm password"
                                                required
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                            )}
                            <Row>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Status</Form.Label>
                                        <Form.Select
                                            name="status"
                                            value={formData.status}
                                            onChange={handleInputChange}
                                        >
                                            <option value="Active">Active</option>
                                            <option value="Inactive">Inactive</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Services Offered</Form.Label>
                                        <div className="mt-2">
                                            {availableServices.map(service => (
                                                <Form.Check
                                                    key={service}
                                                    type="checkbox"
                                                    name="services"
                                                    value={service}
                                                    label={service}
                                                    checked={formData.services.includes(service)}
                                                    onChange={handleInputChange}
                                                    className="mb-1"
                                                />
                                            ))}
                                        </div>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Form>
                    )}
                </Modal.Body>
                {modalType !== 'resetPassword' && (
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" onClick={handleSubmit} disabled={loading}>
                            {loading ? 'Processing...' : (modalType === 'create' ? 'Create' : 'Update')}
                        </Button>
                    </Modal.Footer>
                )}
            </Modal>
        </Container>
    );
};

export default BranchOfficeManagement;
