-- Migration: Create my_branch_offices table
-- Description: Stores branch office information under regional offices
-- Created: 2024-01-18

CREATE TABLE my_branch_offices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    regional_office_id INT NOT NULL,
    branch_name VARCHAR(255) NOT NULL,
    branch_code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(255),
    services_offered JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (regional_office_id) REFERENCES my_regional_offices(id) ON DELETE CASCADE
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_branch_offices_code ON my_branch_offices(branch_code);
CREATE INDEX idx_branch_offices_regional ON my_branch_offices(regional_office_id);
CREATE INDEX idx_branch_offices_active ON my_branch_offices(is_active);
CREATE INDEX idx_branch_offices_name ON my_branch_offices(branch_name);
