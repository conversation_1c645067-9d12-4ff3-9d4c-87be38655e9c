import { createContext, useContext, useState, useEffect } from 'react';
import authAPI from '../api/authAPI';

// import schemaBasedDatabaseService from '../services/schemaBasedDatabaseService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [dbConnected, setDbConnected] = useState(false);
  const [userPermissions, setUserPermissions] = useState({});
  const [sessionExpiry, setSessionExpiry] = useState(null);

  useEffect(() => {
    const initializeAuth = async () => {
      // Test API connection
      try {
        const response = await fetch('/api/health');
        setDbConnected(response.ok);
      } catch (error) {
        console.log('API not available, using schema-based database service');
        setDbConnected(false);
      }

      // Check if user is logged in from localStorage
      const savedUser = localStorage.getItem('currentUser');
      const authToken = localStorage.getItem('authToken');

      if (savedUser && authToken) {
        const user = JSON.parse(savedUser);

        // Validate token expiry
        if (isTokenValid(authToken)) {
          setCurrentUser(user);
          updateUserPermissions(user.role);
          setSessionExpiry(getTokenExpiry(authToken));
        } else {
          // Token expired, clear session
          logout();
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  // Token validation
  const isTokenValid = (token) => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  };

  const getTokenExpiry = (token) => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return new Date(payload.exp * 1000);
    } catch (error) {
      return null;
    }
  };

  // Update user permissions based on role
  const updateUserPermissions = (role) => {
    const permissions = {
      user_management: authAPI.canAccessFeature(role, 'user_management'),
      advertisement_management: authAPI.canAccessFeature(role, 'advertisement_management'),
      location_management: authAPI.canAccessFeature(role, 'location_management'),
      group_management: authAPI.canAccessFeature(role, 'group_management'),
      role_management: authAPI.canAccessFeature(role, 'role_management'),
      reports: authAPI.canAccessFeature(role, 'reports')
    };
    setUserPermissions(permissions);
  };

  const login = async (username, password) => {
    try {
      const result = await authAPI.login(username, password);

      if (result.success) {
        const user = {
          id: result.user.id,
          username: result.user.username,
          role: result.user.role,
          role_id: result.user.role_id,
          role_description: result.user.role_description,
          name: result.user.name || result.user.username,
          email: result.user.email,
          mobile_number: result.user.mobile_number,
          group_id: result.user.group_id,
          group_name: result.user.group_name,
          last_logged_in: result.user.last_logged_in,
          createdBy: result.user.createdBy
        };

        setCurrentUser(user);
        updateUserPermissions(user.role);

        // Set session expiry
        if (result.token) {
          setSessionExpiry(getTokenExpiry(result.token));
          localStorage.setItem('authToken', result.token);
        }

        localStorage.setItem('currentUser', JSON.stringify(user));

        // Login successful - last login time will be updated by the server

        return { success: true, user };
      }
      return result;
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    }
  };



  const logout = () => {
    setCurrentUser(null);
    setUserPermissions({});
    setSessionExpiry(null);
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    return userPermissions[permission] || false;
  };

  // Check if user has role level access
  const hasRoleAccess = (requiredRole) => {
    if (!currentUser) return false;
    return authAPI.hasPermission(currentUser.role, requiredRole);
  };

  // Get user's role level
  const getUserRoleLevel = () => {
    if (!currentUser) return 0;
    return authAPI.getRoleLevel(currentUser.role);
  };

  const createUser = async (userData) => {
    try {
      // Add creator information
      const userDataWithCreator = {
        ...userData,
        createdBy: currentUser?.id || null
      };

      // Use API endpoint for user creation
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(userDataWithCreator)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create user');
      }

      const result = await response.json();
      return result.user;
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  };

  // Helper function to get role_id from role name
  const getRoleIdFromRole = (role) => {
    const roleMap = {
      'admin': 1,
      'corporate': 2,
      'headoffice': 3,
      'regional': 4,
      'branch': 5,
      'partner': 6,
      'customer': 7,
      'app_admin': 8,
      'USER': 7 // Default for customer registration
    };
    return roleMap[role] || 7;
  };

  const getUsersByCreator = async (creatorId) => {
    try {
      const response = await fetch(`/api/users?createdBy=${creatorId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const result = await response.json();
      return result.users || [];
    } catch (error) {
      console.error('Get users by creator error:', error);
      return [];
    }
  };

  // Additional CRUD operations for Corporate Dashboard
  const updateUser = async (userId, userData, userRole) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update user');
      }

      return await response.json();
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  };

  const deleteUser = async (userId, userRole) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete user');
      }

      return await response.json();
    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  };

  const resetUserPassword = async (userId, newPassword, userRole) => {
    try {
      const response = await fetch(`/api/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({ newPassword })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to reset password');
      }

      return await response.json();
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  };

  const value = {
    currentUser,
    login,
    logout,
    createUser,
    updateUser,
    deleteUser,
    resetUserPassword,
    getUsersByCreator,
    loading,
    dbConnected,
    userPermissions,
    sessionExpiry,
    hasPermission,
    hasRoleAccess,
    getUserRoleLevel
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
