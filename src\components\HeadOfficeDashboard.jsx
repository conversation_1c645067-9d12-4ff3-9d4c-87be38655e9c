import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Modal, Form, Alert, Table, Badge } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import Navbar from './Navbar';

// Import Head Office Dashboard Components
import RegionalOfficeManagement from './head-office/RegionalOfficeManagement';
import BranchOfficeManagement from './head-office/BranchOfficeManagement';
// import HeaderAdsManagement from './head-office/HeaderAdsManagement';
// import AdsPricingManagement from './head-office/AdsPricingManagement';
// import MainPageAdsManagement from './head-office/MainPageAdsManagement';
// import WalletManagement from './head-office/WalletManagement';
// import ContentManagement from './head-office/ContentManagement';
// import ReportsAnalytics from './head-office/ReportsAnalytics';
// import ChangePasswordManagement from './head-office/ChangePasswordManagement';
import NotificationSystem from './NotificationSystem';

import '../styles/HeadOfficeDashboard.css';

const HeadOfficeDashboard = () => {
  const { currentUser, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  // Sidebar menu items for Head Office Dashboard
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    {
      id: 'office-management',
      label: 'Office Management',
      icon: 'fas fa-building',
      hasSubmenu: true,
      submenu: [
        { id: 'regional-office', label: 'Regional Offices' },
        { id: 'branch-office', label: 'Branch Offices' }
      ]
    },
    {
      id: 'ads-management',
      label: 'Advertisement Management',
      icon: 'fas fa-ad',
      hasSubmenu: true,
      submenu: [
        { id: 'header-ads', label: 'Header Ads' },
        { id: 'ads-pricing', label: 'Ads Pricing' },
        { id: 'main-page-ads', label: 'Main Page Ads' }
      ]
    },
    {
      id: 'financial-management',
      label: 'Financial Management',
      icon: 'fas fa-wallet',
      hasSubmenu: true,
      submenu: [
        { id: 'wallet-management', label: 'Wallet Management' },
        { id: 'transactions', label: 'Transactions' },
        { id: 'financial-reports', label: 'Financial Reports' }
      ]
    },
    {
      id: 'content-management',
      label: 'Content Management',
      icon: 'fas fa-edit',
      hasSubmenu: true,
      submenu: [
        { id: 'content-editor', label: 'Content Editor' },
        { id: 'media-library', label: 'Media Library' },
        { id: 'templates', label: 'Templates' }
      ]
    },
    {
      id: 'reports-analytics',
      label: 'Reports & Analytics',
      icon: 'fas fa-chart-bar',
      hasSubmenu: true,
      submenu: [
        { id: 'performance-reports', label: 'Performance Reports' },
        { id: 'user-analytics', label: 'User Analytics' },
        { id: 'revenue-reports', label: 'Revenue Reports' }
      ]
    },
    { id: 'notifications', label: 'Notifications', icon: 'fas fa-bell' },
    { id: 'change-password', label: 'Change Password', icon: 'fas fa-key' }
  ];

  const renderMainContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h4>Head Office Dashboard</h4>
              <div className="d-flex gap-2">
                <Button variant="outline-primary" size="sm">
                  <i className="fas fa-download me-2"></i>Export Report
                </Button>
                <Button variant="primary" size="sm">
                  <i className="fas fa-plus me-2"></i>Quick Action
                </Button>
              </div>
            </div>

            {/* Dashboard Statistics */}
            <Row className="mb-4">
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                    <h3 className="text-primary">8</h3>
                    <p className="mb-0">Regional Offices</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-store fa-2x text-success mb-2"></i>
                    <h3 className="text-success">45</h3>
                    <p className="mb-0">Branch Offices</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-users fa-2x text-info mb-2"></i>
                    <h3 className="text-info">1,234</h3>
                    <p className="mb-0">Active Users</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-wallet fa-2x text-warning mb-2"></i>
                    <h3 className="text-warning">$45,678</h3>
                    <p className="mb-0">Total Revenue</p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Quick Actions and Recent Activities */}
            <Row>
              <Col md={8}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-chart-line me-2"></i>Performance Overview
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="performance-metrics">
                      <Row>
                        <Col md={6}>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>Regional Office Performance</span>
                              <span className="text-success">+12%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-success" style={{width: '85%'}}></div>
                            </div>
                          </div>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>Branch Office Growth</span>
                              <span className="text-info">+8%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-info" style={{width: '72%'}}></div>
                            </div>
                          </div>
                        </Col>
                        <Col md={6}>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>User Engagement</span>
                              <span className="text-primary">+15%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-primary" style={{width: '90%'}}></div>
                            </div>
                          </div>
                          <div className="metric-item mb-3">
                            <div className="d-flex justify-content-between">
                              <span>Revenue Growth</span>
                              <span className="text-warning">+22%</span>
                            </div>
                            <div className="progress mt-1">
                              <div className="progress-bar bg-warning" style={{width: '95%'}}></div>
                            </div>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-tasks me-2"></i>Recent Activities
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="activity-item mb-2">
                      <small className="text-muted">30 minutes ago</small>
                      <div>New regional office registered</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">2 hours ago</small>
                      <div>Advertisement campaign updated</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">4 hours ago</small>
                      <div>Wallet transaction processed</div>
                    </div>
                    <div className="activity-item">
                      <small className="text-muted">1 day ago</small>
                      <div>Content management updated</div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </div>
        );

      // Component cases
      case 'regional-office':
        return <RegionalOfficeManagement />;

      case 'branch-office':
        return <BranchOfficeManagement />;

      case 'header-ads':
        return (
          <div className="text-center py-5">
            <i className="fas fa-ad fa-3x text-muted mb-3"></i>
            <h3>Header Advertisement Management</h3>
            <p className="text-muted">Manage header advertisements with calendar-based booking</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'ads-pricing':
        return (
          <div className="text-center py-5">
            <i className="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
            <h3>Advertisement Pricing Management</h3>
            <p className="text-muted">Configure advertisement pricing and packages</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'main-page-ads':
        return (
          <div className="text-center py-5">
            <i className="fas fa-bullhorn fa-3x text-muted mb-3"></i>
            <h3>Main Page Advertisement Management</h3>
            <p className="text-muted">Manage main page advertisements and campaigns</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'wallet-management':
        return (
          <div className="text-center py-5">
            <i className="fas fa-wallet fa-3x text-muted mb-3"></i>
            <h3>Wallet Management</h3>
            <p className="text-muted">Manage wallet functionality and transactions</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'content-editor':
        return (
          <div className="text-center py-5">
            <i className="fas fa-edit fa-3x text-muted mb-3"></i>
            <h3>Content Management</h3>
            <p className="text-muted">Manage content with Summernote editor integration</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'performance-reports':
        return (
          <div className="text-center py-5">
            <i className="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h3>Reports & Analytics</h3>
            <p className="text-muted">Comprehensive reporting and data visualization</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      case 'notifications':
        return <NotificationSystem />;

      case 'change-password':
        return (
          <div className="text-center py-5">
            <i className="fas fa-key fa-3x text-muted mb-3"></i>
            <h3>Change Password</h3>
            <p className="text-muted">Update your account password</p>
            <Button variant="primary">Coming Soon</Button>
          </div>
        );

      default:
        return (
          <div className="text-center py-5">
            <i className="fas fa-building fa-3x text-muted mb-3"></i>
            <h3>Welcome to Head Office Dashboard</h3>
            <p className="text-muted">Select a menu item to get started</p>
          </div>
        );
    }
  };

  return (
    <div className="head-office-dashboard">
      <Navbar />
      
      <div className="dashboard-container">
        {/* Sidebar */}
        <div className="sidebar">
          <div className="sidebar-header">
            <h5 className="text-white mb-0">
              <i className="fas fa-building me-2"></i>Head Office
            </h5>
            <small className="text-white-50">Management Portal</small>
          </div>
          
          <div className="sidebar-menu">
            {sidebarItems.map((item) => (
              <div key={item.id} className="menu-item">
                {item.hasSubmenu ? (
                  <div className="menu-group">
                    <div className="menu-header">
                      <i className={`${item.icon} me-2`}></i>
                      {item.label}
                    </div>
                    <div className="submenu">
                      {item.submenu.map((subItem) => (
                        <div
                          key={subItem.id}
                          className={`submenu-item ${activeSection === subItem.id ? 'active' : ''}`}
                          onClick={() => setActiveSection(subItem.id)}
                        >
                          {subItem.label}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div
                    className={`menu-link ${activeSection === item.id ? 'active' : ''}`}
                    onClick={() => setActiveSection(item.id)}
                  >
                    <i className={`${item.icon} me-2`}></i>
                    {item.label}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <Container fluid>
            {error && (
              <Alert variant="danger" dismissible onClose={() => setError('')}>
                {error}
              </Alert>
            )}
            {success && (
              <Alert variant="success" dismissible onClose={() => setSuccess('')}>
                {success}
              </Alert>
            )}
            
            {renderMainContent()}
          </Container>
        </div>
      </div>
    </div>
  );
};

export default HeadOfficeDashboard;
